@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Flash Message Animations */
  @keyframes flash-slide-in {
    from {
      opacity: 0;
      transform: translateY(-100%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes flash-slide-out {
    from {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-100%) scale(0.95);
    }
  }

  @keyframes flash-pulse {
    0%, 100% {
      box-shadow: 0 4px 6px -1px hsl(var(--semantic-gray-900) / 0.1), 0 2px 4px -1px hsl(var(--semantic-gray-900) / 0.06);
    }
    50% {
      box-shadow: 0 10px 15px -3px hsl(var(--semantic-gray-900) / 0.1), 0 4px 6px -2px hsl(var(--semantic-gray-900) / 0.05);
    }
  }

  /* Flash Message Container */
  .flash-message-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    pointer-events: none;
  }

  .flash-message-container > * {
    pointer-events: auto;
  }

  /* Alert Box Enhancements */
  .flash-alert {
    animation: flash-slide-in 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  .flash-alert.flash-alert-exit {
    animation: flash-slide-out 0.3s cubic-bezier(0.4, 0, 1, 1);
  }

  .flash-alert:hover {
    animation: flash-pulse 2s ease-in-out infinite;
  }

  /* Banner Enhancements - Glassy effect */
  div.flash-banner {
    animation: flash-slide-in 0.5s cubic-bezier(0.16, 1, 0.3, 1) !important;
    backdrop-filter: blur(16px) saturate(180%) !important;
    -webkit-backdrop-filter: blur(16px) saturate(180%) !important;
    position: fixed !important;
    top: 1rem !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 9999 !important;
    max-width: 64rem !important;
    width: calc(100% - 2rem) !important;
    margin: 0 !important;
    border: 1px solid hsl(16 75% 62% / 0.5) !important;
    box-shadow:
      0 8px 32px -8px hsl(16 75% 62% / 0.25),
      0 0 0 1px hsl(16 75% 62% / 0.2) inset !important;
  }

  .flash-banner.flash-banner-exit {
    animation: flash-slide-out 0.4s cubic-bezier(0.4, 0, 1, 1);
  }

  /* Responsive Design */
  @media (max-width: 640px) {
    .flash-message-container {
      top: 1rem;
      right: 1rem;
      left: 1rem;
    }

    .flash-alert {
      max-width: none !important;
      min-width: auto !important;
      width: 100% !important;
    }

    div.flash-banner {
      max-width: none !important;
      width: calc(100% - 1rem) !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      top: 0.5rem !important;
    }
  }



  /* Light theme specific styling */
  .flash-alert {
    border: 1px solid hsl(var(--border));
  }

  .flash-modal-backdrop {
    background: hsl(var(--semantic-gray-900) / 0.15);
  }

  /* Light theme alert variants */
  .flash-alert-success {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(142 71% 45% / 0.08) 100%);
    border-color: hsl(142 71% 45% / 0.25);
    box-shadow: 0 4px 12px -2px hsl(142 71% 45% / 0.15);
  }

  .flash-alert-danger,
  .flash-alert-error {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(0 84% 60% / 0.08) 100%);
    border-color: hsl(0 84% 60% / 0.25);
    box-shadow: 0 4px 12px -2px hsl(0 84% 60% / 0.15);
  }

  .flash-alert-info,
  .flash-alert-default {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--primary) / 0.08) 100%);
    border-color: hsl(var(--primary) / 0.25);
    box-shadow: 0 4px 12px -2px hsl(var(--primary) / 0.15);
  }

  .flash-alert-warning {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(45 93% 47% / 0.08) 100%);
    border-color: hsl(45 93% 47% / 0.25);
    box-shadow: 0 4px 12px -2px hsl(45 93% 47% / 0.15);
  }

  /* Light theme banner variants - More orange glassy effect */
  .flash-banner-alert {
    background:
      linear-gradient(135deg,
        hsl(16 75% 62% / 0.4) 0%,
        hsl(16 75% 62% / 0.6) 100%),
      linear-gradient(135deg,
        hsl(0 84% 60% / 0.7) 0%,
        hsl(0 84% 55% / 0.8) 100%);
    box-shadow: 0 8px 25px -5px hsl(16 75% 62% / 0.3);
    border-color: hsl(16 75% 62% / 0.5);
    color: white;
  }

  .flash-banner-maintenance {
    background:
      linear-gradient(135deg,
        hsl(16 75% 62% / 0.5) 0%,
        hsl(16 75% 62% / 0.7) 100%),
      linear-gradient(135deg,
        hsl(var(--primary) / 0.8) 0%,
        hsl(var(--primary-600) / 0.9) 100%);
    box-shadow: 0 8px 25px -5px hsl(16 75% 62% / 0.3);
    border-color: hsl(16 75% 62% / 0.5);
    color: white;
  }

  .flash-banner-announcement {
    background:
      linear-gradient(135deg,
        hsl(16 75% 62% / 0.4) 0%,
        hsl(16 75% 62% / 0.6) 100%),
      linear-gradient(135deg,
        hsl(142 71% 45% / 0.7) 0%,
        hsl(142 71% 40% / 0.8) 100%);
    box-shadow: 0 8px 25px -5px hsl(16 75% 62% / 0.3);
    border-color: hsl(16 75% 62% / 0.5);
    color: white;
  }

  /* Dark theme adjustments */
  .dark .flash-alert {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-color: hsl(var(--border));
  }

  .dark div.flash-banner {
    backdrop-filter: blur(20px) saturate(180%) !important;
    -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
    border-color: hsl(16 75% 62% / 0.2) !important;
    box-shadow:
      0 8px 32px -8px hsl(16 75% 62% / 0.25),
      0 0 0 1px hsl(16 75% 62% / 0.15) inset !important;
  }

  .dark .flash-modal-backdrop {
    background: hsl(var(--semantic-gray-900) / 0.35);
  }

  /* Dark theme alert variants */
  .dark .flash-alert-success {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(142 71% 45% / 0.12) 100%);
    border-color: hsl(142 71% 45% / 0.3);
    box-shadow: 0 4px 12px -2px hsl(142 71% 45% / 0.2);
  }

  .dark .flash-alert-danger,
  .dark .flash-alert-error {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(0 84% 60% / 0.12) 100%);
    border-color: hsl(0 84% 60% / 0.3);
    box-shadow: 0 4px 12px -2px hsl(0 84% 60% / 0.2);
  }

  .dark .flash-alert-info,
  .dark .flash-alert-default {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--primary) / 0.12) 100%);
    border-color: hsl(var(--primary) / 0.3);
    box-shadow: 0 4px 12px -2px hsl(var(--primary) / 0.2);
  }

  .dark .flash-alert-warning {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(45 93% 47% / 0.12) 100%);
    border-color: hsl(45 93% 47% / 0.3);
    box-shadow: 0 4px 12px -2px hsl(45 93% 47% / 0.2);
  }

  /* Dark theme banner variants - enhanced shadows */
  .dark .flash-banner-alert {
    box-shadow: 0 8px 25px -5px hsl(0 84% 60% / 0.4);
    color: white;
  }

  .dark .flash-banner-maintenance {
    box-shadow: 0 8px 25px -5px hsl(var(--primary) / 0.4);
    color: white;
  }

  .dark .flash-banner-announcement {
    box-shadow: 0 8px 25px -5px hsl(142 71% 45% / 0.4);
    color: white;
  }

  /* Accessibility enhancements */
  @media (prefers-reduced-motion: reduce) {
    .flash-alert,
    .flash-banner {
      animation: none;
    }

    .flash-alert:hover {
      animation: none;
    }
  }

  /* Focus states */
  .flash-alert button:focus,
  .flash-banner button:focus {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .flash-alert,
    .flash-banner {
      border-width: 2px;
    }
  }
}
