@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {

  .locked-tab {
    @apply opacity-70 hover:opacity-85 transition-all duration-300;
    position: relative;
    overflow: hidden;
  }

  .locked-tab:hover {
    box-shadow: 0 0 0 1px hsl(var(--semantic-gray-300));
  }

  .locked-tab::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, hsl(var(--background) / 0.05), hsl(var(--semantic-gray-200) / 0.3));
    pointer-events: none;
  }

  .locked-tab-content {
    @apply relative;
  }

  .locked-tab-overlay {
    @apply absolute inset-0 z-0;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background: linear-gradient(to right, hsl(var(--background) / 0.15), hsl(var(--semantic-gray-200) / 0.2));
  }

  .locked-tab-text {
    @apply relative z-10 opacity-75;
    text-shadow: 0 0 1px hsl(var(--semantic-gray-900) / 0.1);
  }
}
