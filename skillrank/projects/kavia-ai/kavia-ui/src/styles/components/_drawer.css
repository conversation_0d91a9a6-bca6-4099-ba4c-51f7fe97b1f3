.drawer-content {
  @apply bg-custom-bg-primary shadow-xl absolute flex flex-col;

  &.vertical {
    @apply h-full;
  }

  &.horizontal {
    @apply w-full;
  }
}

.drawer-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-custom-border;
}

.drawer-body {
  @apply h-full overflow-hidden;
}

.drawer-footer {
  @apply flex items-center justify-between py-4 px-6 border-t border-custom-border;
}

  .drawer-open {
    &.drawer-lock-scroll {
      @apply overflow-hidden;
    }
  }

  .drawer-overlay {
    transition: all 0.3s ease-in-out;
    @apply inset-0 fixed z-30;
    background-color: hsl(var(--semantic-gray-900) / 0.12);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    left: 4rem;
  }

  .drawer-overlay-after-open {
    opacity: 1;
    left: 4rem;
  }

  .drawer-overlay-before-close {
    opacity: 0;
  }
