/* work item tab content alignment starts */
.workItemMainDiv{
    @apply w-full
}
.workItemTopPanel {
    @apply sticky top-0 left-0 bg-custom-bg-primary
}
.loadingDivWrapper {
    @apply flex justify-between w-full
}
.searchDiv {
    @apply mt-2
}
.buttonDiv {
    @apply flex gap-3 mt-2 items-center
}
.mainDiv {
  @apply overflow-auto max-h-[75vh]
}
.mainDivLoading{
    @apply mt-5
}
.stateViewModal {
    @apply flex justify-center items-center text-center
}
/* work item tab alignment end */
/* work item detail page alignment start */