"use client";

import { Bell, Trash2 } from "lucide-react";
import { useNotifications } from "@/components/Context/NotificationProvider";

export const NotificationDisplay = () => {
  const { notifications, setNotifications } = useNotifications();

  

  const handleDelete = (index) => {
    const updatedNotifications = notifications.filter((_, i) => i !== index);
    setNotifications(updatedNotifications);
  };

  return (
    <div className="mx-auto bg-custom-bg-primary rounded-xl shadow-sm border border-custom-border overflow-hidden">
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="w-6 h-6 text-custom-text-primary" />
            <h2 className="typography-heading-4 font-weight-bold text-custom-text-primary">Notifications</h2>
          </div>
          <span className="px-2.5 py-1 bg-custom-bg-muted text-custom-text-primary typography-body-sm font-weight-medium rounded-full">
            {notifications.length}
          </span>
        </div>
      </div>

      {/* Notification List */}
      <div className="divide-y divide-custom-border">
        {notifications.map((notification, index) => {
          const title = notification?.data?.["pinpoint.notification.title"] || "No Title";
          const body = notification?.data?.["pinpoint.notification.body"] || "No Body";

          return (
            <div
              key={index}
              className="group hover:bg-custom-bg-secondary transition-colors duration-200"
            >
              <div className="p-4 border-t border-custom-border">
                <div className="flex items-start justify-between">
                  {/* Notification Content */}
                  <div className="flex-1 min-w-0">
                    <h3 className="typography-body-sm font-weight-semibold text-custom-text-primary mb-1">{title}</h3>
                    <p className="typography-body-sm text-custom-text-secondary line-clamp-2">{body}</p>
                  </div>

                  {/* Action Buttons */}
                  <div className="ml-4 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <button
                      onClick={() => handleDelete(index)}
                      className="p-1 hover:bg-custom-bg-muted rounded-full text-custom-text-destructive transition-colors duration-200"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Empty State */}
        {notifications.length === 0 && (
          <div className="py-12">
            <div className="flex flex-col items-center justify-center text-center">
              <Bell className="w-12 h-12 text-custom-text-secondary mb-4" />
              <h3 className="typography-body-lg font-weight-medium text-custom-text-primary mb-2">
                No notifications yet
              </h3>
              <p className="typography-body-sm text-custom-text-secondary max-w-sm">
                When you receive notifications, they&apos;ll show up here.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
