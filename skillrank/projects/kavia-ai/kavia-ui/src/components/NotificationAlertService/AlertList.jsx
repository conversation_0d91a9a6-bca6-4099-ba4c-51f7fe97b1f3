"use client";
import { useState, createContext, useContext } from "react";
import Alert from "./Alert";

const AlertContext = createContext({
  alerts: [],
  showAlert: (message, type) => { },
  closeAlert: () => { },
});

export const AlertProvider = ({ children }) => {
  const [alerts, setAlerts] = useState([]);

  const showAlert = (message, type = "success") => {
    
    setAlerts((prevAlerts) => [
      ...prevAlerts,
      { message, type, id: Date.now() },
    ]);
  };

  const closeAlert = (id) => {
    setAlerts((prevAlerts) => prevAlerts.filter((alert) => alert.id !== id));
  };

  return (
    <AlertContext.Provider value={{ alerts, showAlert, closeAlert }}>
      {children}
    </AlertContext.Provider>
  );
};

function AlertList() {
  const { alerts, closeAlert } = useContext(AlertContext);

  return (
    <div className="toaster-container">
      {alerts.map((alert) => (
        <Alert key={alert.id} {...alert} onClose={() => closeAlert(alert.id)} />
      ))}
    </div>
  );
}

export default AlertList;
export { AlertContext };