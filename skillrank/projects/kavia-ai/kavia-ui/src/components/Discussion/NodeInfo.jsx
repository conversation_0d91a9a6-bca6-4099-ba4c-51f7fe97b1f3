import React from "react";
import dynamic from "next/dynamic";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import Badge from "@/components/UIComponents/Badge/Badge";
import Editor from '@monaco-editor/react';
import { renderHTML } from '@/utils/helpers';

const NoSSR = dynamic(() => import("../Chart/MermaidChart"), {
  ssr: false,
});

const NodeBadge = ({ type }) => {
  if (!type) return null;
  return <Badge className="bg-primary-100 text-primary-800" type={type} />;
};

const NodeHeader = ({ title, type }) => (
  <div className="flex items-center gap-2">
    <h4 className="mb-1 project-panel-heading">{title}</h4>
    <NodeBadge type={type} />
  </div>
);

const processBoldText = (text) => {
  if (typeof text !== "string") return text;

  text = text.replace(/#+\s*(.*)/g, `<h1 class="typography-body-sm font-weight-bold">$1</h1>`)

  text = text.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");

  text = text.replace(/`(.*?)`/g, "<code>$1</code>");

  return text;
};

const formatListContent = (content) => {
  if (!content) return null;

  // Simplified - use renderHTML for proper markdown formatting
  if (typeof content === "string") {
    return (
      <div
        className="text-gray-600"
        dangerouslySetInnerHTML={{ __html: renderHTML(content) }}
      />
    );
  } else {
    // If content is not a string, return as is
    return (
      <div className="text-gray-600">
        {content}
      </div>
    );
  }
};

const formatDefaultListContent = (content) => {
  if (!content) return null;

  // Simplified - use renderHTML for proper markdown formatting
  if (typeof content === "string") {
    return (
      <div
        className="text-gray-600"
        dangerouslySetInnerHTML={{ __html: renderHTML(content) }}
      />
    );
  }

  // For regular text, fallback to processBoldText
  return (
    <div
      className="text-gray-600"
      dangerouslySetInnerHTML={{ __html: processBoldText(content) }}
    />
  );
};

const renderField = (key, value, uiMetaData) => {
  if (!value) return null;

  const cleanJsonString = (str) => {
    try {
      // Remove <JSON> tags and decode Unicode characters
      const cleanStr = str
        .replace(/\\u003cJSON\\u003e|<JSON>/, '')
        .replace(/\\u003c\/JSON\\u003e|<\/JSON>/, '')
        .replaceAll('<JSON>', '') // Plain <JSON>
        .replaceAll('</JSON>', '')
        .replace(/\\n/g, '\n')
        .replace(/\\"/g, '"');

      // Parse and stringify to format it properly
      const parsed = JSON.parse(cleanStr);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {

      return str;
    }
  };

  // List of keys that should use Monaco Editor with their corresponding languages
const monacoEditorConfig = {
  'main_tf': 'hcl',
  'outputs_tf': 'hcl',
  'providers_tf': 'hcl',
  'variables_tf': 'hcl',
  'workflow_file': 'yaml',
  'docker_file': 'dockerfile',
  'build_spec': 'yaml',  // Adding this for build spec content
  'build_settings': 'yaml'  // Adding this if you have build settings
};

const isJsonContent = (str) => {
  // Handle null, undefined, or non-string inputs
  if (!str || typeof str !== 'string') {
    return false;
  }

  try {
    return (
      (str.includes('"openapi"') || str.includes('\\"openapi\\"')) ||
      (str.includes('<JSON>') || str.includes('\\u003cJSON\\u003e'))
    );
  } catch (error) {
    // Handle any unexpected errors during string operations

    return false;
  }
};


// Determine if we should use Monaco Editor
const shouldUseMonaco = key.endsWith('_tf') ||
  monacoEditorConfig[key] ||
  isJsonContent(value);

if (shouldUseMonaco) {
  const language = isJsonContent(value) ? 'json' : (monacoEditorConfig[key] || 'hcl');
  const processedValue = isJsonContent(value) ? cleanJsonString(value) : value;


  return (
    <div key={key} className="space-y-3">
      <h3 className="typography-body font-weight-bold text-gray-800">
        {key}
      </h3>
      <div className="pl-4">
        <Editor
          height="400px"  // Increased height for better JSON visibility
          defaultLanguage={language}
          defaultValue={processedValue}
          theme="vs-dark"
          options={{
            readOnly: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: "on",
            automaticLayout: true,
            fontSize: 'var(--font-size-code)',
            formatOnPaste: true,
            formatOnType: true,
            wordWrap: "on"
          }}
        />
      </div>
    </div>
  );
}


  const flattenMetadata = (metadata) => {
    const flattened = {};

    // Iterate through each top-level key (Design, ClassDiagram, Diagram, etc.)
    Object.values(metadata).forEach((section) => {
      // Merge each section's fields into the flattened object
      Object.entries(section).forEach(([key, value]) => {
        // Only add if the key doesn't already exist (first occurrence takes precedence)
        if (!flattened[key]) {
          flattened[key] = value;
        }
      });
    });

    return flattened;
  };

  const flattenedMetadata = flattenMetadata(uiMetaData);

  if (flattenedMetadata[key]?.display_type === 'mermaid_chart') {
    return (
      <div key={key} className="space-y-3">
        <h3 className="typography-body font-weight-bold text-gray-800">
          {flattenedMetadata[key].Label || (
            key.charAt(0).toUpperCase() +
            key
              .slice(1)
              .replace(/([A-Z])/g, " $1")
              .trim()
          )}
        </h3>
        <div className="pl-4">
          <NoSSR chartDefinition={value} />
        </div>
      </div>
    );
  }

  // Handle rich text display type
  if (flattenedMetadata[key]?.display_type === 'rich_text') {
    return (
      <div key={key} className="space-y-3">
        <h3 className="typography-body font-weight-bold text-gray-800">
          {flattenedMetadata[key].Label || (
            key.charAt(0).toUpperCase() +
            key
              .slice(1)
              .replace(/([A-Z])/g, " $1")
              .trim()
          )}
        </h3>
        <div className="pl-4">{formatListContent(value)}</div>
      </div>
    );
  }

  // Default rendering for other types
  return (
    <div key={key} className={`${key.includes("state")? "flex items-center gap-3 mt-3" : "space-y-3"}`}>
      <h3 className = "typography-body font-weight-bold text-gray-800">
        {flattenedMetadata[key]?.Label || (
          key.charAt(0).toUpperCase() +
          key
            .slice(1)
            .replace(/([A-Z])/g, " $1")
            .trim()
        )}
      </h3>
      {key.includes("state")? (
        <Badge className="bg-primary-100 text-primary-800" type={value} />
      ): (
        <div className="pl-4">{formatDefaultListContent(value)}</div>
      )}
    </div>
  );
};

const renderNodeContent = (node, uiMetaData) => {
  if (!node) return null;

  // Fields to exclude from rendering
  const excludeFields = ["id", "Title", "Type", "__typename"];

  return (
    <div className="space-y-6">
      {Object.entries(node)
        .filter(([key]) => !excludeFields.includes(key))
        .map(([key, value]) => renderField(key, value, uiMetaData))}
    </div>
  );
};

const renderChildNode = (node, badge, uiMetaData) => {
  if (!node) return null;

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
      <div className="flex items-center gap-2">
        <h6 className="font-weight-medium text-gray-700">
          {node.Title || "Untitled Node"}
        </h6>
        {badge}
      </div>
      {renderNodeContent(node, uiMetaData)}
    </div>
  );
};

const NodeInfo = React.memo(function NodeInfo({ nodeInfo, title = "Available Information" }) {
  if (!nodeInfo?.node) return null;

  const { node, relationships, ui_metadata } = nodeInfo;
  const renderRelationships = (relationships) => {
    if (!relationships || Object.keys(relationships).length === 0) return null;

    return Object.entries(relationships).map(([relType, relNodes]) => {
      if (!Array.isArray(relNodes) || relNodes.length === 0) return null;

      return (
        <div key={relType} className="space-y-4">
          <div className="space-y-4">
            {relNodes.map((rel, index) => (
              <div key={`${relType}-${rel.node?.id || rel.node?.Title || index}`}>
                {renderChildNode(
                  rel.node,
                  rel.node?.Type && <NodeBadge type={rel.node.Type} />,
                  ui_metadata
                )}
              </div>
            ))}
          </div>
        </div>
      );
    });
  };

  return (
    <div className="space-y-4">
      <Accordion
        title={title}
        defaultOpen={true}
        className="bg-white shadow-sm rounded-lg"
      >
        <div className="  border-gray-200">
          <NodeHeader title={node.Title} type={node.Type} />
          {renderNodeContent(node, ui_metadata)}
        </div>
      </Accordion>

      {relationships &&
        Object.keys(relationships).filter((key) => key !== "hasChild").length >
        0 && (
          <Accordion
            title="Related Nodes"
            defaultOpen={false}
            className="bg-white shadow-sm rounded-lg"
          >
            <div className=" border-t border-gray-200">
              {renderRelationships(
                Object.fromEntries(
                  Object.entries(relationships).filter(
                    ([key]) => key !== "hasChild"
                  )
                )
              )}
            </div>
          </Accordion>
        )}

      {relationships?.hasChild && relationships.hasChild.length > 0 && (
        <Accordion
          title="Child Nodes"
          defaultOpen={false}
          className="bg-white shadow-sm rounded-lg"
        >
          <div className=" border-gray-200">
            {renderRelationships({ hasChild: relationships.hasChild })}
          </div>
        </Accordion>
      )}
    </div>
  );
});

export default NodeInfo;
