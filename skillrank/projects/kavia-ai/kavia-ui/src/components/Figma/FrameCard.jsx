import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Download, Edit, MoreVertical, Trash2 } from 'lucide-react';

const CardGrid = ({ cards, onCardClick, loadingCardId, onRename, onDelete, onDownload, type }) => {
  const [activeDropdown, setActiveDropdown] = useState(null);
  const dropdownRefs = useRef({});  // Changed to store multiple refs

  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if any dropdown is active and if click is outside of its menu
      if (activeDropdown !== null) {
        const currentRef = dropdownRefs.current[activeDropdown];
        if (currentRef && !currentRef.contains(event.target)) {
          setActiveDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);  // Added activeDropdown as dependency

  const handleOptionsClick = (e, index) => {
    e.preventDefault();  // Changed from stopPropagation
    e.stopPropagation();
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  const handleRename = (e, card) => {
    e.preventDefault();  // Added preventDefault
    e.stopPropagation();
    onRename(card);
    setActiveDropdown(null);
  };

  const handleDelete = (e, card) => {
    e.preventDefault();
    e.stopPropagation();
    onDelete(card);
    setActiveDropdown(null);
  };

  const handleDownload = (e, card) => {
    e.preventDefault();  // Added preventDefault
    e.stopPropagation();
    onDownload(card);
    setActiveDropdown(null);
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {cards.map((card, index) => (
        <div
          key={index}
          className="relative bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => onCardClick(card)}
        >
          {type === 'image' && (
            <div 
              className="absolute top-2 right-2 z-10" 
              ref={el => dropdownRefs.current[index] = el}  // Assign ref for this dropdown
            >
              <button
                className="p-1 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 transition-opacity"
                onClick={(e) => handleOptionsClick(e, index)}
              >
                <MoreVertical className="w-5 h-5 text-white" />
              </button>
              {activeDropdown === index && (
                <div className="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                  <button
                    className="w-full flex items-center px-4 py-2 typography-body-sm text-gray-700 hover:bg-gray-100"
                    onClick={(e) => handleRename(e, card)}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    <span>Rename</span>
                  </button>
                  <button
                    className="w-full flex items-center px-4 py-2 typography-body-sm text-gray-700 hover:bg-gray-100"
                    onClick={(e) => handleDownload(e, card)}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    <span>Download</span>
                  </button>
                  <button
                    className="w-full flex items-center px-4 py-2 typography-body-sm text-red-600 hover:bg-gray-100"
                    onClick={(e) => handleDelete(e, card)}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    <span>Delete</span>
                  </button>
                </div>
              )}
            </div>
          )}
          <div className="aspect-video relative">
            {loadingCardId === card.id ? (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
              </div>
            ) : (
              <div className="relative w-full h-full">
                <Image
                  src={card.image}
                  alt={card.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity flex items-center justify-center">
                  <div className="opacity-0 hover:opacity-100 transition-opacity">
                    <span className="text-white bg-black bg-opacity-50 px-3 py-1 rounded-full typography-body-sm">
                      Click to view
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="p-4">
            <h3 className="font-weight-medium text-gray-900">{card.title}</h3>
            <p className="typography-body-sm text-gray-500 mt-1">
              {card.dimensions.width} x {card.dimensions.height}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CardGrid;