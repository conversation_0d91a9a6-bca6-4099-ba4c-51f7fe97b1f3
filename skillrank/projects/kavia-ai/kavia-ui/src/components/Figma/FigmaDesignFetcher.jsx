// FigmaDesignFetcher.jsx
"use client";
import { useState, useEffect, useContext, } from "react";
import Loader from "./Loader";
import TableComponent from "@/components/SimpleTable/table";
import ImageLightbox from "@/components/Figma/ImageLightbox";
import CardGrid from "./FrameCard";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { getFigmaExtImage, getFigmaFileData, getFigmaFramesProgressive } from "@/api/figma";
import en from '@/en.json';
import EnParser from "@/utils/enParser";
import ErrorView from "../Modal/ErrorViewModal";
import {
  Group
} from 'lucide-react';
import { useParams } from "next/navigation";
import { FigmaExtractionContext } from "../Context/FigmaExtractionContext";

export default function FigmaDesignFetcher({
  figmaId,
  figmaLink,
  viewMode,
  setViewMode,
  isUpdating,
  isDownloading,
  handleUpdateFigmaDesign,
  handleDownloadAllFrames,
  selectedDesign,
  designType,
  onRename,  // Add this prop
  onDelete,  // Add this prop
}) {
  const { selectedDesignData, setSelectedDesignData } = useContext(FigmaExtractionContext);
  const [figmaData, setFigmaData] = useState(null);
  const [frames, setFrames] = useState([]);
  const [error, setError] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [isFetchingMainData, setIsFetchingMainData] = useState(false);
  const [fetchingScreenId, setFetchingScreenId] = useState(null);
  const [isProgressiveLoading, setIsProgressiveLoading] = useState(false);
  const [progressiveStatus, setProgressiveStatus] = useState(null);
  const { showAlert } = useContext(AlertContext)
  const params = useParams();
  const projectId = params.projectId;

  const fetchProgressiveFrames = async () => {
    if (designType !== 'figma') {
      console.log('🔍 [Progressive] Skipping - not a figma design, designType:', designType);
      return;
    }

    console.log('🔍 [Progressive] Starting fetchProgressiveFrames for figmaId:', figmaId, 'projectId:', projectId);

    try {
      console.log('🔍 [Progressive] Calling getFigmaFramesProgressive API...');
      const response = await getFigmaFramesProgressive(figmaId, projectId);

      console.log('🔍 [Progressive] API Response:', response);
      console.log('🔍 [Progressive] Frames received:', response.frames?.length || 0);
      console.log('🔍 [Progressive] Status:', response.status);
      console.log('🔍 [Progressive] Progress:', response.completed_frames, '/', response.total_frames);

      setFrames(response.frames || []);
      setProgressiveStatus({
        status: response.status,
        total_frames: response.total_frames,
        completed_frames: response.completed_frames,
        failed_frames: response.failed_frames
      });

      // Update figma data structure
      const data = { frames: response.frames || [] };
      setFigmaData(data);
      setSelectedDesignData(data);

      console.log('🔍 [Progressive] Updated frames state with', response.frames?.length || 0, 'frames');

      // If processing is complete, stop progressive loading
      if (response.status === 'completed' || response.status === 'failed') {
        console.log('🔍 [Progressive] Processing complete, stopping progressive loading');
        setIsProgressiveLoading(false);
      }

    } catch (err) {
      console.error('❌ [Progressive] Error fetching progressive frames:', err);
      console.error('❌ [Progressive] Error details:', err.message);
      console.error('❌ [Progressive] Error stack:', err.stack);
      // Don't show error for progressive loading failures, just continue
    }
  };

  const fetchFigmaData = async () => {
    console.log('🔍 [Main] Starting fetchFigmaData for figmaId:', figmaId);
    console.log('🔍 [Main] designType:', designType);
    console.log('🔍 [Main] selectedDesign:', selectedDesign);
    console.log('🔍 [Main] selectedDesign status:', selectedDesign?.status);

    setIsFetchingMainData(true);
    setError(null);

    try {
      let data = null;

      if (designType === 'figma') {
        // First try to get progressive frames if design is processing
        if (selectedDesign?.status === 'processing' || selectedDesign?.status === 'pending') {
          console.log('🔍 [Main] Design is processing/pending, switching to progressive loading');
          setIsProgressiveLoading(true);
          await fetchProgressiveFrames();
          setIsFetchingMainData(false);
          return; // Exit early for progressive loading
        }

        console.log('🔍 [Main] Design is completed, fetching complete data');
        // Otherwise fetch complete data
        data = await getFigmaFileData(figmaId);
        // Ensure frames property exists for Figma data
        if (!data || !data.frames) {
          throw new Error('Invalid Figma data structure received');
        }
      } else if (designType === 'image') {
        const response = await getFigmaExtImage(projectId, figmaId);
        // Check if response and image property exist
        if (!response || !response.image || !response.image.images) {
          throw new Error('Invalid image data structure received');
        }
        // Transform image data to match expected frames structure
        data = {
          frames: response.image.images.map(img => ({
            id: img.file_id,
            name: img.filename,
            imageUrl: img.base64url,
            absoluteBoundingBox: {
              width: 800,
              height: 600
            },
            dimensions: {
              height: 800,
              width: 600
            }


          })) || []
        };
      }
      

      setFigmaData(data || { frames: [] });
      setFrames(Array.isArray(data?.frames) ? data.frames : []);
      setSelectedDesignData(data || { frames: [] });
    } catch (err) {
      
      setError(err.message);
      showAlert(err.message, 'error');
      // Set empty arrays to prevent undefined errors
      setFigmaData({ frames: [] });
      setFrames([]);
    } finally {
      setIsFetchingMainData(false);
    }
  };

  useEffect(() => {
    console.log('🔍 [Effect] useEffect triggered');
    console.log('🔍 [Effect] figmaId:', figmaId);
    console.log('🔍 [Effect] selectedDesign:', selectedDesign);

    if (figmaId) {
      console.log('🔍 [Effect] figmaId exists, calling fetchFigmaData');
      fetchFigmaData();
    } else {
      console.log('🔍 [Effect] No figmaId, skipping fetchFigmaData');
    }
  }, [figmaId, selectedDesign]);

  // Progressive loading effect - poll for updates when in progressive mode
  useEffect(() => {
    console.log('🔍 [Polling] Progressive loading effect triggered');
    console.log('🔍 [Polling] isProgressiveLoading:', isProgressiveLoading);
    console.log('🔍 [Polling] designType:', designType);
    console.log('🔍 [Polling] figmaId:', figmaId);
    console.log('🔍 [Polling] projectId:', projectId);

    let intervalId;

    if (isProgressiveLoading && designType === 'figma') {
      console.log('🔍 [Polling] Starting polling interval (every 2 seconds)');
      intervalId = setInterval(() => {
        console.log('🔍 [Polling] Polling tick - calling fetchProgressiveFrames');
        fetchProgressiveFrames();
      }, 2000); // Poll every 2 seconds
    } else {
      console.log('🔍 [Polling] Not starting polling - conditions not met');
    }

    return () => {
      if (intervalId) {
        console.log('🔍 [Polling] Cleaning up polling interval');
        clearInterval(intervalId);
      }
    };
  }, [isProgressiveLoading, figmaId, projectId, designType]);

  // Listen for WebSocket updates to trigger progressive loading refresh
  useEffect(() => {
    const handleFigmaUpdate = (event) => {
      if (event.detail?.figma_id === figmaId && isProgressiveLoading) {
        fetchProgressiveFrames();
      }
    };

    window.addEventListener('figma_update', handleFigmaUpdate);

    return () => {
      window.removeEventListener('figma_update', handleFigmaUpdate);
    };
  }, [figmaId, isProgressiveLoading]);

  const onRowClick = async (rowOrId) => {
    if (fetchingScreenId) return; // Prevent multiple clicks

    // Handle both cases: when receiving full row object or just ID
    const row = typeof rowOrId === 'string'
      ? frames.find(frame => frame.id === rowOrId)
      : rowOrId;

    if (!row || !row.id) {
      return;
    }

    // Only allow clicks on processed frames
    const frameData = row.originalData || row;
    if (!frameData.is_processed) {
      showAlert('This frame is still being processed. Please wait.', 'warning');
      return;
    }

    setFetchingScreenId(row.id);
    try {
      if (row.imageUrl) {
        setSelectedImage(row.imageUrl);
        return;
      }
    } catch (error) {
      console.error('Error fetching image:', error);
      showAlert('Failed to load image', 'error');
    } finally {
      setFetchingScreenId(null);
    }
  };

  const handleRename = (row) => {
    if (row && row.id) {
      onRename(row);
    }
  };

  const handleDelete = async (row) => {
    
    // e.stopPropagation(); // Prevent row click event
    if (row && row.originalData) {
      // Call the onDelete prop with the frame data
      onDelete({
        id: row.originalData.id,
        name: row.originalData.name
      });
    } else if (row && row.id) {
      // For card view which has a different data structure
      onDelete({
        id: row.id,
        name: row.title
      });
    }
  }

  const handleDownload = async (row) => {
    try {
      
      // Create blob from image URL
      const response = await fetch(row.imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `${row.name|| row.title || 'image'}.png`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      
    }
  }

  const formatForTable = (frames) => {
    if (!Array.isArray(frames)) {

      return [];
    }
    return frames.map((frame) => ({
      id: frame.id || `frame-${Math.random()}`,
      name: frame.name || 'Untitled',
      dimensions: frame.absoluteBoundingBox
        ? `${Math.round(frame.absoluteBoundingBox.width)} x ${Math.round(frame.absoluteBoundingBox.height)}`
        : 'N/A',
      type: designType === 'image' ? 'Image' : 'Frame',
      status: frame.is_processed ? 'Processed' : 'Processing...',
      imageUrl: frame.imageUrl || '',
      // Add original frame data for actions
      originalData: frame,
      is_processed: frame.is_processed || false,
      processing_status: frame.processing_status || 'pending'
    }));
  };

  const formatForCards = (frames) => {
    if (!Array.isArray(frames)) {
      return [];
    }
    return frames.map((frame) => ({
      id: frame.id || `frame-${Math.random()}`,
      image: frame.imageUrl ||
        (frame.absoluteBoundingBox
          ? `https://via.placeholder.com/${Math.round(frame.absoluteBoundingBox.width)}x${Math.round(frame.absoluteBoundingBox.height)}?text=${encodeURIComponent(frame.name || 'Untitled')}`
          : `https://via.placeholder.com/800x600?text=${encodeURIComponent(frame.name || 'Untitled')}`),
      title: frame.name || 'Untitled',
      dimensions: frame.absoluteBoundingBox ? {
        width: Math.round(frame.absoluteBoundingBox.width),
        height: Math.round(frame.absoluteBoundingBox.height),
      } : { width: 800, height: 600 },
      imageUrl: frame.imageUrl || '',
      is_processed: frame.is_processed || false,
      processing_status: frame.processing_status || 'pending'
    }));
  };

  const tableHeaders = designType === 'image' ? [
    { key: "name", label: "Screen" },
    { key: "dimensions", label: "Dimensions" },
    { key: "type", label: "Type" },
    { key: "action", label: "Actions" }
  ] : [
    { key: "name", label: "Screen" },
    { key: "dimensions", label: "Dimensions" },
    { key: "type", label: "Type" },
    { key: "status", label: "Status" }
  ];


  return (
    <div className="h-full max-h-[80vh] overflow-auto">
      {isFetchingMainData ? (
        <Loader loadingText={"Fetching Frames"} />
      ) : isProgressiveLoading ? (
        <div className="p-4">
          <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="typography-body-lg font-weight-medium text-blue-900">
                Processing Figma Design
              </h3>
              <div className="text-blue-600 typography-body-sm">
                {progressiveStatus?.completed_frames || 0} / {progressiveStatus?.total_frames || 0} frames
              </div>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${progressiveStatus?.total_frames > 0
                    ? (progressiveStatus.completed_frames / progressiveStatus.total_frames) * 100
                    : 0}%`
                }}
              ></div>
            </div>
            <p className="text-blue-700 typography-body-sm">
              All frames are shown below. Processed frames are clickable, while others are still being processed.
            </p>
          </div>

          <div className="h-full overflow-auto">
            <div className={`${viewMode === 'card' ? 'bg-white rounded-xl shadow-sm border border-gray-100 p-6' : ''}`}>
              {viewMode === "table" ? (
                <div className="max-h-[80%] overflow-hidden mb-12">
                  <TableComponent
                    data={formatForTable(frames)}
                    onRowClick={onRowClick}
                    headers={tableHeaders}
                    sortableColumns={{ id: true, name: true, dimensions: true }}
                    itemsPerPage={20}
                    isLoading={fetchingScreenId !== null}
                    loadingRowId={fetchingScreenId}
                    type={designType}
                    onEdit={(row) => handleRename(row.originalData)}
                    onDelete={(row) => handleDelete(row.originalData)}
                    onDownload={(row) => handleDownload(row)}
                  />
                </div>
              ) : (
                <CardGrid
                  cards={formatForCards(frames)}
                  onCardClick={onRowClick}
                  loadingCardId={fetchingScreenId}
                  type={designType}
                  onRename={(row) => handleRename(row)}
                  onDelete={(row)=>handleDelete(row)}
                  onDownload={(row) => handleDownload(row)}
                />
              )}
            </div>
          </div>
        </div>
      ) : error ? (
        <ErrorView
          title="Unable to Load Figma"
          message={error}
          onRetry={() => fetchFigmaData()}
          panelType='main'
        />
      ) : !frames || frames.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="bg-gray-100 p-4 rounded-full mb-4">
            <Group size={32} className="text-gray-400" />
          </div>
          <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-2">No Frames Found</h3>
          <p className="text-gray-500 max-w-md">
            <EnParser content={en.NoFrames} />
          </p>
          <div className="mt-4 typography-body-sm text-gray-400">
            Design ID: {figmaId || 'Not provided'}
          </div>
        </div>
      ) : (
        <div className="h-full overflow-auto p-4">
          <div className={`${viewMode === 'card' ? 'bg-white rounded-xl shadow-sm border border-gray-100 p-6' : ''}`}>
            {viewMode === "table" ? (
              <div className="max-h-[80%] overflow-hidden mb-12">
                <TableComponent
                  data={formatForTable(frames)}
                  onRowClick={onRowClick}
                  headers={tableHeaders}
                  sortableColumns={{ id: true, name: true, dimensions: true }}
                  itemsPerPage={20}
                  isLoading={fetchingScreenId !== null}
                  loadingRowId={fetchingScreenId}
                  type={designType}
                  onEdit={(row) => handleRename(row.originalData)}
                  onDelete={(row) => handleDelete(row.originalData)}
                  onDownload={(row) => handleDownload(row)}
                />
              </div>
            ) : (
              <CardGrid
                cards={formatForCards(frames)}
                onCardClick={onRowClick}
                loadingCardId={fetchingScreenId}
                type={designType}
                onRename={(row) => handleRename(row)}
                onDelete={(row)=>handleDelete(row)}  
                onDownload={(row) => handleDownload(row)}
              />
            )}
          </div>
        </div>
      )}

      {selectedImage && (
        <ImageLightbox
          imageUrl={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </div>
  );
}