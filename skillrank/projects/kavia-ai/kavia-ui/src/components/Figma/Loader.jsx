import React from 'react';

const Loader = ({ loadingText = "Loading..." }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full py-12">
      <div className="relative">
        <div className="h-16 w-16 rounded-full border-t-4 border-b-4 border-primary animate-spin"></div>
        <div className="h-12 w-12 rounded-full border-t-4 border-b-4 border-primary-500 animate-spin absolute top-2 left-2"></div>
      </div>
      <p className="mt-4 text-gray-600 font-weight-medium">{loadingText}</p>
    </div>
  );
};

export default Loader;
