// src/components/Figma/ExtractionTabs/StatusTab.jsx
'use client'
import { useState, useEffect, } from 'react';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { useFigmaExtraction } from '@/components/Context/FigmaExtractionContext';
import { useSearchParams, useRouter, useParams } from 'next/navigation';
const StatusTab = ({  extractionStatus, extractionProgress, logMessages, selectedFrame }) => {
  // Provide default values in case props aren't passed
  const { projectId } = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { figmaDiscussionId } = useFigmaExtraction();
  const [isRetrying, setIsRetrying] = useState(false);
  const [customProgressData, setCustomProgressData] = useState(null);
  const [customLogMessages, setCustomLogMessages] = useState(null);
  
  // Update status and progress data based on selected frame
  const [status, setStatus] = useState(extractionStatus || {
    status: 'Running',
    taskId: searchParams.get('figmaDiscussionId') || 'figma-extraction',
    description: 'Starting task: Figma Screen Extraction'
  });
  useEffect(() => {
    if (selectedFrame) {
      setStatus(prevStatus => ({
        ...prevStatus,
        description: `Processing frame: ${selectedFrame.name}`
      }));
      
      // Update progress data based on selected frame
      if (extractionProgress) {
        setCustomProgressData({
          ...extractionProgress,
          // Show a different percentage based on the frame ID (for demo purposes)
          percentage: (extractionProgress.percentage + (selectedFrame.id * 5)) % 100,
        });
      } else {
        setCustomProgressData(null);
      }
      
      // Update log messages based on selected frame
      if (logMessages && logMessages.length > 0) {
        // Add a frame-specific log message at the top
        const frameSpecificLogs = [
          { 
            timestamp: new Date().toLocaleString(), 
            message: `Processing frame: ${selectedFrame.name}`, 
            level: 'info' 
          },
          ...logMessages
        ];
        setCustomLogMessages(frameSpecificLogs);
      } else {
        setCustomLogMessages(null);
      }
    } else {
      setCustomProgressData(extractionProgress);
      setCustomLogMessages(logMessages);
    }
  }, [selectedFrame, extractionProgress, logMessages]);

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      // Implement retry logic here
      await new Promise(resolve => setTimeout(resolve, 1000));
      // After retry completes, you might want to update the status
    } catch (error) {
      
    } finally {
      setIsRetrying(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="bg-white border-b border-gray-200 pb-6">
        <h3 className="typography-body-lg font-weight-semibold mb-4 flex items-center justify-between">
          Current Status
          <DynamicButton
            type="button"
            size="small"
            variant="danger"
            onClick={handleRetry}
            text="Retry"
            isLoading={isRetrying}
            disabled={isRetrying}
          />
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center">
            <span className="typography-body-sm font-weight-medium text-gray-600 w-24">Status:</span>
            <span className="px-3 py-1 rounded-full typography-body-sm font-weight-medium bg-green-100 text-green-800">
              {status.status}
            </span>
          </div>
          
          <div className="flex items-center">
            <span className="typography-body-sm font-weight-medium text-gray-600 w-24">Task ID:</span>
            <span className="typography-body-sm text-primary">{status.taskId}</span>
          </div>
          
          <div className="flex items-center">
            <span className="typography-body-sm font-weight-medium text-gray-600 w-24">Description:</span>
            <span className="typography-body-sm text-gray-700">{status.description}</span>
          </div>
        </div>
      </div>

      {/* Conditionally render progress section */}
      {customProgressData && (
        <div className="mt-6">
          <h3 className="typography-body-lg font-weight-semibold mb-4">Extraction Progress</h3>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between typography-body-sm">
                <span>Progress</span>
                <span>{customProgressData.percentage || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-primary"
                  style={{ width: `${customProgressData.percentage || 0}%` }}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-6 mt-4">
              <div className="border border-gray-200 rounded-md p-3">
                <div className="typography-body-sm font-weight-medium text-gray-600">Total Elements</div>
                <div className="typography-heading-2 font-weight-bold mt-1">{customProgressData.totalElements || 0}</div>
              </div>
              
              <div className="border border-gray-200 rounded-md p-3">
                <div className="typography-body-sm font-weight-medium text-gray-600">Processed</div>
                <div className="typography-heading-2 font-weight-bold mt-1">{customProgressData.processedElements || 0}</div>
              </div>
              
              <div className="border border-gray-200 rounded-md p-3">
                <div className="typography-body-sm font-weight-medium text-gray-600">Succeeded</div>
                <div className="typography-heading-2 font-weight-bold text-green-600 mt-1">{customProgressData.succeededElements || 0}</div>
              </div>
              
              <div className="border border-gray-200 rounded-md p-3">
                <div className="typography-body-sm font-weight-medium text-gray-600">Failed</div>
                <div className="typography-heading-2 font-weight-bold text-red-600 mt-1">{customProgressData.failedElements || 0}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conditionally render log messages section */}
      {customLogMessages && customLogMessages.length > 0 && (
        <div className="mt-6">
          <h3 className="typography-body-lg font-weight-semibold mb-4">Recent Log Messages</h3>
          
          <div className="space-y-1 max-h-48 overflow-y-auto bg-gray-50 p-4 rounded-md">
            {customLogMessages.map((log, index) => (
              <div key={index} className={`typography-caption  ${
                log.level === 'warning' ? 'text-yellow-600' : 
                log.level === 'error' ? 'text-red-600' : ''
              }`}>
                <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default StatusTab;