import React, { useState } from 'react';
import { FaGithub } from 'react-icons/fa';
import { ChevronDown, Loader2 } from 'lucide-react';
import { getCookie } from '@/utils/auth';
import { getHeadersRaw } from '@/utils/api';
import EmptyStateView from '../Modal/EmptyStateModal';
interface Branch {
  name: string;
  isDefault: boolean;
}

interface Repository {
  id: number;
  name: string;
  description: string | null;
  languages: string[];
  branch: string | null;
  branches: Branch[];
  selected: boolean;
  lastUpdated: string;
  clone_url: string;
  path?: string;
  repo_type?: 'public' | 'private';
}

interface RepositoryTableProps {
  repositories: Repository[];
  onToggleRepository: (index: number) => void;
  onUpdateRepository?: (index: number, updates: Partial<Repository>) => void;
}

interface BranchSelectProps {
  repo: Repository;
  index: number;
  onBranchSelect: (branch: string) => void;
  isSelectable: boolean;
  onUpdateRepository: (index: number, updates: Partial<Repository>) => void;
}

const BranchSelect: React.FC<BranchSelectProps> = ({
  repo,
  index,
  onBranchSelect,
  isSelectable,
  onUpdateRepository
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<string | null>(repo.branch);

  const fetchBranches = async () => {

    // Extract owner from path safely
    const pathParts = repo.path?.split('/') || [];
    const owner = pathParts[3]; // GitHub URLs typically follow pattern: https://github.com/owner/repo

    setIsLoading(true);
    setError(null);
    try {

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/oauth/github/repo-branches`, {
        method: 'POST',
        headers: getHeadersRaw(),
        body: JSON.stringify({
          user_id: await getCookie('userId'),
          repo_name: repo.name,
          owner: repo.path ? repo.path.split('/')[3] : '',
        }),
      });
      // window.alert(repo.path.split('/')[3]);
      if (!response.ok) throw new Error('Failed to fetch branches');

      const data: Branch[] = await response.json();
      if (data.length === 0) {
        setError('No branches found');
        return;
      }
      setBranches(data);

      // If no branch is selected, choose the default branch
      if (!selectedBranch) {
        const defaultBranch = data.find(branch => branch.isDefault);
        if (defaultBranch) {
          handleBranchSelect(defaultBranch.name);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // In RepositoryTable.tsx, update the BranchSelect component's handleBranchSelect function:

const handleBranchSelect = (branchName: string) => {
  setSelectedBranch(branchName);
  onBranchSelect(branchName);
  onUpdateRepository(index, {
      branch: branchName,
      clone_url: repo.path,
      description: repo.description,
      name: repo.name
  });
};

  return (
    <div className="relative w-[200px]">
      <button
        onClick={() => {
          if (!isOpen) fetchBranches();
          setIsOpen(!isOpen);
        }}
        disabled={!isSelectable}
        className={`w-full flex items-center justify-between px-3 py-1.5 border rounded-md
          ${!isSelectable
            ? 'bg-gray-100 cursor-not-allowed border-gray-200'
            : 'bg-white border-gray-300 hover:bg-gray-50'
          }`}
      >
        <span className={`typography-body-sm truncate max-w-[150px] ${!isSelectable ? 'text-gray-400' : 'text-gray-700'}`}>
          {selectedBranch || 'Select branch'}
        </span>
        <ChevronDown
          className={`w-4 h-4 transition-transform flex-shrink-0
            ${isOpen ? 'transform rotate-180' : ''}
            ${!isSelectable ? 'text-gray-400' : 'text-gray-500'}`}
        />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="w-5 h-5 text-primary animate-spin" />
              <span className="ml-2 text-gray-600">Loading branches...</span>
            </div>
          ) : error ? (
            <div className="p-4 text-black-500 text-center">{error}</div>
          ) : (
            branches.map((branch, idx) => (
              <button
                key={idx}
                onClick={() => {
                  if (isSelectable) {
                    handleBranchSelect(branch.name);
                  }
                  setIsOpen(false);
                }}
                className={`w-full px-4 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100
                  ${selectedBranch === branch.name ? 'bg-primary-50' : ''}`}
              >
                <div className="flex items-center justify-between">
                  <span className="typography-body-sm text-gray-900">{branch.name}</span>
                  {branch.isDefault && (
                    <span className="typography-caption text-primary bg-primary-50 px-2 py-0.5 rounded">
                      default
                    </span>
                  )}
                </div>
              </button>
            ))
          )}
        </div>
      )}
    </div>
  );
};

const RepositoryTable: React.FC<RepositoryTableProps> = ({
  repositories,
  onToggleRepository,
  onUpdateRepository = () => {}
}) => {
    const allowedLanguages = [
      'JavaScript', 'TypeScript', 'HTML', 'CSS', 'SCSS',
      'JSON', 'YAML', 'Markdown', 'Python', 'C', 'C++',
      'COBOL', 'Prompt', 'Jinja2'
    ];
    const [searchQuery, setSearchQuery] = useState('');

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      JavaScript: 'bg-yellow-200 text-yellow-800',
      TypeScript: 'bg-primary-200 text-primary-800',
      Python: 'bg-green-200 text-green-800',
      Vue: 'bg-emerald-200 text-emerald-800',
      Java: 'bg-red-200 text-red-800',
      'C++': 'bg-purple-200 text-purple-800',
      PHP: 'bg-indigo-200 text-indigo-800',
      HTML: 'bg-primary-200 text-primary-800',
      CSS: 'bg-pink-200 text-pink-800',
      SCSS: 'bg-purple-200 text-purple-800',
      C: 'bg-gray-200 text-gray-800',
    };
    return colors[language] || 'bg-gray-200 text-gray-800';
  };
  const filteredRepositories = repositories.filter((repo) =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToggleRepository = (repoIndex: number) => {
    const originalRepo = filteredRepositories[repoIndex];
    const trueOriginalIndex = repositories.findIndex(
      (repo) => repo === originalRepo
    );
    onToggleRepository(trueOriginalIndex);
  };



  const selectedCount = filteredRepositories.filter((repo) => repo.selected).length;


  return (
    <>
     <div className='mt-2 ml-2 mr-2 '>
     <input
            type="text"
            placeholder="Search repositories..."
            className="w-full border border-gray-300 rounded-lg px-6 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent "
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}

          />
          </div>
    <div className="flex flex-col h-full mt-3">

      <div className="flex-grow border border-[#e9edf5] rounded-[5px] overflow-hidden">
      {filteredRepositories.length === 0 ? (
            <EmptyStateView
                type="noSearchResult"
                onClick={() => setSearchQuery("")}
              />
          ):(
        <div className="max-h-[400px] overflow-y-auto">

          <table className="w-full border-collapse">
            <thead className="bg-[#f7f8fc] sticky top-0 z-10">
              <tr>
                <th className="w-12 py-3 px-4"></th>
                <th className="text-left py-3 px-4 w-1/4">
                  <span className="text-[#687082] typography-caption font-weight-medium uppercase">Repository</span>
                </th>
                <th className="text-left py-3 px-4 w-1/3">
                  <span className="text-[#687082] typography-caption font-weight-medium uppercase">Description</span>
                </th>
                <th className="text-left py-3 px-4 w-[200px]">
                  <span className="text-[#687082] typography-caption font-weight-medium uppercase">Branch</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredRepositories.map((repo, index) => {
                const isSelectable = true

                return (
                  <tr
                    key={repo.id}
                    className={`border-t border-[#e9edf5] ${isSelectable ? 'hover:bg-gray-50' : 'bg-gray-100'}`}
                  >
                    <td className="py-4 px-4">
                      <input
                        type="checkbox"
                        checked={repo.selected}
                        onChange={() => isSelectable && handleToggleRepository(index)}
                        disabled={!isSelectable}
                        className="w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <FaGithub className="w-5 h-5 text-gray-600" />
                        <a
                          href={repo.path}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`font-weight-medium ${isSelectable ? 'text-primary hover:text-primary-800' : 'text-gray-400'}`}
                        >
                          {repo.name}
                        </a>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <p className={`typography-body-sm ${isSelectable ? 'text-gray-600' : 'text-gray-400'} line-clamp-2`}>
                        {repo.description || 'No description available'}
                      </p>
                    </td>
                    <td className="py-4 px-4">
                      <BranchSelect
                        repo={repo}
                        index={index}
                        isSelectable={isSelectable}
                        onBranchSelect={(branchName) => {
                          if (isSelectable) {
                            onUpdateRepository(index, { branch: branchName });
                          }
                        }}
                        onUpdateRepository={onUpdateRepository}
                      />
                    </td>

                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>)}
      </div>
      <div className="mt-4 typography-body-sm text-gray-600">
        {selectedCount} repositories selected
      </div>
    </div>
    </>
  );
};

export default RepositoryTable;