"use client";
import React, { createContext, useState, useContext, useEffect, useCallback, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";

// Create a context for the deployment data
const DeploymentContext = createContext();

// Custom hook to use the Deployment context
export const useDeployment = () => {
  return useContext(DeploymentContext);
};

// Provider component to wrap your app
export const DeploymentProvider = ({ children }) => {
  const searchParams = useSearchParams();
  const currentTaskId = searchParams.get("task_id");
  const { showAlert } = useContext(AlertContext);

  // Repository and deployment settings state
  const [repositories, setRepositories] = useState([]);
  const [currentRepository, setCurrentRepository] = useState(null);
  const [deploymentPath, setDeploymentPath] = useState('/');
  const [isLoadingRepo, setIsLoadingRepo] = useState(false);
  
  // Panel visibility states
  const [isDeployPanelOpen, setIsDeployPanelOpen] = useState(false);
  const [isDeploymentConfigOpen, setIsDeploymentConfigOpen] = useState(false);
  
  // Deployment status
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployedUrl, setDeployedUrl] = useState('');
  const [showDeploymentSuccess, setShowDeploymentSuccess] = useState(false);
  
  // Deployment configuration
  const [command, setCommand] = useState('npm run build');
  const [installPackages, setInstallPackages] = useState(true);
  const [envVariables, setEnvVariables] = useState([]);
  
  // Manifest data
  const [manifest, setManifest] = useState(null);
  
  // WebSocket reference (passed from parent)
  const [wsConnection, setWsConnection] = useState(null);
  const [dirContents, setDirContents] = useState([]);
  const [currentPath, setCurrentPath] = useState('');
  const [isLoadingDir, setIsLoadingDir] = useState(false);

  // Add refs to track values inside WebSocket handlers to prevent re-registrations
  const deploymentPathRef = useRef('/');
  const hasAutoSelectedRef = useRef(false);

  // Update refs when state changes
  useEffect(() => {
    deploymentPathRef.current = deploymentPath;
  }, [deploymentPath]);

  // Reset auto-selection flag when deployment path changes manually
  useEffect(() => {
    hasAutoSelectedRef.current = false;
  }, [deploymentPath]);
  
  // Fetch repositories via WebSocket
  const fetchRepositories = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      setIsLoadingRepo(true);
      wsConnection.send(JSON.stringify({
        type: "list_repositories",
        task_id: currentTaskId
      }));
      
      
    } else {
      setIsLoadingRepo(false);
    }
  }, [wsConnection, currentTaskId]);
  
  // Fetch directory contents via WebSocket
  const fetchDirectoryContents = useCallback((path = '') => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentRepository?.name) {
      // Prevent multiple concurrent calls
      if (isLoadingDir) {
        return;
      }
      
      setIsLoadingDir(true);
      
      // Format the message
      const message = {
        type: "list_dir",
        input_data: {
          base_dir: path || currentRepository.name
        }
      };
      
      // Add the task_id
      if (currentTaskId) {
        message.task_id = currentTaskId;
      }
      
      wsConnection.send(JSON.stringify(message));
      
      // Auto-select the first folder when listing directories for code-generation tasks
      if (currentTaskId?.startsWith('cg')) {
        // The selection will be handled when receiving the response
        // in the WebSocket message handler
      }
    } else {
      setIsLoadingDir(false);
    }
  }, [wsConnection, currentRepository, currentTaskId, isLoadingDir]);
  
  // Fetch manifest data via WebSocket
  const fetchManifest = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      
      
      // Format the message similar to list_dir
      const message = {
        type: "manifest"
      };
      
      // Add the task_id
      if (currentTaskId) {
        message.task_id = currentTaskId;
      }
      
      wsConnection.send(JSON.stringify(message));
    } else {
      
    }
  }, [wsConnection, currentTaskId]);
  
  // Launch deployment
  const launchDeployment = useCallback((customCommand, customInstallPackages, customEnvVariables) => {
    setIsDeploying(true);
    
    if (!currentRepository) {
      setIsDeploying(false);
      showAlert("No repository selected", "error");
      return;
    }
    
    // Convert environment variables array to object
    const envObject = {};
    (customEnvVariables || envVariables).forEach(v => {
      if (v.key.trim() !== '') {
        envObject[v.key] = v.value;
      }
    });
    
    // Prepare deployment data
    const deploymentData = {
      repository: currentRepository.name,
      path: deploymentPath,
      command: customCommand || command,
      install_packages: customInstallPackages !== undefined ? customInstallPackages : installPackages,
      env_variables: envObject
    };
    
    // Send deployment request
    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        type: "deploy_repository",
        task_id: currentTaskId,
        data: deploymentData
      }));
      
      
    } else {
      setIsDeploying(false);
      showAlert("WebSocket connection not available", "error");
    }
  }, [wsConnection, currentRepository, currentTaskId, deploymentPath, command, installPackages, envVariables, showAlert]);
  
  // Handle deploy panel toggle
  const handleDeployClick = useCallback(() => {
    // Toggle the panel state
    setIsDeployPanelOpen(!isDeployPanelOpen);
    
    // Only fetch repositories when opening the panel
    if (!isDeployPanelOpen && !repositories.length) {
      fetchRepositories();
    }
    
    // Always open the deployment configuration drawer when the button is clicked
    setIsDeploymentConfigOpen(true);
  }, [isDeployPanelOpen, repositories.length, fetchRepositories]);
  
  // Handle configure deploy settings
  const handleConfigure = useCallback(() => {
    // Close the deploy panel and open the configuration panel
    setIsDeployPanelOpen(false);
    setIsDeploymentConfigOpen(true);
    
    // Make sure repositories are loaded if not already
    if (!repositories.length) {
      fetchRepositories();
    }
  }, [repositories.length, fetchRepositories]);
  
  // WebSocket message handler for repository updates and deployment status
  useEffect(() => {
    if (!wsConnection) return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Handle repository list responses
        if (data.type === 'repository_list') {
          setRepositories(data.data.repositories || []);
          setIsLoadingRepo(false);
          if (data.data.repositories?.length > 0) {
            setCurrentRepository(data.data.repositories[0]);
          }
        }
        
        // Handle deployment status updates
        if (data.type === 'deployment_status') {
          // Check for the new message format with data.data structure
          const statusData = data.data || data;
          const status = statusData.status || data.status;
          
          if (status === 'completed' || status === 'success') {
            setIsDeploying(false);
            setIsDeploymentConfigOpen(false);
            
            // Check if there's a deployment URL
            if (statusData.url || data.url) {
              setDeployedUrl(statusData.url || data.url);
              setShowDeploymentSuccess(true);
            }
            // Removed the success alert here
          } else if (status === 'failed' || status === 'error') {
            setIsDeploying(false);
            showAlert("Deployment failed", "error");
          } else if (status === 'in_progress' || status === 'processing') {
            // Keep the loading state active
            setIsDeploying(true);
          }
        }
        
        // Handle reply from start_deploy message
        if (data.type === 'start_deploy_response') {
          if (data.status === 'success' || data.success === true) {
            // The deployment started successfully
            setIsDeploying(true);
            showAlert("Deployment started successfully", "success");
          } else if (data.status === 'failed' || data.success === false) {
            // The deployment failed to start
            setIsDeploying(false);
            showAlert(data.message || "Failed to start deployment", "error");
          }
        }
        
        // Handle directory listing responses
        if (data.type === 'dir_list' || data.type === 'list_dir_response') {
          const contents = data.data?.contents || data.contents || [];
          if (Array.isArray(contents)) {
            setDirContents(contents);
            setCurrentPath(data.data?.path || data.path || '');
            
            // Auto-select the first folder for all tasks, avoiding "logs" folders
            // Only do this if deployment path is currently root AND we haven't auto-selected before
            if (deploymentPathRef.current === '/' && !hasAutoSelectedRef.current) {
              // First try to find a folder that's not named "logs" and not hidden
              let folderToSelect = contents.find(item => 
                item.type === 'folder' && !item.name.startsWith('.') && item.name.toLowerCase() !== 'logs'
              );
              
              // If no folder other than "logs" is found, and there's at least one folder, use the first one
              if (!folderToSelect) {
                folderToSelect = contents.find(item => 
                  item.type === 'folder' && !item.name.startsWith('.')
                );
              }
              
              if (folderToSelect) {
                const newPath = `/${folderToSelect.name}`;
                setDeploymentPath(newPath);
                hasAutoSelectedRef.current = true; // Mark as auto-selected
              }
            }
            
            setIsLoadingDir(false);
          }
        }
        
        // Handle manifest responses
        if (data.type === 'manifest') {
          setManifest(data.data);
          
          // Note: base_path handling is now done in DeploymentInterface.tsx
          // based on the container type (frontend/backend) to avoid conflicts
        }
        
        // Check for any message containing directory contents
        else if ((data.contents && Array.isArray(data.contents)) || 
                (data.data && data.data.contents && Array.isArray(data.data.contents))) {
          const contents = data.data?.contents || data.contents;
          if (Array.isArray(contents)) {
            setDirContents(contents);
            setCurrentPath(data.data?.path || data.path || '');
            setIsLoadingDir(false);
          }
        }
      } catch (error) {
        setIsLoadingDir(false);
      }
    };

    wsConnection.addEventListener('message', handleMessage);
    
    return () => {
      wsConnection.removeEventListener('message', handleMessage);
    };
  }, [wsConnection, showAlert, currentTaskId]); // Removed deploymentPath to prevent unnecessary re-registrations

  return (
    <DeploymentContext.Provider
      value={{
        // Repository data
        repositories,
        setRepositories,
        currentRepository,
        setCurrentRepository,
        deploymentPath,
        setDeploymentPath,
        isLoadingRepo,
        
        // Panel state
        isDeployPanelOpen,
        setIsDeployPanelOpen,
        isDeploymentConfigOpen,
        setIsDeploymentConfigOpen,
        
        // Deployment status
        isDeploying,
        setIsDeploying,
        deployedUrl,
        setDeployedUrl,
        showDeploymentSuccess,
        setShowDeploymentSuccess,
        
        // Deployment configuration
        command,
        setCommand,
        installPackages,
        setInstallPackages,
        envVariables,
        setEnvVariables,
        
        // Directory browsing
        dirContents,
        currentPath,
        isLoadingDir,
        
        // Manifest data
        manifest,
        setManifest,
        
        // WebSocket
        wsConnection,
        setWsConnection,
        
        // Functions
        fetchRepositories,
        fetchDirectoryContents,
        fetchManifest,
        launchDeployment,
        handleDeployClick,
        handleConfigure,
      }}
    >
      {children}
    </DeploymentContext.Provider>
  );
};
