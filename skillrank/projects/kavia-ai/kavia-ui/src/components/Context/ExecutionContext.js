"use client";
import React, { createContext, useState, useEffect, useContext } from "react";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { AlertContext } from "../NotificationAlertService/AlertList";
const ExecutionContext = createContext();
import Cookies from "js-cookie";
import { useParams, usePathname } from "next/navigation";
import {
  cancelTask,
  deleteTask,
  getActiveTasks,
  getHeadersRaw,
} from "@/utils/api";
import { StateContext } from "./StateContext";
import { retrieveTaskStatus } from "@/utils/api";
import { getReconfigNodeSectionFlag } from "@/utils/api";

const ExecutionProvider = ({ children }) => {
  const { showAlert } = useContext(AlertContext);
  const { isVertCollapse, setIsVertCollapse } = useContext(StateContext);
  const idToken = Cookies.get("idToken");
  const tenant_id = Cookies.get("tenant_id");
  const [currentTaskId, setCurrentTaskId] = useState(null); //
  const [lastTime, setLastTime] = useState(null);
  const [configStatus, setConfigStatus] = useState({});
  const [currentTaskDetailsId, setCurrentTaskDetailsId] = useState(null);
  const [isNodeType, setIsNodeType] = useState(null);
  const [activeTask, setActiveTask] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const pathname = usePathname();
  const { projectId } = useParams();
  const [currentTaskDetails, setcurrentTaskDetails] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading,setIsLoading] = useState(false);
  const [configLabel,setConfiglabel] = useState("auto-config")
  const [taskStatusUpdate,setTaskStatusUpdate] = useState("Idle")
  const [extractTaskId,setExtractTaskId] = useState(null)
  const [extract,setExtract] = useState(false)
  const [taskStatusInfo,setTaskStatusInfo] = useState(false)
  const [enableReconfig, setEnableReconfig] = useState(false);
  const [reconfigApprovedOrRejected, setReconfigApprovedOrRejected] = useState(false);
  const [reconfigStatus, setReconfigStatus] = useState({
    project_reconfig: false,
    requirement_reconfig: false,
    architecture_reconfig: false,
  });
  const [requirementReconfigEnable,setRequirementReconfigEnable] = useState(false)
  const [architectureReconfigEnable,setArchitectureReconfigEnable] = useState(false)
  const [autoNavigateEnabled, setAutoNavigateEnabled] = useState(() => {
    // Check if we're in the browser environment before accessing localStorage
    if (typeof window !== 'undefined') {
      const savedPreference = localStorage.getItem('autoNavigateEnabled');
      return savedPreference !== null ? JSON.parse(savedPreference) : true;
    }
    return true; // Default value for server-side rendering
  });

  // Save autoNavigateEnabled to localStorage whenever it changes
  useEffect(() => {
    // Only access localStorage in the browser
    if (typeof window !== 'undefined') {
      localStorage.setItem('autoNavigateEnabled', JSON.stringify(autoNavigateEnabled));
    }
  }, [autoNavigateEnabled]);

  const confirmAndDelete = () => {
    setIsDeleteModalOpen(true);
  };

  const pauseTask = async (deleteRecord = false) => {
    setIsDeleting(true)
    try {
      if(currentTaskId){
        const response = await deleteTask(currentTaskId, deleteRecord);
        if(response){
          showAlert("Configuration execution cancelled successfully","success")
          setExtract(false)
          if (sessionStorage.getItem(`${projectId}-auto-extract`)) {
            sessionStorage.removeItem(`${projectId}-auto-extract`);
          }
        }

      }else{
        setIsDeleteModalOpen(false)
        showAlert("No active configurations to stop", "info")
      }

    } catch (error) {
      
    }finally{
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const checkForActiveTask = async (nodeId, nodeType = null) => {
    let activeTask = null;
    setCurrentTaskId(null);

    // Clear previous project's configuration status when switching projects
    setConfigStatus({});
    setLastTime(null);
    setTaskStatusUpdate("Idle");
    setReconfigApprovedOrRejected(false);

    try{
      activeTask = await getActiveTasks(nodeId, nodeType);
    }catch(e){

    }

    if (activeTask?.task_id){
      localStorage.removeItem('current_task_id');

      setCurrentTaskId(activeTask?.task_id);
      setConfiglabel(activeTask.type);
      if(activeTask.type == 're-config'){
        setReconfigApprovedOrRejected(activeTask.approved_or_rejected)
      }

    } else if(activeTask?.prev_task_id){
          setCurrentTaskId(activeTask.prev_task_id);
          setConfiglabel(activeTask.type);
          if(activeTask.type == 're-config'){
            setReconfigApprovedOrRejected(activeTask.approved_or_rejected)
          }

          setIsVertCollapse(false);
        }
  }


    const retrieveTaskStatusUpdate = async ()=>{
      try{
        const response= await retrieveTaskStatus(projectId,currentTaskId)
        if(response?.["auto-extract"] === true){
          setTaskStatusInfo(true)
          setConfiglabel('auto-extract')
        }else{
          setTaskStatusInfo(false)

        }
      }catch(error){
        }
    }





  let apiCalled = false;

  useEffect(() => {
    if (projectId) {
    checkForActiveTask(projectId,'Project');
    }
  }, [projectId]); // Dependency array, add variables you want to watch

  useEffect(() => {

    const fetchTaskUpdates = async (taskId) => {

      let url = `${process.env.NEXT_PUBLIC_API_URL}/v2/tasks/configure/task_updates/${taskId}`;
      const controller = new AbortController();
      const eventSource = new fetchEventSource(url, {
        headers: getHeadersRaw(),
        signal: controller.signal,
        openWhenHidden: true,
        onopen: (response) => {
          setIsConnected(true);

        },
        onmessage: (event) => {
          setIsConnected(true);
          const data = JSON.parse(event.data);
          console.log('ExecutionContext - WebSocket data received:', data);

          // Create a new configuration status object
          let newConfigStatus = {...configStatus.taskId || {}};
          if (data.status) {
            newConfigStatus.task_status = data.status;

            if (data.status === "complete" || data.status === "failed") {
              setIsConnected(false);
            }
          }
          if (data.progress) {
            newConfigStatus.progress = data.progress;
          }
          if (data.configuration_status) {
            newConfigStatus.configuration_status = data.configuration_status;
          }
          if (data.formatted_response) {
            newConfigStatus.formatted_response = data.formatted_response;
          }
          if (data.title) {
            newConfigStatus.title = data.title;
            setIsLoading(false);
          }
          if (data.start_time) {
            setLastTime(data.start_time);
          }
          if (data.eta_info) {
            console.log('ExecutionContext - eta_info found:', data.eta_info);
            newConfigStatus.eta_info = data.eta_info;
          }
          // Update the configStatus for the specific taskId


          // If the server sends a 'stop' signal, close the connection
          if (data.stop) {
            setIsConnected(false);
            controller.abort();
            apiCalled = false;
          }else{
            setConfigStatus((prevConfigStatus) => ({
              ...prevConfigStatus, // Keep existing task statuses
              [taskId]: newConfigStatus, // Update or add the status for this specific taskId
            }));
          }

        },
        onerror: (error) => {
          setIsLoading(false);
          
          controller.abort();
          apiCalled = false;
          // If network error, you can retry the connection
          fetchTaskUpdates(taskId);
          throw new Error("Error in task updates");
        },
        onclose: (response) => {
          apiCalled = false;
          return Promise.resolve();
        },
      });
    };

    if (!apiCalled && currentTaskId) {
      setIsLoading(true);
      fetchTaskUpdates(currentTaskId);
      retrieveTaskStatusUpdate();
      apiCalled = true;
    }
  }, [currentTaskId,projectId]);

  useEffect(() => {
    const loadReconfigStatus = async () => {
      setRequirementReconfigEnable(false);
      setArchitectureReconfigEnable(false);
      if (projectId) {
        try {
          const reconfigData = await getReconfigNodeSectionFlag(projectId);
          if (reconfigData) {
            setReconfigStatus(reconfigData);
            if(reconfigData?.requirement_reconfig === true){
              setRequirementReconfigEnable(true)
            }if(reconfigData?.architecture_reconfig === true){
              setArchitectureReconfigEnable(true)
            }
          }

        } catch (error) {
          }
      }
    }

    loadReconfigStatus();

  }, [currentTaskId, taskStatusUpdate, projectId]);

  const handleCancelTask = async () => {
    try {
      if (!currentTaskId) {
        //
        return;
      }
      let response = await cancelTask(currentTaskId);
      setCurrentTaskId(null);
    } catch (error) {
      
    }
  };

  const handleCancelTaskDetails = async () => {
    try {
      if (!currentTaskDetailsId) {
        //
        return;
      }
      setCurrentTaskDetailsId(null);
    } catch (error) {
      
    }
  };



  const isAutoConfigInProgress = taskStatusUpdate === "In Progress" || taskStatusUpdate === "in-progress" || taskStatusUpdate === "in_progress";

  return (
    <ExecutionContext.Provider
      value={{
        activeTask,
        setActiveTask,
        setCurrentTaskId,
        setConfigStatus,
        configStatus,
        isConnected,
        setIsConnected,
        setIsNodeType,
        currentTaskDetailsId,
        setCurrentTaskDetailsId,
        handleCancelTask,
        handleCancelTaskDetails,
        pauseTask,
        checkForActiveTask,
        currentTaskDetails,
        confirmAndDelete,
        cancelDelete,
        isDeleting,
        isDeleteModalOpen,
        setIsDeleteModalOpen,
        isDropdownOpen,
        currentTaskId,
        isLoading,
        setIsLoading,
        setConfiglabel,
        configLabel,
        setTaskStatusUpdate,
        taskStatusUpdate,
        setExtractTaskId,
        extractTaskId,
        setExtract,
        extract,
        taskStatusInfo,
        lastTime,
        enableReconfig,
        reconfigApprovedOrRejected,
        setReconfigApprovedOrRejected,
        autoNavigateEnabled,
        setAutoNavigateEnabled,
        reconfigStatus,
        setReconfigStatus,
        isAutoConfigInProgress,
        setEnableReconfig,
        architectureReconfigEnable,
        requirementReconfigEnable,
        setRequirementReconfigEnable,
      }}
    >
    {children}
    </ExecutionContext.Provider>
  );
};

export { ExecutionProvider, ExecutionContext };
