import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { 
  ChevronRight, 
  ChevronLeft, 
  ChevronRightCircle,
  Inbox,
  LayoutList,
  ClipboardPen,
  Box,
  Code2,
  Network,
  FileText
} from 'lucide-react';
import { TreeNodeProps, FileTreeProps } from '@/types/reconfig';

const styles = `
  .tree-node-container {
    position: relative;
    width: 100%;
  }

  .tree-node-content {
    display: flex;
    align-items: center;
    position: relative;
    padding: 4px 0px;
    width: 100%;
    z-index: 20;
  }

  .truncated-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 210px;
  }

  .file-tree-container {
    height: 100%;
    width: 100%;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #f9fafb, #f3f4f6);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: visible;
    position: relative;
  }

  .file-tree-header {
    flex-shrink: 0;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 12px 12px;
    position: relative;
    z-index: 10;
  }

  .file-tree-content {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 0px 16px 20px 24px;
    font-size: 14px;
    position: relative;
  }

  .file-tree-content::-webkit-scrollbar {
    display: none;
  }

  .project-node {
    margin-top: 5px !important;
  }

  .vertical-line {
    position: absolute;
    left: 20px;
    width: 2px;
    background-color: hsl(var(--primary));
    z-index: 0;
    top: 0.3125rem;
    bottom: 20px;
  }

  .subsection-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 56px;
    padding: 2px 8px;
    font-size: 10px;
    font-weight: 600;
    color: hsl(var(--primary));
    background: #eff6ff;
    border: none;
    border-radius: 12px;
    box-shadow: none;
    transition: background 0.2s ease, color 0.2s ease;
    z-index: 10;
  }

  .subsection-badge:hover {
    background: #bfdbfe;
    color: hsl(var(--primary));
  }

  .badge-ellipsis {
    color: #6b7280;
    font-size: 10px;
    margin-right: 4px;
  }

  .basic-tooltip {
    position: fixed;
    background: #333;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    white-space: nowrap;
    pointer-events: none;
  }
`;

const isSystemContextOrChild = (nodeType: string, parentType?: string): boolean => {
  const systemContextTypes = ['systemcontext', 'container', 'component', 'design', 'interface'];
  return (
    systemContextTypes.includes(nodeType.toLowerCase()) || 
    (parentType?.toLowerCase() === 'systemcontext' && systemContextTypes.includes(nodeType.toLowerCase()))
  );
};

const TreeNode: React.FC<TreeNodeProps> = ({ node, level, onSelect, selectedNodeId, fetchNodeVersions }) => {
  const [isExpanded, setIsExpanded] = useState(isSystemContextOrChild(node.type));
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipTimeout, setTooltipTimeout] = useState<NodeJS.Timeout | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const hasChildren = node?.children && node.children.length > 0;
  const isSelected = selectedNodeId === node?.id;
  const isHighlighted = selectedNodeId === node?.id || (!selectedNodeId && node?.type === "project");
  const isWorkitem = node.type?.toLowerCase() === 'workitem';

  const handleClick = async () => {
    if (node?.id) {
      onSelect(node.id);
     
      await fetchNodeVersions(node.id, node.type);
    }
    if (hasChildren) setIsExpanded(!isExpanded);
  };

  const handleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleMouseEnter = () => {
    const timeout = setTimeout(() => {
      setShowTooltip(true);
    }, 500);
    setTooltipTimeout(timeout);
  };

  const handleMouseLeave = () => {
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
    }
    setShowTooltip(false);
  };

  useEffect(() => {
    if (node?.type === "project" && !selectedNodeId) {
      onSelect(node.id);
      fetchNodeVersions(node.id, node.type);
    }
  }, [node, selectedNodeId, onSelect, fetchNodeVersions]);

  useEffect(() => {
    return () => {
      if (tooltipTimeout) clearTimeout(tooltipTimeout);
    };
  }, [tooltipTimeout]);

  const getIconForType = (type: string | undefined) => {
    switch (type?.toLowerCase()) {
      case 'project': return Inbox;
      case 'workitem': return LayoutList;
      case 'requirement': case 'epic': case 'userstory': return ClipboardPen;
      case 'architecture': case 'functional': case 'nonfunctional': return Box;
      case 'systemcontext': return Network;
      case 'container': return Box;
      case 'component': return Box;
      case 'design': return Box;
      case 'interface': return Box;
      case 'codegen': return Code2;
      case 'documentation': case 'prd': case 'sad': return FileText;
      default: return Box;
    }
  };

  const getIconSizeClass = (nodeLevel: number) => {
    return nodeLevel > 0 ? "w-5 h-5" : "w-6 h-6";
  };

  const getChevronLeftOffset = (nodeLevel: number) => {
    return nodeLevel > 0 ? "-left-6" : "-left-4";
  };

  const getMarginClass = (nodeLevel: number) => {
    return nodeLevel > 0 ? "ml-2" : "ml-4";
  };

  const getIndentationClass = (childLevel: number) => {
    if (node.type?.toLowerCase() === 'epic' && childLevel === level + 1) {
      return 'ml-0';
    }
    return 'ml-6';
  };

  const shouldShowBadge = () => {
    return (
      (node.type?.toLowerCase() === 'epic' && level > 0) || 
      (node.type?.toLowerCase() === 'userstory' && level > 1)
    );
  };

  const shouldShowEllipsis = () => false;

  const getBadgeLabel = () => {
    switch (node.type?.toLowerCase()) {
      case 'epic': return 'Epic';
      case 'userstory': return 'User Story';
      default: return '';
    }
  };

  const getTooltipPosition = () => {
    if (tooltipRef.current) {
      const rect = tooltipRef.current.getBoundingClientRect();
      return {
        top: rect.bottom + window.scrollY + 4,
        left: rect.left + window.scrollX,
      };
    }
    return { top: 0, left: 0 };
  };

  if (!node || !node.name) return null;

  return (
    <div className={`tree-node-container relative ${node.type === 'project' ? 'project-node' : ''}`}>
      <div 
        className={`tree-node-content flex items-center group ${hasChildren ? 'cursor-pointer' : ''}`} 
        onClick={handleClick}
      >
        <div className="relative flex items-center w-10">
          {hasChildren && (
            <ChevronRight
              onClick={handleExpand}
              className={`absolute ${getChevronLeftOffset(level)} w-4 h-4 text-gray-600 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
            />
          )}
          <div className="relative z-[1] bg-white p-2 rounded-full border border-gray-200 shadow-sm">
            {React.createElement(getIconForType(node.type), { className: getIconSizeClass(level), color: '#6B7280' })}
          </div>
        </div>
        <div className={`${getMarginClass(level)} flex-1`}>
          <div className="flex items-center justify-between relative">
            <div 
              className="flex items-center space-x-1 flex-shrink-0 relative"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              ref={tooltipRef}
            >
              <h4 
                className={`truncated-title typography-body-sm font-weight-medium ${isHighlighted ? 'text-primary' : 'text-gray-900'} hover:text-primary transition-colors`}
                style={isWorkitem ? { maxWidth: '300px' } : undefined}
              >
                {node.name}
              </h4>
            </div>
            <div className="flex items-center gap-2">
              {shouldShowBadge() && (
                <>
                  {shouldShowEllipsis() && <span className="badge-ellipsis">...</span>}
                  <span className="subsection-badge">
                    {getBadgeLabel()}
                  </span>
                </>
              )}
              {(node.count || node.count === 0) && !shouldShowBadge() && (
                <span className="px-1.5 py-0.5 typography-caption text-gray-500 bg-gray-100 rounded-full">{node.count}</span>
              )}
            </div>
          </div>
          {node.description && <p className="text-gray-600 mt-1 typography-caption">{node.description}</p>}
        </div>
      </div>
      
      {hasChildren && isExpanded && (
        <div className={`${getIndentationClass(level + 1)} space-y-2 mt-1 overflow-visible transition-all duration-500`}>
          {node.children?.map((child, index) => (
            <TreeNode
              key={index}
              node={child}
              level={level + 1}
              onSelect={onSelect}
              selectedNodeId={selectedNodeId}
              fetchNodeVersions={fetchNodeVersions}
            />
          ))}
        </div>
      )}

      {showTooltip && tooltipRef.current && createPortal(
        <div 
          className="basic-tooltip"
          style={{
            top: getTooltipPosition().top,
            left: getTooltipPosition().left,
          }}
        >
          {node.name}
        </div>,
        document.body
      )}
    </div>
  );
};

const FileTree: React.FC<FileTreeProps> = ({
  data,
  onSelect,
  selectedNodeId,
  fetchNodeVersions,
  isCollapsed,
  onToggleCollapse,
  projectName,
}) => {
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  useEffect(() => {
    const styleSheet = document.createElement("style");
    styleSheet.innerText = styles;
    document.head.appendChild(styleSheet);
    return () => styleSheet.remove();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (data) setLastRefreshed(new Date());
  }, [data]);

  const calculateDuration = (startTime: string | number | Date, endTime: string | number | Date) => {
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end.getTime() - start.getTime();
      const seconds = Math.floor(diffMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      const weeks = Math.floor(days / 7);
      const years = Math.floor(days / 365.25);
      return '';
    } catch (error) {
      
      return '';
    }
  };

  const getTimeAgo = () => calculateDuration(lastRefreshed, currentTime);

  if (isCollapsed) {
    return (
      <div 
        className="h-full w-14 bg-gray-50 flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-colors rounded-l-lg" 
        onClick={onToggleCollapse}
      >
        <ChevronRightCircle className="w-6 h-6 text-gray-600" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="file-tree-container">
        <div className="file-tree-header flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button 
              onClick={onToggleCollapse} 
              className="hover:bg-gray-200 rounded-md transition-colors" 
              aria-label="Collapse panel"
            >
              <ChevronLeft className="w-5 h-5 text-gray-600" />
            </button>
            <h4 className="truncated-title typography-body font-weight-semibold text-gray-700">{projectName}</h4>
          </div>
          <div className="flex items-center gap-3">
            <span className="typography-caption text-gray-500">{getTimeAgo()}</span>
          </div>
        </div>
        <div className="file-tree-content p-4 text-gray-500">
          No project data available.
        </div>
      </div>
    );
  }

  const flattenedData = data.flatMap((node) => {
    const projectNode = { ...node, children: undefined };
    const childrenNodes = node.children || [];
    return [projectNode, ...childrenNodes];
  });

  return (
    <div className="file-tree-container">
      <div className="file-tree-header flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button 
            onClick={onToggleCollapse} 
            className="hover:bg-gray-200 rounded-md transition-colors" 
            aria-label="Collapse panel"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600" />
          </button>
          <h3 className="truncated-title typography-body font-weight-semibold text-gray-700">{projectName}</h3>
        </div>
        <div className="flex items-center gap-3">
          <span className="typography-caption text-gray-500">{getTimeAgo()}</span>
        </div>
      </div>
      <div className="file-tree-content px-5 py-5">
        <div className="relative space-y-4">
          <div className="vertical-line" />
          {flattenedData.map((node, index) => (
            <TreeNode
              key={index}
              node={node}
              level={0}
              onSelect={onSelect}
              selectedNodeId={selectedNodeId}
              fetchNodeVersions={fetchNodeVersions}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FileTree;