"use client";

import React, { useState, useEffect, useRef, useContext } from "react";
import { useParams } from "next/navigation";
import { X, Check, ChevronRight  } from "lucide-react";
import { configureNodeWithAgent, deleteTask, getAvailableOptions,getReconfigNodeStatus } from "@/utils/api";
import { ExecutionContext } from "../Context/ExecutionContext";
import { AlertContext } from "../NotificationAlertService/AlertList";
import EitherOrModal from "./EitherOrModal";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";

interface ConfigOption {
  configure: boolean;
  [key: string]: ConfigOption | boolean;
}

interface ReConfigureModalProps {
  id?: any;
  requirementId?: any;
  type?: string;
  isNodeType: string;
  closeModal: () => void;
  onSubmitSuccess: () => void;
  setLoadingAutoConfigure: (loading: boolean) => void;
}

interface TreeNodeProps {
  option: ConfigOption;
  path: string;
  depth?: number;
}

interface ApiResponse {
  error?: string;
  task_id: string;
}

const ReConfigureModal: React.FC<ReConfigureModalProps> = (props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [allSelected, setAllSelected] = useState(false);
  const [config, setConfig] = useState<Record<string, ConfigOption> | null>(null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [approvalOpen, setApprovalOpen] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [reconfigStatus, setReconfigStatus] = useState<Record<string, boolean>>();

  const modalRef = useRef<HTMLDivElement>(null);
  const { showAlert } = useContext(AlertContext);
  const { setCurrentTaskId, setIsNodeType, currentTaskId ,setConfiglabel} = useContext(ExecutionContext);
  const params = useParams();
  const projectId = params.projectId as string;

  useEffect(() => {
    setIsNodeType(props.isNodeType);
  }, [props.isNodeType, setIsNodeType]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        props.closeModal();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [props.closeModal]);

  useEffect(() => {
    const loadConfig = async () => {
      props.setLoadingAutoConfigure(true);
      const data = await getAvailableOptions(props.type || 'requirement');
      const reconfigResponse = await getReconfigNodeStatus(projectId)
      const status = {
        work_item: reconfigResponse.WorkItemRoot?.some((item:any) => item.reconfig_needed === true) || false,
        requirements: reconfigResponse.RequirementRoot?.some((item:any) => item.reconfig_needed === true) || false,
        epic: reconfigResponse.Epic?.some((item:any) => item.reconfig_needed === true) || false,
        user_story: reconfigResponse.UserStory?.some((item:any) => item.reconfig_needed === true) || false,
        architecture: reconfigResponse.Architecture?.some((item:any) => item.reconfig_needed === true) || false,
        architectural_requirements: reconfigResponse.FunctionalRequirement && reconfigResponse.NonFunctionalRequirement
          ? [
            ...reconfigResponse.FunctionalRequirement,
            ...reconfigResponse.NonFunctionalRequirement
          ].some((item:any) => item.reconfig_needed === true)
          : false,
        system_context: reconfigResponse.SystemContext?.some((item:any) => item.reconfig_needed === true) || false,
        container: reconfigResponse.Container?.some((item:any) => item.reconfig_needed === true) || false,
        components: reconfigResponse.Component?.some((item:any) => item.reconfig_needed === true) || false,
        design: [
            ...(reconfigResponse.Sub_Section || []),
            ...(reconfigResponse.RobustnessTest || []),
            ...(reconfigResponse.PerformanceTest || []),
            ...(reconfigResponse.IntegrationTest || []),
            ...(reconfigResponse.UnitTest || []),
            ...(reconfigResponse.StateDiagram || []),
            ...(reconfigResponse.Sequence || []),
            ...(reconfigResponse.ClassDiagram || []),
            ...(reconfigResponse.Algorithm || []),
            ...(reconfigResponse.Design || [])
        ].some((item: any) => item.reconfig_needed === true)
      };
      setReconfigStatus(status)
      setConfig(data);
      props.setLoadingAutoConfigure(false);
    };

    if (!config) {
      loadConfig();
    }
  }, [config, props.type, props.setLoadingAutoConfigure]);

  const isConfigOption = (value: unknown): value is ConfigOption => {
    return typeof value === 'object' && value !== null && 'configure' in value;
  };

  

  const toggleAllOptions = () => {
    const newState = !allSelected;
    setAllSelected(newState);

    const updateAllOptions = (obj: Record<string, ConfigOption>) => {
      Object.entries(obj).forEach(([key, value]) => {
        if (isConfigOption(value)) {
          value.configure = newState;
          const nestedObj = Object.entries(value).reduce((acc, [k, v]) => {
            if (k !== 'configure' && isConfigOption(v)) {
              acc[k] = v;
            }
            return acc;
          }, {} as Record<string, ConfigOption>);
          updateAllOptions(nestedObj);
        }
      });
    };

    if (config) {
      const updatedConfig = { ...config };
      updateAllOptions(updatedConfig);
      setConfig(updatedConfig);
    }
  };

  const handleToggle = (path: string) => {
    if (!config) return;

    const keys = path.split('.');
    const newConfig = { ...config };

    const toggleState = (obj: Record<string, ConfigOption | boolean>, keys: string[]): void => {
      if (!obj || typeof obj !== 'object') return;

      if (keys.length === 1) {
        const currentObj = obj[keys[0]];
        if (isConfigOption(currentObj)) {
          const newValue = !currentObj.configure;
          currentObj.configure = newValue;

          // Update children
          const updateChildren = (node: Record<string, ConfigOption | boolean>) => {
            Object.entries(node).forEach(([key, value]) => {
              if (key !== 'configure' && isConfigOption(value)) {
                value.configure = newValue;
                updateChildren(value as Record<string, ConfigOption>);
              }
            });
          };
          updateChildren(currentObj);

          // Update parents if needed
          if (!newValue) {
            const updateParents = (currentPath: string[]) => {
              if (currentPath.length <= 1) return;
              const parentPath = currentPath.slice(0, -1);
              let current: ConfigOption | Record<string, ConfigOption> = newConfig;

              for (const key of parentPath) {
                const nextLevel: ConfigOption | undefined = (current as Record<string, ConfigOption>)[key];
                if (isConfigOption(nextLevel)) {
                  current = nextLevel;
                } else {
                  return;
                }
              }

              if (isConfigOption(current)) {
                current.configure = false;
              }
            };
            updateParents(keys);
          }
        }
      } else {
        const nextObj = obj[keys[0]];
        if (isConfigOption(nextObj)) {
          toggleState(nextObj, keys.slice(1));
        }
      }
    };

    toggleState(newConfig, keys);
    setConfig(newConfig);
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!config) return;

    const hasSelectedOptions = Object.values(config).some((item) => {
      if (typeof item === 'object') {
        return item.configure || Object.values(item).some(subItem =>
          typeof subItem === 'object' && subItem.configure
        );
      }
      return false;
    });

    if (!hasSelectedOptions) {
      showAlert("Please select at least one option", "danger");
      return;
    }

    if (config) (config as ConfigOption).configure = Boolean(true);

    setIsSubmitting(true);



    try {
      const response: ApiResponse = await configureNodeWithAgent({
        node_id: props.requirementId || props.id,
        node_type: props.isNodeType,
        user_level: 1,
        project_id: projectId,
        configurations: config,

      });

      if (response.error === "You already have a task in progress. Please cancel the current task to start a new one.") {
        showAlert(response.error, "danger");
        localStorage.setItem("current_task_id", response.task_id);
        setCurrentTaskId(response.task_id);
        setApprovalOpen(true);
        setConfiglabel("auto-config")
      } else {
        setCurrentTaskId(response.task_id);
        setConfiglabel("auto-config")
        props.closeModal();
        props.onSubmitSuccess();
      }
    } catch (error) {
      showAlert("Error configuring node", "danger");
    } finally {
      setIsSubmitting(false);
    }
  };

  const CustomCheckbox: React.FC<{ checked: boolean; onChange: () => void ;disabled?: boolean}> = ({ checked, onChange,disabled = false,  }) => (
    <div
    onClick={ onChange }
    className={`w-5 h-5 rounded-md border-2 flex items-center justify-center transition-all duration-200 
        ${checked ? 'bg-primary border-primary' : 'border-gray-300 hover:border-primary-400 hover:bg-primary-50'}
      `}
    >
      {checked && <Check size={14} className="text-white" />}
    </div>
  );

  const renderOption = (option: ConfigOption, path: string, depth = 0) => {
    if (typeof option !== 'object' || option === null) return null;

    const isHovered = hoveredItem === path;
    const hasChildren = Object.entries(option).some(([key, value]) =>
      typeof value === 'object' && value !== null && key !== 'configure'
    );
    const displayName = path.split('.').pop()?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    // Modified logic to check for false values in reconfigStatus
    const lastPathSegment = path.split('.').slice(-1)[0].toLowerCase().replace(' ', '');
   
    const isInReconfigStatus = reconfigStatus && Object.keys(reconfigStatus).some(
      key => key.toLowerCase() === lastPathSegment
    );
   
    
    const needsReconfig = isInReconfigStatus 
      ? reconfigStatus[Object.keys(reconfigStatus).find(
          key => key.toLowerCase() === lastPathSegment
        )!] === false
      : false;

    return (
      <div key={path} className="flex flex-col">
        <div
          onMouseEnter={() => setHoveredItem(path)}
          onMouseLeave={() => setHoveredItem(null)}
          className={`flex items-center py-1.5 px-3 rounded-lg transition-all duration-200 ${isHovered ? 'bg-gray-50' : ''
            }`}
        >
          <div
            style={{ marginLeft: `${depth * 24}px` }}
            className="flex items-center flex-1 gap-3"
          >
            <CustomCheckbox
              checked={option.configure || false}
              onChange={() => handleToggle(path)}
              disabled={needsReconfig}
            />
            <span className={`transition-colors duration-200 text-gray-700
              ${depth <= 1 ? 'typography-body' : 'typography-body-sm'}  
              ${depth === 0 ? 'font-weight-bold' : ''}
              ${depth === 1 ? 'font-weight-semibold' : ''}
              ${depth === 2 ? 'font-weight-semibold' : ''}
              ${depth === 3 ? 'font-weight-medium' : ''}
            `}>
              {displayName}
            </span>
            {/* {needsReconfig && (
                <div className="flex items-center space-x-2">
                <Info className="w-4 h-4 text-orange-600" />
                <span className="px-2 py-0.5 bg-orange-100 text-orange-600 typography-caption rounded-full">
                Reconfigured
                </span>
                </div>
              )} */}
            {hasChildren && (
              <ChevronRight size={16} className={`text-gray-400 transition-all duration-200 ${isHovered ? 'text-gray-600 transform translate-x-1' : ''
                }`} />
            )}
          </div>
        </div>
        {Object.entries(option).map(([key, value]) => {
          if (typeof value === 'object' && value !== null && key !== 'configure') {
            return renderOption(value as ConfigOption, `${path}.${key}`, depth + 1);
          }
          return null;
        })}
      </div>
    );
  };

  const TreeNode: React.FC<TreeNodeProps> = ({ option, path, depth = 0 }) => {
    if (typeof option !== 'object' || option === null) return null;

    const isHovered = hoveredItem === path;
    const hasChildren = Object.entries(option).some(
      ([key, value]) => typeof value === 'object' && value !== null && key !== 'configure'
    );
    const displayName = path.split('.').pop()?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    const childrenKeys = Object.keys(option).filter(key => key !== 'configure');

    return (
      <div className="relative">
        <div
          className={`flex items-start group ${depth > 0 ? 'ml-6' : ''}`}
          onMouseEnter={() => setHoveredItem(path)}
          onMouseLeave={() => setHoveredItem(null)}
        >
          {/* Vertical connector line from parent */}
          {depth > 0 && (
            <div className="absolute left-[-24px] top-0 bottom-0 w-px bg-gray-200 group-hover:bg-primary-200" />
          )}

          {/* Horizontal connector line to checkbox */}
          {depth > 0 && (
            <div className="absolute left-[-24px] top-1/2 w-6 h-px bg-gray-200 group-hover:bg-primary-200" />
          )}

          <div className={`relative flex items-center py-2 px-3 rounded-lg ${isHovered ? 'bg-gray-50' : ''
            }`}>
            <div className="flex items-center gap-3">
              <CustomCheckbox
                checked={option.configure || false}
                onChange={() => handleToggle(path)}
              />
              <span className={`transition-colors duration-200 text-gray-700
                ${depth === 0 ? 'typography-heading-5' : ''}
                ${depth === 1 ? 'typography-body-sm font-weight-semibold' : ''}
                ${depth >= 2 ? 'typography-body-sm' : ''}
              `}>
                {displayName}
              </span>
              {hasChildren && (
                <ChevronRight size={16} className={`text-gray-400 transition-transform duration-200 ${isHovered ? 'transform translate-x-1' : ''
                  }`} />
              )}
            </div>
          </div>
        </div>

        {/* Render children */}
        <div className="relative">
          {childrenKeys.map(key => {
            if (key === 'configure') return null;
            const childOption = option[key];
            if (typeof childOption === 'object' && childOption !== null) {
              return (
                <TreeNode
                  key={`${path}.${key}`}
                  option={childOption as ConfigOption}
                  path={`${path}.${key}`}
                  depth={depth + 1}
                />
              );
            }
            return null;
          })}
        </div>
      </div>
    );
  };



  if (approvalOpen) {
    return (
      <EitherOrModal
        isOpen={approvalOpen}
        isProcessing={processing}
        onClose={() => setApprovalOpen(false)}
        type="task-approval"
        onAction={async (e: React.FormEvent) => {
          setProcessing(true);
          await deleteTask(currentTaskId, true);
          await handleSubmit(e);
          setProcessing(false);
          props.closeModal();
        }}
        buttons={[
          { name: 'Cancel', className: 'bg-gray-200 text-gray-800 hover:bg-gray-300' },
          { name: 'Overwrite Task', className: 'bg-red-600 text-white hover:bg-red-700' }
        ]}
      />
    );
  }

  if (!config) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
      <div ref={modalRef} className="bg-white rounded-xl shadow-xl w-full max-w-md transform transition-all duration-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="typography-heading-4 font-weight-semibold text-gray-900">Re-Config</h2>
            </div>
            <button
              onClick={props.closeModal}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1 hover:bg-gray-100 rounded-lg"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="px-4 py-4 max-h-[75vh] overflow-y-auto">
            {/* <div className="flex justify-between mb-4">
              <p className="typography-body-sm text-gray-500 mt-1.5">Select items to configure</p>
              <button
                type="button"
                onClick={toggleAllOptions}
                className="typography-body-sm text-primary hover:text-primary-700 font-weight-medium px-3 py-1.5 rounded-lg hover:bg-primary-50 transition-colors"
              >
                {allSelected ? 'Deselect All' : 'Select All'}
              </button>
            </div> */}
            <div className="space-y-1">
              {Object.entries(config).map(([key, value]) =>
                renderOption(value as ConfigOption, key)
              )}
            </div>
          </div>

          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl">
            <div className="flex justify-end gap-3">
              <DynamicButton variant="secondary" onClick={props.closeModal}
                text="Cancel"
              />
              <DynamicButton
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                isLoading={isSubmitting}
                className=""
                text="Submit"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReConfigureModal;