import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { ChevronRight } from "lucide-react";

interface Parent {
    id: string;
    title: string;
    labels: string[];
}

interface BreadCrumbModelProps {
    projectId: string;
    parents?: Parent[];
}

const BreadCrumbModel: React.FC<BreadCrumbModelProps> = ({
    projectId,
    parents = []
}) => {
    const router = useRouter();
    const [availableWidth, setAvailableWidth] = useState<number>(0);
    const breadcrumbRef = useRef<HTMLElement | null>(null);

    const calculateAvailableWidth = () => {
        if (breadcrumbRef.current) {
            const totalWidth = breadcrumbRef.current.clientWidth;
            const iconWidth = Array.from(breadcrumbRef.current.querySelectorAll('svg')).reduce((acc, svg) => acc + svg.clientWidth, 0);
            const newAvailableWidth = totalWidth - iconWidth;
            setAvailableWidth(newAvailableWidth);
        }
    };

    useEffect(() => {
        calculateAvailableWidth();
        window.addEventListener('resize', calculateAvailableWidth);
        return () => {
            window.removeEventListener('resize', calculateAvailableWidth);
        };
    }, []);

    const handleNavigation = (parent: Parent) => {
        const { id, labels } = parent;

        const { buildProjectUrl } = require('@/utils/navigationHelpers');
        if (labels.includes('RequirementRoot')) {
            router.push(buildProjectUrl(projectId, 'requirements'));
        } else if (labels.includes('Epic')) {
            router.push(buildProjectUrl(projectId, `requirements/Epic/${id}`));
        } else if (labels.includes('UserStory')) {
            router.push(buildProjectUrl(projectId, `requirements/UserStory/${id}`));
        } else if (labels.includes('Task')) {
            router.push(buildProjectUrl(projectId, `requirements/Task/${id}`));
        } else if (labels.includes("Architecture") && !labels.includes("ArchitectureRoot")) {
            router.push(buildProjectUrl(projectId, `architecture/high-level/${id}`));
        } else if (labels.includes("ArchitectureRoot") && labels.includes("Architecture")) {
            router.push(buildProjectUrl(projectId, 'architecture/high-level'));
        } else {
            router.push(buildProjectUrl(projectId, 'overview'));
        }
    };

    const getTextWidth = (text: string): number => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');

        if (!context) {
            return 0;
        }

        context.font = '16px Inter, sans-serif';
        const width = context.measureText(text).width;

        return width;
    };

    if (!parents || parents.length === 0) {
        return null;
    }

    return (
        <nav
            className="flex items-center pb-1"
            aria-label="Breadcrumb"
            ref={breadcrumbRef as React.RefObject<HTMLElement>}
        >
            <ol className="flex items-center overflow-hidden space-x-1">
                {parents.map((parent, index) => {
                    const titleWidth = getTextWidth(parent.title);

                    const remainingWidth = availableWidth - parents.slice(0, index).reduce((acc, curr) => acc + getTextWidth(curr.title), 0);

                    const shouldTruncate = index !== 0 && titleWidth > remainingWidth && index !== parents.length - 1;

                    return (

                        <li key={parent.id} className="flex items-center">
                            {index !== 0 && (
                                <ChevronRight className="w-4 h-4 text-gray-400" />
                            )}
                            <BootstrapTooltip title={parent.title} placement="bottom">
                                <button
                                    onClick={() => handleNavigation(parent)}
                                    className={`flex items-center breadcrumb-title hover:text-primary ${index === parents.length - 1 ? 'w-full custom-table-text-ellipsis ' : 'w-20 overflow-hidden whitespace-nowrap text-ellipsis'
                                        }`}
                                    aria-label={`Go to ${parent.title}`}
                                >
                                    <span className="custom-bread-crumb-text-ellipsis">{shouldTruncate ? `${parent.title.slice(0, 13)}...` : parent.title}</span>

                                </button>
                            </BootstrapTooltip>
                        </li>
                    );
                })}
            </ol>
        </nav>
    );
};

export default BreadCrumbModel;