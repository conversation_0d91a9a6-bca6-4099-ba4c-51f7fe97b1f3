"use client"

import { useState, useEffect, useContext, useMemo, useCallback } from "react";
import { Info, CheckCircle, ArrowRight, ArrowLeft, AlertTriangle, ChevronUp, ChevronDown, AlertCircle, RefreshCw, Upload } from "lucide-react";
import {
  fetchNodeById,
  createProjectGuidanceFlow,
  updateNodeByPriority
} from "@/utils/api";
import ConfigureModal from "../Modal/ConfigureModel";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import { ProjectSetupContext } from "../Context/ProjectSetupContext";
import { StateContext } from '../Context/StateContext';
import PropertiesRenderer from '../UiMetadata/PropertiesRenderer';
import axios from "axios";
import { getHeadersRaw } from "../../utils/api";
import { fetchLowLevelDesignDetails } from "@/utils/architectureAPI";
import TableComponent from "@/components/SimpleTable/table";
import EmptyStateView from './EmptyStateModal';
import en from "@/en.json"
import { Code, Braces, Layers, Share2, GitBranch, Workflow } from 'lucide-react';
import { ExecutionContext } from "../Context/ExecutionContext";
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import dynamic from "next/dynamic";
import { buildProjectUrl } from "@/utils/navigationHelpers";
const NoSSR = dynamic(() => import("../../../src/components/Chart/MermaidChart"), { ssr: false });

// Define discussion types and their sequence
const DISCUSSION_TYPES = [
  "design_details",
  "behavior",
  "component_interactions",
  "class_diagrams"
];

// Mapping of discussion types to their configuration state properties
const CONFIG_STATE_MAPPING = {
  "design_details": "configuration_state",
  "behavior": "behavior_config_state",
  "component_interactions": "component_interactions_config_state",
  "class_diagrams": "class_diagrams_config_state"
};

export default function DesignConfigurationStep({ type, setEnableDesignNode }) {
  const [designs, setDesigns] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { projectId, designId, setDesignId, designData, setDesignData, showConfigModel, setShowConfigModel } = useContext(ProjectSetupContext);
  const [configMethod, setConfigMethod] = useState("discussion");
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [skippedDesigns, setSkippedDesigns] = useState([]);
  const [showSkipNotification, setShowSkipNotification] = useState(false);
  const [showAllSkippedNotification, setShowAllSkippedNotification] = useState(false);
  const [designCount, setDesignCount] = useState(null);
  const [designType, setDesignType] = useState(null);
  const [designDataVal, setDesignDataVal] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showDesignDetails, setShowDesignDetails] = useState(false)
  const [isLoad, setIsLoad] = useState(false)
  const [discussionType, setDiscussionType] = useState({
    design_details: false,
    behavior: false,
    component_interactions: false,
    class_diagrams: false
  });

  // For handling sequential discussions
  const [currentDiscussionIndex, setCurrentDiscussionIndex] = useState(0);
  const [completedDiscussions, setCompletedDiscussions] = useState({});
  const [allDiscussionsComplete, setAllDiscussionsComplete] = useState(false);
  const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");

  const searchParams = useSearchParams();
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);
  const pathname = usePathname();
  const { setIsVertCollapse } = useContext(StateContext);

  // Get current discussion type based on index
  const currentDiscussionType = DISCUSSION_TYPES[currentDiscussionIndex];
  const selectedDesignId = searchParams.get("selectedDesignId");

  // Check if all configuration states are "configured"
  const checkAllConfigStateConfigured = useCallback((designProperties) => {
    if (!designProperties) return { allConfigured: false };

    const designDetailsConfigured = designProperties.configuration_state === "configured";
    const behaviorConfigured = designProperties.behavior_config_state === "configured";
    const componentInteractionsConfigured = designProperties.component_interactions_config_state === "configured";
    const classDiagramsConfigured = designProperties.class_diagrams_config_state === "configured";

    return {
      allConfigured: designDetailsConfigured && behaviorConfigured &&
        componentInteractionsConfigured && classDiagramsConfigured,
      designDetailsConfigured,
      behaviorConfigured,
      componentInteractionsConfigured,
      classDiagramsConfigured
    };
  }, []);

  useEffect(() => {
    if (designDataVal && designDataVal.length > 0) {
      const result = checkAllConfigStateConfigured(designDataVal[0]?.properties);

      setAllDiscussionsComplete(result.allConfigured);

      if (!result.allConfigured) {
        // Determine which discussion index to show
        if (!result.designDetailsConfigured) {
          setCurrentDiscussionIndex(0);
        } else if (!result.behaviorConfigured) {
          setCurrentDiscussionIndex(1);
        } else if (!result.componentInteractionsConfigured) {
          setCurrentDiscussionIndex(2);
        } else if (!result.classDiagramsConfigured) {
          setCurrentDiscussionIndex(3);
        }
      }

      // Set discussion types state once
      setDiscussionType({
        design_details: result.designDetailsConfigured,
        behavior: result.behaviorConfigured,
        component_interactions: result.componentInteractionsConfigured,
        class_diagrams: result.classDiagramsConfigured
      });
    }
  }, [designDataVal, checkAllConfigStateConfigured]);




  // Find next unconfigured discussion
  const findNextUnconfiguredDiscussion = useCallback((designProperties) => {
    if (!designProperties) return 0;

    for (let i = 0; i < DISCUSSION_TYPES.length; i++) {
      const configStateKey = CONFIG_STATE_MAPPING[DISCUSSION_TYPES[i]];
      if (designProperties[configStateKey] !== "configured") {
        return i;
      }
    }

    return 0; // Default to first discussion if all are configured
  }, []);

  // Track URL parameters for discussion completion
  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    const isCreatingDesign = searchParams.has("is_creating_Design");
    const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");
    const discussionType = searchParams.get("discussionType");

    if (nodeId && nodeType === "Design") {
      setDesignId(nodeId);
      setDesignType(nodeType);
    }

    // If returning from a discussion
    if (!hasDiscussionParam && !isCreatingDesign && designId && discussionType) {
      // Convert discussionType from URL to internal format if needed
      let internalDiscussionType = discussionType;
      if (discussionType === "specification") {
        internalDiscussionType = "design_details";
      } else if (discussionType === "classdiagram") {
        internalDiscussionType = "class_diagrams";
      }

      // Mark the discussion as completed
      handleDiscussionCompletion(designId, internalDiscussionType);
    }

    // If the discussion parameter was previously present but now removed
    if (!hasDiscussionParam && !isCreatingDesign && designId) {
      // Fetch the updated node data
      const fetchUpdatedDesign = async () => {
        try {
          const updatedDesign = await fetchNodeById(designId, "Design");

          setDesignData(updatedDesign);

          if (updatedDesign) {
            sessionStorage.setItem(
              `openDesignContent-${projectId}-${designId}`,
              "true"
            );

            const allConfigured = checkAllConfigStateConfigured(updatedDesign.properties);

            if (allConfigured) {
              try {
                const result = await createProjectGuidanceFlow(
                  parseInt(projectId),
                  {
                    project_id: parseInt(projectId),
                    step_name: "design_configuration",
                    status: "completed",
                    data: {
                      design_id: parseInt(designId),
                      type: "Design",
                      status: "configured",
                    },
                  }
                );



                // Update designs list
                fetchDesigns();
              } catch (error) {

              }
            }
          }
        } catch (error) {

        }
      };

      fetchUpdatedDesign();
    }
  }, [searchParams, designId, designType, projectId, checkAllConfigStateConfigured]);
  const handlePropertyUpdate = async (key, value) => {
  try {
    const response = await updateNodeByPriority(designDataVal[0]?.id, key, value);

    if (response === "success" || response?.status === "success") {
      // Update local design data
      setDesignDataVal((prev) =>
        prev.map((design, index) =>
          index === 0
            ? {
                ...design,
                properties: {
                  ...design.properties,
                  [key]: value,
                },
              }
            : design
        )
      );

      setDesignData((prev) => ({
        ...prev,
        properties: {
          ...prev.properties,
          [key]: value,
        },
      }));

      showAlert("Content updated successfully", "success");
    } else {
      showAlert("Failed to update content", "error");
    }
  } catch (error) {
    
    showAlert("Failed to update content", "error");
  }
};


  // Fetch design details including configuration states
  const fetchDesignDetails = useCallback(async (designId) => {
    setIsLoad(true)
    if (!designId) return;

    try {
      const response = await fetchLowLevelDesignDetails(designId);
      setDesignDataVal(response);

      if (response && response.length > 0) {
        const designProps = response[0].properties || {};

        // Check if all config states are "configured"
        const allConfigured = checkAllConfigStateConfigured(designProps);
        setAllDiscussionsComplete(allConfigured);
        setEnableDesignNode(true)

        if (!allConfigured) {
          // Find the next unconfigured discussion
          const nextIndex = findNextUnconfiguredDiscussion(designProps);
          setCurrentDiscussionIndex(nextIndex);
        }

        // Update completed discussions based on configuration states
        const updatedCompletedDiscussions = {};

        for (const type of DISCUSSION_TYPES) {
          const configKey = CONFIG_STATE_MAPPING[type];
          updatedCompletedDiscussions[type] = designProps[configKey] === "configured";
        }

        setCompletedDiscussions(prev => ({
          ...prev,
          [designId]: updatedCompletedDiscussions
        }));
      }
    } catch (err) {

    } finally {
      setIsLoad(false)
    }
  }, [checkAllConfigStateConfigured, findNextUnconfiguredDiscussion]);

  // Handle a discussion being completed
  const handleDiscussionCompletion = useCallback((designId, discussionType) => {
    setCompletedDiscussions(prev => {
      const designDiscussions = prev[designId] || {};
      const updatedDesignDiscussions = {
        ...designDiscussions,
        [discussionType]: true
      };

      try {
        createProjectGuidanceFlow(parseInt(projectId), {
          project_id: parseInt(projectId),
          step_name: `design_${discussionType}_completed`,
          status: "completed",
          data: {
            design_id: parseInt(designId),
            type: "Design",
            discussion_type: discussionType,
            status: "configured",
          },
        });
      } catch (error) {
        console.error("Error updating project guidance flow:", error);
      }

      // Return updated state
      return {
        ...prev,
        [designId]: updatedDesignDiscussions
      };
    });

    // Fetch updated design data to get the latest configuration states
    fetchDesignDetails(designId);

    // Move to next discussion if this isn't the last one
    const currentTypeIndex = DISCUSSION_TYPES.indexOf(discussionType);
    if (currentTypeIndex < DISCUSSION_TYPES.length - 1) {
      // Add a slight delay to allow state updates to complete
      setTimeout(() => {
        setCurrentDiscussionIndex(currentTypeIndex + 1);
      }, 300);
    }
  }, [projectId, fetchDesignDetails]);

  useEffect(() => {
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

    }
  }, [currentTaskId, configStatus[currentTaskId], projectId])


  useEffect(() => {
    if (selectedDesignId && designs.length > 0) {
      const index = designs.findIndex(design => design.id.toString() === selectedDesignId);
      if (index !== -1) {
        setCurrentIndex(index);
        setDesignId(designs[index].id);
        setShowDesignDetails(true)
        // Reset to first discussion type
        setCurrentDiscussionIndex(0);
      }
    }
  }, [selectedDesignId, designs]);

  // Get discussion title based on type
  const getDiscussionTitle = (type) => {
    switch (type) {
      case "design_details": return "Design Details";
      case "behavior": return "Design behavior";
      case "component_interactions": return "Component Interactions";
      case "class_diagrams": return "Class Diagrams";
      default: return "Discussion";
    }
  };

  const cleanDescription = (description) => {
    if (!description) return '';

    return description
      .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
      .replace(/\*\*/g, '')        // Remove bold (**text**)
      .replace(/\*/g, '')          // Remove italics (*text*)
      .replace(/`/g, '')           // Remove code ticks (`code`)
      .replace(/\n\n/g, ' ')       // Replace double line breaks with space
      .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
      .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
      .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
      .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
      .trim();                     // Trim extra whitespace
  };

  // Get discussion description based on type
  const getDiscussionDescription = (type) => {
    switch (type) {
      case "design_details":
        return "Discuss the structural and functional aspects of the design, including layout, patterns, and key responsibilities.";
      case "behavior":
        return "Analyze how the design behaves under different scenarios, covering state transitions, data flow, and user interactions.";
      case "component_interactions":
        return "Detail how components communicate with each other, including event handling, data exchange, and integration points.";
      case "class_diagrams":
        return "Review and validate the class diagrams to ensure proper structure, relationships, and alignment with the overall design.";
      default:
        return "Configure this design component.";
    }
  };

  const fetchDesigns = async () => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/architecture/architecture_leaf_nodes/${projectId}`,
        {
          headers: getHeadersRaw()
        }
      );

      if (response.status === 200) {
        const data = response.data;
        const storedSkips = JSON.parse(
          sessionStorage.getItem("skippedDesigns") || "[]"
        );
        const filteredDesigns = (data || []).filter(
          (design) => !storedSkips.includes(design.id)
        );
        setSkippedDesigns(storedSkips);
        setDesigns(filteredDesigns);
        setDesignCount(data.length);

        // Fetch details for the current design if it exists
        if (filteredDesigns.length > 0 && filteredDesigns[currentIndex]) {
          fetchDesignDetails(filteredDesigns[currentIndex].id);
        }
      }
    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchDesigns();
    }
  }, [projectId]);

  // useEffect(() => {
  //   // When current design changes, fetch its details
  //   if (designs.length > 0 && currentIndex < designs.length) {
  //     const design = designs[currentIndex];
  //     if (design?.id) {
  //       fetchDesignDetails(design.id);
  //     }
  //   }
  // }, [designs, currentIndex, fetchDesignDetails]);

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleConfigureClick = () => {
    setConfigMethod("auto");
    setConfigureModel(true);
  };

  const handleSkipAll = () => {
    if (designs.length === 0) return;

    const allIdsToSkip = designs.map((design) => design.id);
    const updatedSkipped = [...skippedDesigns, ...allIdsToSkip];
    sessionStorage.setItem("skippedDesigns", JSON.stringify(updatedSkipped));
    setSkippedDesigns(updatedSkipped);
    setShowAllSkippedNotification(true); // Show notification

    setCurrentIndex(designs.length - 1); // Show the last design before clearing
    setTimeout(() => {
      setDesigns([]); // Clear all designs after displaying last one
      setCurrentIndex(0);
    }, 50);

    // Log skipped designs to MongoDB
    try {
      createProjectGuidanceFlow(parseInt(projectId), {
        project_id: parseInt(projectId),
        step_name: "designs_skipped",
        status: "completed",
        data: {
          skipped_designs: allIdsToSkip.map((id) => parseInt(id)),
          type: "Design",
          status: "skipped",
        },
      })
        .then((result) => {

        })
        .catch((error) => {

        });
    } catch (error) {

    }
  };

  const handleSkip = () => {
    if (!designs[currentIndex]) return;

    const isDesignContentOpen = sessionStorage.getItem(
      `openDesignContent-${projectId}-${designs[currentIndex].id}`
    ) === "true";

    if (isDesignContentOpen) {
      // Just move to the next design when flag is true
      setCurrentIndex((prev) => Math.min(prev + 1, designs.length - 1));
      // Reset to first discussion for the new design
      setCurrentDiscussionIndex(0);
    } else {
      const designId = designs[currentIndex].id;
      const updatedSkipped = [...skippedDesigns, designId];
      sessionStorage.setItem("skippedDesigns", JSON.stringify(updatedSkipped));
      setSkippedDesigns(updatedSkipped);
      setShowSkipNotification(true);

      // Log the skipped design to MongoDB
      try {
        createProjectGuidanceFlow(parseInt(projectId), {
          project_id: parseInt(projectId),
          step_name: "design_skipped",
          status: "completed",
          data: {
            design_id: parseInt(designId),
            type: "Design",
            status: "skipped",
          },
        })
          .then((result) => {

          })
          .catch((error) => {

          });
      } catch (error) {

      }

      setTimeout(() => {
        const newDesigns = designs.filter((design) => design.id !== designId);
        setDesigns(newDesigns);
        setCurrentIndex((prev) => Math.min(prev, newDesigns.length - 1));
        setShowSkipNotification(false); // Hide the badge after 5 seconds
      }, 5000);
    }
  };

  const handlePrevious = () => {
    setCurrentIndex((prev) => {
      const newIndex = Math.max(0, prev - 1);
      return newIndex;
    });
  };

  const handleNext = () => {
    setCurrentIndex((prev) => {
      const newIndex = Math.min(prev + 1, designs.length - 1);
      return newIndex;
    });
  };

  const currentDesign = designs[currentIndex];

  // Update designId when currentDesign changes
  useEffect(() => {
    if (currentDesign?.id) {
      setDesignId(currentDesign.id);
    }
  }, [currentDesign, setDesignId]);

  const isFirstDesign = currentIndex === 0;
  const isLastDesign = currentIndex === designs.length - 1 || designs.length === 0;

  // Check if a specific discussion is completed based on its configuration state
  const isDiscussionCompleted = useCallback((designId, discussionType) => {
    if (!designDataVal || designDataVal.length === 0) return false;

    const configKey = CONFIG_STATE_MAPPING[discussionType];
    return designDataVal[0]?.properties?.[configKey] === "configured";
  }, [designDataVal]);

  const handleUpdateDesign = (id) => {
    setConfigMethod("discussion");
    if (!id) {
      return null;
    }

    const newSearchParams = new URLSearchParams(searchParams.toString());

    let updatedDiscussionType = currentDiscussionType;
    if (currentDiscussionType === "design_details") {
      updatedDiscussionType = "specification";
    } else if (currentDiscussionType === "class_diagrams") {
      updatedDiscussionType = "classdiagram";
    }

    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", designDataVal[0]?.id || id);
    newSearchParams.set("node_type", "Design");
    newSearchParams.set("discussionType", updatedDiscussionType);
    newSearchParams.set("is_creating_Design", "true");

    if (pathname.includes('/architecture/design/')) {
      // If already on the correct URL format, just append search params
      router.push(`${pathname}?${newSearchParams.toString()}`);
    } else {
      // Navigate to the correct URL format with the design ID
      router.push(`${buildProjectUrl(projectId, `architecture/design/${id}`)}?${newSearchParams.toString()}`);
    }
  };
  // Table headers for design list view
  const tableHeaders = [
    { key: 'id', label: 'ID' },
    { key: 'title', label: 'Title' },
    // { key: 'type', label: 'Type' },
    { key: 'containerTitle', label: 'Container' },
    { key: 'description', label: 'Description' },
  ];

  // Prepare table data for the designs list
  const tableData = useMemo(() => {
    return designs.map(design => ({
      id: design.id,
      title: design.title || 'Untitled Design',
      // type: design.type || 'Design',
      containerTitle: design.container?.title || 'Unknown Container',
      description: (cleanDescription(design.description) || 'No description available').substring(0, 100) + (design.description?.length > 100 ? '...' : '')
    }));
  }, [designs]);

  // Notification components
  function DesignSkippedNotification() {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <div className="border border-cyan-200 bg-cyan-50 rounded-lg p-4 flex items-start gap-3">
          <div className="text-gray-500">
            <Info size={24} />
          </div>
          <div>
            <p className="text-gray-800 font-weight-medium">
              Design skipped: You can create specifications for {currentDesign?.title || "this design"} later in the workflow
              manager.
            </p>
          </div>
        </div>
      </div>
    );
  }

  function AllDesignsSkippedNotification() {
    return (
      <div className="border-l-4 border-green-500 bg-green-50 p-4 mb-8 flex items-start gap-3">
        <div className="text-gray-500">
          <Info size={24} />
        </div>
        <div>
          <p className="text-gray-800">
            All designs skipped: You can create specifications for all designs later
            in the workflow manager.
          </p>
        </div>
      </div>
    );
  }

  function LoadingSpinner() {
    return (
      <div className="w-full animate-pulse rounded-lg overflow-hidden border border-gray-200">
        {/* Table header */}
        <div className="flex items-center p-4 border-b bg-gray-50 rounded-t-lg">
          <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
          <div className="flex-1 grid grid-cols-5 gap-4">
            <div className="h-6 bg-gray-200 rounded col-span-2"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
          </div>
        </div>

        {/* Table rows */}
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center p-4 border-b hover:bg-gray-50">
            <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
            <div className="flex-1 grid grid-cols-5 gap-4">
              <div className="col-span-2">
                <div className="h-5 bg-gray-200 rounded mb-2 w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="flex items-center">
                <div className="h-6 w-16 bg-gray-200 rounded"></div>
              </div>
              <div className="h-5 bg-gray-200 rounded w-1/2"></div>
              <div className="h-5 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
        ))}

        {/* Table footer / pagination */}
        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-b-lg">
          <div className="h-8 w-32 bg-gray-200 rounded"></div>
          <div className="flex space-x-2">
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  function SuccessBadge({ discussionType }) {
    const isLastDiscussion = currentDiscussionIndex === DISCUSSION_TYPES.length - 1;

    return (
      <div className="max-w-3xl mx-auto p-4">
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-emerald-500 rounded-lg p-5 shadow-md">
          <div className="flex items-start gap-4">
            <div className="bg-green-500 text-white p-2 rounded-full flex-shrink-0">
              <CheckCircle size={24} />
            </div>
            <div>
              <h3 className="font-weight-bold typography-body-lg text-gray-800 flex items-center gap-2">
                {getDiscussionTitle(discussionType)} completed successfully!
                <span className="inline-flex px-2 py-1 bg-green-100 text-green-800 typography-caption font-weight-medium rounded-full">
                  Complete
                </span>
              </h3>

              {/* Next Discussion Button */}
              {!isLastDiscussion && (
                <div className="mt-4">
                  <button
                    onClick={() => setCurrentDiscussionIndex(prev => prev + 1)}
                    className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-600 transition flex items-center"
                  >
                    Continue to {getDiscussionTitle(DISCUSSION_TYPES[currentDiscussionIndex + 1])}
                    <ArrowRight size={16} className="ml-2" />
                  </button>
                </div>
              )}

              {/* Check if there's any unconfigured discussion left */}
              {designDataVal && designDataVal.length > 0 && DISCUSSION_TYPES.some(type => {
                const configKey = CONFIG_STATE_MAPPING[type];
                return designDataVal[0]?.properties?.[configKey] !== "configured";
              }) ? (
                <div className="mt-2">
                  <p className="text-gray-600">
                    Please complete the remaining discussions to see all design properties:
                  </p>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {DISCUSSION_TYPES.map(type => {
                      const configKey = CONFIG_STATE_MAPPING[type];
                      const isConfigured = designDataVal[0]?.properties?.[configKey] === "configured";

                      if (!isConfigured) {
                        return (
                          <button
                            key={type}
                            onClick={() => setCurrentDiscussionIndex(DISCUSSION_TYPES.indexOf(type))}
                            className="px-3 py-1 bg-primary-100 text-primary-800 rounded-full typography-body-sm hover:bg-primary-200"
                          >
                            {getDiscussionTitle(type)}
                          </button>
                        );
                      }
                      return null;
                    })}
                  </div>
                </div>
              ) : (
                <p className="text-gray-600 mt-2">
                  All discussions completed! You can now move on to the next Design.
                </p>
              )}

              <p className="text-gray-600 mt-1">
                Once the project creation flow is complete, you will be able to
                view the Design Specification details in the{" "}
                <span className="font-weight-semibold">Architecture</span> tab.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  function AllDiscussionsCompleteBadge() {
    const allConfigured = designDataVal && designDataVal.length > 0 &&
      checkAllConfigStateConfigured(designDataVal[0]?.properties);

    return (
      <div className="w-full mx-auto p-4 -mt-6">
        <div className="bg-white p-5 shadow-sm">
          {designDataVal && designDataVal.length > 0 && (
            <div>
              <PropertiesRenderer
                properties={designDataVal[0].properties || {}}
                metadata={designDataVal[0].ui_metadata || {}}
                to_skip={[
                  "Type",
                  "Title",
                  "configuration_state",
                  "behavior_config_state",
                  "component_interactions_config_state",
                  "class_diagrams_config_state"
                ]}
                onUpdate={handlePropertyUpdate}
              />


              {allConfigured && (
                <div className="mt-8">
                  {/* Design Behavior Section */}
                  <DesignBehaviorSection details={designDataVal[0]} />


                  {/* Component Interactions Section */}
                  <ComponentInteractionsSection details={designDataVal[0]} />

                  {/* Class Diagrams Section */}
                  <ClassDiagramsSection details={designDataVal[0]} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  function Skipped_Designs() {
    return (
      <div className="p-4 max-h-[40vh] overflow-y-auto bg-white">
        <div className="mx-auto w-full max-w-2xl">
          <div className="bg-gradient-to-r from-primary-50 to-cyan-50 rounded-lg shadow p-4 mb-4 border-l-4 border-cyan-500">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-green-500 text-white p-2 rounded-full">
                <CheckCircle size={24} />
              </div>
            </div>

            <h2 className="typography-heading-4 font-weight-semibold text-center text-gray-800 mb-2">
              All Designs Skipped Successfully
            </h2>

            <p className="text-gray-600 text-center typography-body-sm mb-4">
              You have chosen to skip all designs for now. You can create specifications
              later in the workflow manager.
            </p>

            <div className="border-t border-gray-200 pt-4">
              <h3 className="typography-body-sm font-weight-medium text-gray-700 mb-2">
                What is next?
              </h3>
              <ul className="space-y-2 typography-body-sm">
                <li className="flex items-start gap-2">
                  <div className="bg-cyan-100 text-cyan-600 p-1 rounded-full">
                    <ArrowRight size={14} />
                  </div>
                  <span>Your project setup will continue to the next step</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="bg-cyan-100 text-cyan-600 p-1 rounded-full">
                    <ArrowRight size={14} />
                  </div>
                  <span>
                    You can find your designs in the Architecture tab after
                    project creation
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="bg-cyan-100 text-cyan-600 p-1 rounded-full">
                    <ArrowRight size={14} />
                  </div>
                  <span>
                    The workflow manager will guide you through creating specifications
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  function LoadingSkeleton() {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-8 animate-pulse">
        <div className="flex justify-between items-center mb-4">
          <div className="h-6 w-24 bg-gray-200 rounded"></div>
          <div className="h-8 w-20 bg-gray-200 rounded"></div>
        </div>
        <div className="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>
        <div className="flex space-x-2 mb-6">
          <div className="h-6 w-16 bg-gray-200 rounded-full"></div>
          <div className="h-6 w-20 bg-gray-200 rounded-full"></div>
        </div>
        <div className="mb-6">
          <div className="h-6 w-1/2 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
        </div>
        <div className="flex flex-col md:flex-row gap-6 justify-center md:justify-start">
          <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
              <div className="h-6 w-32 bg-gray-200 rounded"></div>
            </div>
            <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-4/6 bg-gray-200 rounded"></div>
          </div>
          <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
              <div className="h-6 w-32 bg-gray-200 rounded"></div>
            </div>
            <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-4/6 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const handleBackToDesignList = () => {

    setShowDesignDetails(false);
    const newSearchParams = new URLSearchParams(searchParams);

    if (newSearchParams.has("selectedDesignId")) {

      newSearchParams.delete("selectedDesignId");
    }
    router.replace(`${pathname}?${newSearchParams.toString()}`, { scroll: false });

  };

  const TestCaseCard = ({ testCase }) => {
    const getTypeIcon = (type) => {
      switch (type.toLowerCase()) {
        case 'positive':
          return <CheckCircle className="text-green-500 mr-2" />;
        case 'negative':
          return <AlertTriangle className="text-red-500 mr-2" />;
        default:
          return <Info className="text-primary mr-2" />;
      }
    };

    return (
      <div className="bg-white shadow-md rounded-lg border border-gray-200 p-5 mb-4 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center mb-3">
          {getTypeIcon(testCase.Type)}
          <h4 className="typography-body-lg font-weight-semibold text-gray-800 truncate">
            {testCase.Title}
          </h4>
        </div>
        <div className="space-y-2 text-gray-700">
          <div className="flex items-start">
            <span className="font-weight-medium text-gray-600 w-36 shrink-0">
              Type:
            </span>
            <span className="text-gray-800">{testCase.Type}</span>
          </div>
          <div className="flex items-start">
            <span className="font-weight-medium text-gray-600 w-36 shrink-0">
              Description:
            </span>
            <span className="text-gray-800">{testCase.Description}</span>
          </div>
          <div className="flex items-start">
            <span className="font-weight-medium text-gray-600 w-36 shrink-0">
              Expected Result:
            </span>
            <span className="text-gray-800">{testCase.ExpectedResult}</span>
          </div>
        </div>
      </div>
    );
  };

  const handleRefresh = () => {
    if (currentDesign?.id) {
      setIsLoad(true);
      fetchDesignDetails(currentDesign.id)
        .finally(() => {
          setIsLoad(false);
        });
    }
  };

  const TestCasesSection = ({ details }) => {
    const testSections = [
      {
        title: "Unit Test Cases",
        tests: details?.UnitTest,
        emptyMessage: "No Unit Test Cases found",
        id: "unit-test"
      },
      {
        title: "Integration Test Scenarios",
        tests: details?.IntegrationTest,
        emptyMessage: "No Integration Test Scenarios found",
        id: "integration-test"
      },
      {
        title: "Performance Test Cases",
        tests: details?.PerformanceTest,
        emptyMessage: "No Performance Test Cases found",
        id: "performance-test"
      },
      {
        title: "Fault Tolerance Test Cases",
        tests: details?.RobustnessTest,
        emptyMessage: "No Fault Tolerance Test Cases found",
        id: "robustness-test"
      }
    ];

    return (
      <div className="container mx-auto px-4 py-6">
        <div>
          <div className="flex items-center justify-between mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-gray-900">
              Design Test Cases
            </h2>
          </div>

          {testSections.map((section) => (
            <div id={section.id} key={section.id} className="mb-8">
              <h3 className="typography-body-lg font-weight-semibold text-gray-800 mb-4 border-b pb-2">
                {section.title}
              </h3>
              {section.tests?.length ? (
                <div className="space-y-4">
                  {section.tests.map((testCase, index) => (
                    <TestCaseCard key={index} testCase={testCase} />
                  ))}
                </div>
              ) : (
                <div className="text-gray-500 italic text-center py-4">
                  {section.emptyMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const BehaviorCard = ({ behavior }) => {
    const [expanded, setExpanded] = useState(false);

    const getIcon = (type) => {
      switch (type.toLowerCase()) {
        case 'algorithm':
          return <Code className="text-purple-500 mr-2" />;
        case 'state':
          return <Layers className="text-teal-500 mr-2" />;
        default:
          return <Braces className="text-indigo-500 mr-2" />;
      }
    };

    return (
      <div className="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-lg">
        <div
          className="flex justify-between items-center px-5 py-4 cursor-pointer hover:bg-gray-50"
          onClick={() => setExpanded(!expanded)}
        >
          <div className="flex items-center">
            {getIcon(behavior.Type)}
            <h4 className="typography-body-lg font-weight-semibold text-gray-800">{behavior.Title}</h4>
          </div>
          {expanded ? (
            <ChevronUp className="text-gray-500" size={20} />
          ) : (
            <ChevronDown className="text-gray-500" size={20} />
          )}
        </div>

        {expanded && (
          <div className="px-5 pb-4 space-y-3">
            <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto typography-body-sm text-gray-800 border border-gray-200">
              {behavior.Details}
            </pre>
          </div>
        )}
      </div>
    );
  };

  const DesignBehaviorSection = ({ details }) => {
    const behaviorSections = [
      {
        title: 'Algorithmic Details',
        behaviors: details?.Algorithm,
        emptyMessage: 'No Algorithmic Details Available',
        id: 'algorithm',
      },
    ];

    return (
      <div className="container mx-auto px-4 py-6">
        <div>
          <div className="flex justify-between items-center mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-gray-900">Design Behavior</h2>
          </div>

          {behaviorSections.map((section, sectionIndex) => (
            <div id={section.id} key={sectionIndex} className="mb-8">
              <h3 className="typography-body-lg font-weight-semibold text-gray-800 mb-4 border-b pb-2">
                {section.title}
              </h3>

              {section.behaviors?.length ? (
                <div className="space-y-4">
                  {section.behaviors.map((behavior, behaviorIndex) => (
                    <BehaviorCard key={behaviorIndex} behavior={behavior} />
                  ))}
                </div>
              ) : (
                <div className="text-gray-500 italic text-center py-4">
                  {section.emptyMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const DiagramCard = ({ diagram, type }) => {
    const getIcon = () => {
      switch (type) {
        case 'sequence':
          return <Share2 className="text-primary mr-2" />;
        case 'state':
          return <GitBranch className="text-green-500 mr-2" />;
        default:
          return <Workflow className="text-purple-500 mr-2" />;
      }
    };

    return (
      <div className="bg-white shadow-md rounded-lg p-5 mb-4 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center mb-3">
          {getIcon()}
          <h4 className="typography-heading-4 text-gray-800">{diagram.Title}</h4>
        </div>

        {diagram.Description && (
          <p className="text-gray-600 mb-4">{diagram.Description}</p>
        )}

        <div className="bg-white rounded-md p-3 overflow-x-auto">
          <NoSSR chartDefinition={diagram.Diagram} />
        </div>
      </div>
    );
  };

  const ComponentInteractionsSection = ({ details }) => {
    const diagramSections = [
      {
        title: "Sequence Diagrams",
        diagrams: details?.Sequence,
        type: 'sequence',
        emptyMessage: "No Sequence Diagrams Available",
        id: "sequence"
      },
      {
        title: "State Machine Diagrams",
        diagrams: details?.StateDiagram,
        type: 'state',
        emptyMessage: "No State Machine Diagrams Available",
        id: "state-diagram"
      }
    ];

    return (
      <div className="container mx-auto pr-4 py-6">
        <div>
          <div className="flex justify-between items-center mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-gray-900 flex items-center">
              <Workflow className="mr-3 text-purple-600" />
              Design Component Interactions
            </h2>
          </div>

          {diagramSections.map((section, sectionIndex) => (
            <div id={section.id} key={sectionIndex} className="mb-8">
              <h3 className="typography-body-lg font-weight-semibold text-gray-800 mb-4 border-b pb-2">
                {section.title}
              </h3>

              {section.diagrams?.length ? (
                <div className="space-y-4">
                  {section.diagrams.map((diagram, diagramIndex) => (
                    <DiagramCard
                      key={diagramIndex}
                      diagram={diagram}
                      type={section.type}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-gray-500 italic text-center py-4 bg-gray-50 rounded-md">
                  {section.emptyMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const ClassDiagramCard = ({ diagram }) => {
    return (
      <div className="bg-white shadow-md rounded-lg p-5 mb-4 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center mb-4">
          <Layers className="text-indigo-500 mr-3" />
          <h4 className="typography-heading-4 text-gray-800">{diagram.Title}</h4>
        </div>

        <div className="bg-white rounded-md p-3 overflow-x-auto">
          <NoSSR chartDefinition={diagram.Diagram} />
        </div>
      </div>
    );
  };

  const ClassDiagramsSection = ({ details }) => {
    return (
      <div className="container mx-auto pr-4 py-6">
        <div>
          <div className="flex justify-between items-center mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-gray-900 flex items-center">
              <Layers className="mr-3 text-indigo-600" />
              Design Class Diagrams
            </h2>
          </div>

          {details?.ClassDiagram?.length ? (
            <div className="space-y-4" id="class-diagram">
              {details.ClassDiagram.map((diagram, index) => (
                <ClassDiagramCard
                  key={index}
                  diagram={diagram}
                />
              ))}
            </div>
          ) : (
            <div className="text-gray-500 italic text-center py-4 bg-gray-50 rounded-md">
              No Class Diagrams Available
            </div>
          )}
        </div>
      </div>
    );
  };


  // If type is "design list", display a table of all designs
  if (!showDesignDetails) {
    return (
      <div className="p-6 h-full  overflow-y-auto bg-white">
        <div className="mx-auto w-full">
          <h2 className="typography-body-lg font-weight-semibold text-gray-800 mb-4">
            Design List
          </h2>

          {isLoading ? (
            <LoadingSpinner />
          ) : designs.length > 0 ? (
            <TableComponent
              data={tableData}
              headers={tableHeaders}
              onRowClick={(id) => {
                if (type === "design list") {
                  setShowDesignDetails(true)
                  // Navigate to Design Details with the selected design
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set("stepName", "Design Details");
                  newSearchParams.set("selectedDesignId", id);
                  router.push(`${pathname}?${newSearchParams.toString()}`);
                } else {
                  // For consistency, though we don't expect this in detail view
                  const designIndex = designs.findIndex(design => design.id === id);
                  if (designIndex !== -1) {
                    setCurrentIndex(designIndex);
                    setDesignId(designs[designIndex].id);
                    // Reset to first discussion type
                    setCurrentDiscussionIndex(0);
                  }
                }
              }}
              sortableColumns={{ id: true, title: true, type: true, containerTitle: true }}
              itemsPerPage={10}
              title="Project Designs"
            />
          ) : (
            <EmptyStateView type="designDetails" />
          )}
        </div>
      </div>
    );
  }

  // Default view for the sequential discussions
  return (
    <div className="p-6 h-full  overflow-y-auto px-4 bg-white">
      {showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
        <StatusPanel />
      ) : (
        <>
          <div className="mx-auto w-full">
            {/* Skip Button and Navigation Controls */}
            <div className="flex justify-end mb-2 -mt-6">
              <div className="flex space-x-2">
                <button
                  className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200"
                  onClick={handleBackToDesignList}
                  title="Navigate to Design list"
                >
                  <ArrowLeft size={16} className="mr-2" />
                  Back to Design List
                </button>

                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isFirstDesign ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handlePrevious}
                  disabled={isFirstDesign}
                  title="Click to see previous design"
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Previous
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isLastDesign ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleNext}
                  disabled={isLastDesign}
                  title="Click to see next design"
                >
                  Next
                  <ArrowRight size={16} className="ml-1" />
                </button>
              </div>
              {/* <button
            className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-orange-100 hover:text-orange-700 transition border border-gray-200"
            onClick={handleSkipAll}
            disabled={designs.length === 0}
          >
            <FaForward size={16} className="mr-1" />
            Skip All
          </button> */}
            </div>

            {showAllSkippedNotification && <Skipped_Designs />}

            {/* Design Progress */}
            {!showAllSkippedNotification && designs.length > 0 ? (
              <div className="flex flex-col space-y-2 mb-6">
                <div className="flex justify-between items-center px-4 py-2 bg-gray-100 rounded">
                  <div className="text-gray-700 font-weight-medium">Design Progress:</div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-700">
                      {currentIndex + 1} / {designCount}
                    </span>
                    <div className="w-24 h-2 bg-gray-200 rounded-full">
                      <div
                        className="h-2 bg-orange-500 rounded-full"
                        style={{
                          width: `${((currentIndex + 1) / designCount) * 100}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                </div>


                {/* Discussion Progress */}
                {/* {currentDesign && !allDiscussionsComplete && (
  <div className="flex justify-between items-center px-4 py-2 bg-primary-50 rounded">
    <div className="text-primary-700 font-weight-medium">Discussion Progress:</div>
    <div className="flex items-center space-x-2">
      <span className="text-primary-700">
        {currentDiscussionIndex + 1} / {DISCUSSION_TYPES.length}
      </span>
      <div className="w-24 h-2 bg-primary-100 rounded-full">
        <div
          className="h-2 bg-primary rounded-full transition-all duration-300"
          style={{
            width: `${((currentDiscussionIndex + 1) / DISCUSSION_TYPES.length) * 100}%`,
          }}
        ></div>
      </div>
    </div>
  </div>
)} */}
              </div>
            ) : (
              ""
            )}

            {/* Design Card */}
            {isLoad && showDesignDetails ? (
              <LoadingSkeleton />
            ) :

              currentDesign ? (
                <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                  <div className="flex justify-between items-center mb-4 space-x-4">
                    <div className="flex items-center space-x-4 flex-wrap">
                      <div className="typography-body-sm bg-gray-100 text-gray-500 px-3 py-1 rounded">
                        {`DESIGN-${currentDesign.id}` || "DESIGN-ID"}
                      </div>
                      <h2 className="typography-body-lg font-weight-semibold text-gray-800 ">
                        {currentDesign.title || "No title available"}
                      </h2>
                      <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full typography-body-sm">
                        Design
                      </span>
                      <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                        {currentDesign.container?.title || "Container"}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* {!allDiscussionsComplete && (
                  <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                    {getDiscussionTitle(currentDiscussionType)}
                  </span>
                )} */}

                      {/* Discussion Navigation Buttons */}
                      {/* {!allDiscussionsComplete && (
  <div className="flex justify-between mt-6">
    <button
      onClick={() => setCurrentDiscussionIndex(prev => Math.max(0, prev - 1))}
      disabled={currentDiscussionIndex === 0}
      className={`flex items-center px-4 py-2 rounded ${
        currentDiscussionIndex === 0
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
          : 'bg-primary-50 text-primary hover:bg-primary-100'
      }`}
    >
      <ArrowLeft size={16} className="mr-2" /> Previous Discussion
    </button>
    
    <button
      onClick={() => setCurrentDiscussionIndex(prev => Math.min(DISCUSSION_TYPES.length - 1, prev + 1))}
      disabled={currentDiscussionIndex === DISCUSSION_TYPES.length - 1}
      className={`flex items-center px-4 py-2 rounded ${
        currentDiscussionIndex === DISCUSSION_TYPES.length - 1
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
          : 'bg-primary-50 text-primary hover:bg-primary-100'
      }`}
    >
      Next Discussion <ArrowRight size={16} className="ml-2" />
    </button>
  </div>
)} */}
                      {/* <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-orange-100 hover:text-orange-700 transition border border-gray-200${designDataVal[0]?.properties?.configuration_state === "configured"? "disabled:opacity-50 disabled:cursor-not-allowed":""}`}
                  onClick={handleSkip}
                  disabled={designDataVal[0]?.properties?.configuration_state === "configured"}
                >
                  <FaForward size={16} className="mr-1" />
                  Skip
                </button> */}
                    </div>
                  </div>



                  <div className="flex space-x-2 mb-6">

                  </div>

                  {showSkipNotification ? (
                    <DesignSkippedNotification />
                  ) : allDiscussionsComplete ? (
                    <AllDiscussionsCompleteBadge />
                    // ) : isDiscussionCompleted(currentDesign.id, currentDiscussionType) ? (
                    //   <SuccessBadge discussionType={currentDiscussionType} />
                  ) : (
                    <>
                      <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="typography-body-lg font-weight-medium text-gray-700">
                            {getDiscussionTitle(currentDiscussionType)}
                          </h3>

                          {/* Progress Bar aligned right */}
                          <div className="flex items-center space-x-2">
                            <button
                              className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-50 hover:text-primary transition border border-gray-200"
                              onClick={handleRefresh}
                              disabled={isLoad}
                              title="Click to refresh the page"
                            >
                              <RefreshCw size={16} className={`mr-2 ${isLoad ? 'animate-spin' : ''}`} />
                              {isLoad ? 'Refreshing...' : 'Refresh Details'}
                            </button>
                            <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                              Discussion Progress
                            </span>
                            <span className="text-primary-700 typography-body-sm">
                              {currentDiscussionIndex + 1} / {DISCUSSION_TYPES.length}
                            </span>
                            <div className="w-24 h-2 bg-primary-100 rounded-full">
                              <div
                                className="h-2 bg-primary rounded-full transition-all duration-300"
                                style={{
                                  width: `${((currentDiscussionIndex + 1) / DISCUSSION_TYPES.length) * 100}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 p-4 bg-amber-100 border-l-4 border-amber-400 rounded-md flex items-center">
                          <AlertCircle className="text-amber-600 mr-3" size={24} />
                          <p className="text-amber-800 typography-body-sm font-weight-medium">
                            To view the Design details, please complete all the discussions listed below
                          </p>
                        </div>
                      </div>

                      {/* Discussion Status Pills */}
                      {/* Discussion Status Pills */}
                      <div className="flex flex-wrap gap-2 mb-6">
                        {DISCUSSION_TYPES.map((type, index) => {
                          const isCompleted = discussionType[type];
                          return (
                            <div
                              key={type}
                              onClick={() => {
                                // Only allow clicking if the discussion is not completed
                                if (!isCompleted) {
                                  setCurrentDiscussionIndex(index);
                                }
                              }}
                              className={`px-3 py-1 rounded-full typography-body-sm flex items-center gap-1 ${index === currentDiscussionIndex
                                ? 'bg-orange-100 text-orange-800 border border-orange-300'
                                : isCompleted
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 cursor-pointer'
                                } ${isCompleted ? '' : 'cursor-pointer'}`}
                            >
                              {index + 1}. {getDiscussionTitle(type)}
                              {isCompleted && (
                                <CheckCircle size={14} className="text-green-600 ml-1" />
                              )}
                            </div>
                          );
                        })}
                      </div>

                      {/* Buttons */}
                      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                        <div
                          className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "discussion"
                            ? "border-orange-500 bg-orange-50"
                            : "border-gray-200"
                            }`}
                          onClick={() => handleUpdateDesign(currentDesign.id)}
                        >
                          <div className="flex items-center space-x-2 mb-4">
                            <div className="py-1 px-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                              <Image
                                src={Logo}
                                alt="Logo"
                                width={16}
                                height={16}
                                className="text-orange-500"
                              />
                            </div>
                            <h4 className="typography-body-lg font-weight-medium">
                              Interactive configuration
                            </h4>
                          </div>

                          <p className="text-gray-600">
                            {en.DesignUpdate}
                          </p>
                        </div>
                        <div
                          className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md  ${configMethod === "auto"
                            ? "border-orange-500 bg-orange-50"
                            : "border-gray-200"
                            }`}
                          onClick={handleConfigureClick}
                        >
                          <div className="flex mb-4 items-center space-x-2">
                            <div className="p-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                              <Upload className="w-4 h-4 text-orange-500" />
                            </div>
                            <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
                          </div>

                          <p className="text-gray-600">
                            Let our LLM automatically configure this Design based on the available information
                          </p>
                        </div>
                      </div>

                      {/* Discussion Selection */}
                      {/* {DISCUSSION_TYPES.some(type => isDiscussionCompleted(currentDesign.id, type)) && (
                  <div className="mt-6">
                    <p className="typography-body-sm text-gray-600 mb-2">
                      Or choose a different discussion:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {DISCUSSION_TYPES.map((type, index) => (
                        <button
                          key={type}
                          onClick={() => setCurrentDiscussionIndex(index)}
                          disabled={index === currentDiscussionIndex}
                          className={`px-3 py-1 rounded typography-body-sm ${
                            index === currentDiscussionIndex 
                              ? 'bg-orange-500 text-white cursor-default' 
                              : isDiscussionCompleted(currentDesign.id, type)
                                ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          {getDiscussionTitle(type)}
                        </button>
                      ))}
                    </div>
                  </div>
                )} */}
                    </>
                  )}
                </div>
              ) : !showAllSkippedNotification ? (
                <p className="text-center text-gray-500">Loading designs...</p>
              ) : null}
          </div></>)}

      {configureModel && currentDesign && (
        <ConfigureModal
          id={currentDesign.id}
          type={"Architecture"}
          isNodeType={"Architecture"}
          isCreateProject={true}
          setShowConfigModel={setShowConfigModel}
          requirementId={currentDesign.id}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            const successMessage = "Design Configured Successfully";
            showAlert(successMessage, "success");

            // Mark the current discussion as completed
            handleDiscussionCompletion(currentDesign.id, currentDiscussionType);

            // Log the successful configuration to MongoDB
            try {
              createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "design_configuration",
                status: "completed",
                data: {
                  design_id: parseInt(currentDesign.id),
                  type: "Design",
                  status: "configured",
                },
              })
                .then((result) => {

                  // Set the flag to true to indicate this design has been configured
                  sessionStorage.setItem(
                    `openDesignContent-${projectId}-${currentDesign.id}`,
                    "true"
                  );
                  // Refresh the designs list
                  fetchDesigns();
                })
                .catch((error) => {

                });
            } catch (error) {

            }
          }}
        />
      )}
    </div>
  );
}