import React, { useState, useEffect } from "react";
import { useSearchPara<PERSON>, useRouter, usePathname } from "next/navigation";
import { fetchGraph } from "@/utils/graphAPI";
import { FaSearch } from 'react-icons/fa'
import { X } from "lucide-react";

const NodeTypeFilter = ({ nodeTypes, selectedNodeTypes, onNodeSelection }) => {

  return (
    <div className="mb-6">
      <div className="grid grid-cols-1 gap-2">       
        {nodeTypes.map((type, index) => (
          <div key={index} className="flex items-center">
            <input
              type="checkbox"
              id={`type-${index}`}
              checked={selectedNodeTypes?.includes(type)}
              onChange={() => onNodeSelection(type)}
              className="mr-2"
            />
            <label htmlFor={`type-${index}`}>{type}</label>
          </div>
        ))}
      </div>
    </div>
  );
};

const Search = ({ searchTerm, setSearchTerm }) => {
  return (
    <div className="flex items-center justify-center relative">
    <div className="absolute left-0 pl-3">
      <FaSearch className="text-gray-400" />
    </div>
    <input
      type="text"
      placeholder="Search node types"
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      className="px-10 pr-10 py-2 text-left text-color border rounded-md shadow-md border-gray-300 focus:ring-0 w-full"
    />
  </div>
  );
};


const GraphFilterModal = ({ onClose, isCodeGraph = false, onFilterApplied }) => {
  const [selectedNodeTypes, setSelectedNodeTypes] = useState([]);
  const [levels, setLevels] = useState(2);
  const [availableNodeTypes, setAvailableNodeTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [applyLoading, setApplyLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    const getNodeTypes = async () => {
      try {
        setLoading(true);
        const dbType = isCodeGraph ? "code" : "project";
        const data = await fetchGraph(dbType);

        setAvailableNodeTypes(data);
        setLoading(false);

        // Load saved state from local storage
        const savedNodeTypes = localStorage.getItem('selectedNodeTypes');
        const savedLevels = localStorage.getItem('levels');

        if (savedNodeTypes) {
          setSelectedNodeTypes(JSON.parse(savedNodeTypes));
        }
        if (savedLevels) {
          setLevels(parseInt(savedLevels, 10));
        }
      } catch (err) {
        
        setError("Failed to fetch node types");
        setLoading(false);
      }
    };

    getNodeTypes();
  }, [isCodeGraph]);

  const handleNodeSelection = (type) => {
    setSelectedNodeTypes((prevTypes) => {
      const newTypes = prevTypes.includes(type)
        ? prevTypes.filter((t) => t !== type)
        : [...prevTypes, type];
      localStorage.setItem('selectedNodeTypes', JSON.stringify(newTypes));
      return newTypes;
    });
  };

  const allSelected = availableNodeTypes.length === selectedNodeTypes.length;

  const handleSelectAll = () => {
    const newSelectedTypes = allSelected ? [] : [...availableNodeTypes];
    setSelectedNodeTypes(newSelectedTypes);
    localStorage.setItem('selectedNodeTypes', JSON.stringify(newSelectedTypes));
  };

  const handleLevelsChange = (event) => {
    const newLevels = parseInt(event.target.value, 10);
    setLevels(newLevels);
    localStorage.setItem('levels', newLevels.toString());
  };

  const handleClearAll = () => {
    setSelectedNodeTypes([]);
    setLevels(2);
    localStorage.removeItem('selectedNodeTypes');
    localStorage.removeItem('levels');
  };

  const handleApplyFilter = async () => {
    try {
      setApplyLoading(true);

      if (selectedNodeTypes.length === 0) {
        throw new Error("No node types selected");
      }

      const selectedTypeData = {
        levels: levels,
        selectedNodeTypes: selectedNodeTypes
      };

      onFilterApplied(selectedTypeData);

      setApplyLoading(false);
      onClose();
    } catch (error) {
      
      setError(error.message);
      setApplyLoading(false);
    }
  };
  const filteredNodeTypes = availableNodeTypes.filter((type) =>
    type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
      <div className="right-0 bg-white shadow-lg flex flex-col h-full">

        <div className="flex items-center justify-between p-4">
          <h2 className="typography-heading-4 font-weight-bold">Filter Nodes</h2>
          <div className="flex items-center">
            <button
              type="button"
              onClick={handleSelectAll}
              className="mr-4 mt-1 text-primary hover:text-primary-800 font-weight-bold"
            >
              {allSelected ? "Deselect All" : "Select All"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-800"
            >
              <X className="mr-1 cursor-pointer" width={20} height={20} />
            </button>
          </div>
        </div>

        {availableNodeTypes.length > 0 && (
        <div className="pl-4 pr-4 pb-4 border-b">
          <Search searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
        </div>
      )}

        <div className="overflow-y-auto pl-4 pt-4  h-64 custom-scrollbar">
        {loading && <p>Loading node types...</p>}
        {error && <p className="text-red-500">{error}</p>}
        <NodeTypeFilter
          nodeTypes={filteredNodeTypes}
          selectedNodeTypes={selectedNodeTypes}
          onNodeSelection={handleNodeSelection}
        />
      </div>

  <div className="border-t p-3 sticky bottom-0 bg-white">
  <div className="mb-4">
      <h3 className="typography-body-lg font-weight-semibold">Max Depth</h3>
      <input
        type="number"
        value={levels}
        onChange={handleLevelsChange}
        min="1"
        max="10"
        className="w-full p-2 border rounded"
      />
    </div>

    <div className="flex items-end justify-end space-x-2 w-full">
  <button
    type="button"
    onClick={handleClearAll}
    className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
  >
    Reset
  </button>
  <button
    type="button"
    onClick={handleApplyFilter}
    className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-primary-600 rounded-md hover:bg-primary-700"
    disabled={applyLoading}
  >
    {applyLoading ? "Applying..." : "Apply"}
  </button>
</div>


  </div>
</div>

    
  );
};

export default GraphFilterModal;