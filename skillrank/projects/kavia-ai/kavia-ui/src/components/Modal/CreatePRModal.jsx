import React, { useState, useEffect } from 'react';
import { GitPullRequest, ArrowRight, X, ChevronDown } from 'lucide-react';

const BranchSelect = ({ value, onChange, searchValue, setSearchValue, placeholder, branches, isOpen, onToggle }) => {
  const filteredBranches = branches
    .filter(branch => (branch.startsWith('remote'))&&(!branch.includes('/HEAD'))) // Only show remote branches
    .filter(branch => 
      branch.toLowerCase().includes(searchValue.toLowerCase())
    );

  const displayValue = value ? value.replace('remotes/origin/', '') : placeholder;
  
  return (
    <div className="relative">
      <div 
        className="w-full p-2 border rounded-md bg-white cursor-pointer flex justify-between items-center hover:border-gray-400"
        onClick={onToggle}
      >
        <span className="typography-body-sm">{displayValue}</span>
        <ChevronDown size={16} className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </div>
      
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
          <div className="p-2 border-b sticky top-0 bg-white">
            <div className="flex items-center gap-2 px-2 py-1 bg-gray-50 rounded">
              <input
                type="text"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                placeholder={`Search ${placeholder}`}
                className="w-full bg-transparent focus:outline-none typography-body-sm"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
          
          <div className="py-1">
            {filteredBranches.map((branch) => {
              const displayBranch = branch.replace('remotes/origin/', '');
              return (
                <div
                  key={branch}
                  className={`px-4 py-2 cursor-pointer hover:bg-gray-100 typography-body-sm ${
                    value === branch ? 'bg-primary-50 text-primary-700' : ''
                  }`}
                  onClick={() => {
                    onChange(branch);
                    onToggle();
                  }}
                >
                  {displayBranch}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const CreatePRModal = ({ isOpen, onClose, branches=[], onSubmit, isLoading }) => {
  const [sourceBranch, setSourceBranch] = useState('');
  const [targetBranch, setTargetBranch] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [sourceSearch, setSourceSearch] = useState('');
  const [targetSearch, setTargetSearch] = useState('');
  const [activeDropdown, setActiveDropdown] = useState(null);

  // Detect and set current branch as source branch and set target branch on initial render
  useEffect(() => {
    if (branches.length > 0) {
      // Find the current branch (the one with * prefix)
      const currentBranchWithAsterisk = branches.find(branch => branch.startsWith('*'));
      
      if (currentBranchWithAsterisk) {
        // Remove the asterisk and any leading space
        const currentBranch = currentBranchWithAsterisk.replace(/^\*\s*/, '');
        
        // Find the remote equivalent of the current branch
        const remoteBranch = branches.find(
          branch => branch.includes(`origin/${currentBranch}`) && 
                   !branch.includes('HEAD')
        );
        
        // Set the source branch - prefer remote version if available
        if (remoteBranch) {
          setSourceBranch(remoteBranch);
        } else {
          // Convert local branch name to remote format for consistency
          setSourceBranch(`remotes/origin/${currentBranch}`);
        }
      }
      
      // Filter to get only remote branches (excluding HEAD references)
      const remoteBranches = branches.filter(branch => 
        branch.startsWith('remote') && !branch.includes('/HEAD')
      );
      
      if (remoteBranches.length > 0) {
        // First, check if "remotes/origin/main" exists
        const mainBranch = remoteBranches.find(branch => branch === "remotes/origin/main");
        
        if (mainBranch && mainBranch !== sourceBranch) {
          // Set target to main branch if it exists and isn't the same as source
          setTargetBranch(mainBranch);
        } else {
          // Otherwise, select the first branch that isn't the source branch
          const firstAvailableBranch = remoteBranches.find(branch => branch !== sourceBranch);
          if (firstAvailableBranch) {
            setTargetBranch(firstAvailableBranch);
          } else if (remoteBranches.length > 0) {
            // Fallback: just use the first remote branch even if it's the same as source
            setTargetBranch(remoteBranches[0]);
          }
        }
      }
    }
  }, [branches]);

  if (!isOpen) return null;

  const sourceDisplay = sourceBranch ? sourceBranch.replace('remotes/origin/', '') : '';
  const targetDisplay = targetBranch ? targetBranch.replace('remotes/origin/', '') : '';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-lg shadow-xl">
        <div className="p-4 border-b flex items-center gap-2">
          <GitPullRequest size={18} className="text-gray-600" />
          <h2 className="typography-body font-weight-semibold">Create Pull Request</h2>
          <button 
            onClick={onClose}
            className="ml-auto text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <X size={16} />
          </button>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            {/* Branch Selection */}
            <div className="space-y-2">
              <label className="typography-body-sm font-weight-medium text-gray-700">Source Branch</label>
              <BranchSelect
                value={sourceBranch}
                onChange={(branch) => {
                  setSourceBranch(branch);
                  setActiveDropdown(null);
                }}
                searchValue={sourceSearch}
                setSearchValue={setSourceSearch}
                placeholder="Select a source branch"
                branches={branches}
                isOpen={activeDropdown === 'source'}
                onToggle={() => setActiveDropdown(activeDropdown === 'source' ? null : 'source')}
              />
            </div>

            <div className="space-y-2">
              <label className="typography-body-sm font-weight-medium text-gray-700">Target Branch</label>
              <BranchSelect
                value={targetBranch}
                onChange={(branch) => {
                  setTargetBranch(branch);
                  setActiveDropdown(null);
                }}
                searchValue={targetSearch}
                setSearchValue={setTargetSearch}
                placeholder="Select a target branch"
                branches={branches}
                isOpen={activeDropdown === 'target'}
                onToggle={() => setActiveDropdown(activeDropdown === 'target' ? null : 'target')}
              />
            </div>

            {/* Branch Preview */}
            {sourceBranch && targetBranch && (
              <div className="bg-gray-50 p-3 rounded-md">
                <div className="flex items-center gap-2 justify-center">
                  <div className="px-3 py-1 bg-primary-100 rounded-full text-primary-700 typography-body-sm font-weight-medium">
                    {sourceDisplay}
                  </div>
                  <ArrowRight size={16} className="text-gray-400" />
                  <div className="px-3 py-1 bg-gray-200 rounded-full typography-body-sm font-weight-medium">
                    {targetDisplay}
                  </div>
                </div>
              </div>
            )}

            {/* Title and Description Fields */}
            <div className="space-y-2">
              <input
                type="text"
                placeholder="Enter PR title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full p-2 border rounded-md typography-body-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
              />
            </div>

            <div className="space-y-2">
              <textarea
                placeholder="Enter PR description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={5}
                className="w-full p-2 border rounded-md typography-body-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
              />
            </div>
          </div>
        </div>

        <div className="p-4 border-t flex justify-end gap-2">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 rounded-md typography-body-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={() => onSubmit({ sourceBranch, targetBranch, title, description })}
            disabled={isLoading || !sourceBranch || !targetBranch || !title}
            className="px-4 py-2 bg-green-600 text-white typography-body-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 font-weight-medium"
          >
            {isLoading ? 'Creating...' : 'Create Pull Request'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreatePRModal;

