// components/Modal/DeleteConfirmationModal.tsx
import React from 'react';
import { Dialog } from '@headlessui/react';
import { AlertTriangle } from 'lucide-react';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  isLoading: boolean;
  confirmText?: string;
  confirmButtonClass?: string;
}

export default function DeleteConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Deletion",
  message = "This action will delete the item permanently. This cannot be undone.",
  isLoading = false,
  confirmText,
  confirmButtonClass
}: DeleteConfirmationModalProps) {
  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 backdrop-blur-modal bg-black/50 dark:bg-black/60" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="rounded-lg p-6 max-w-sm w-full bg-white dark:bg-gray-800">
          <div className="flex items-center gap-3 text-red-600 mb-4">
            <AlertTriangle size={24} />
            <Dialog.Title className="typography-body-lg font-weight-semibold">
              {title}
            </Dialog.Title>
          </div>

          <Dialog.Description className="text-gray-600 mb-6">
            {message}
          </Dialog.Description>

          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 rounded-md text-gray-600 hover:bg-gray-200 transition-all duration-300 shadow-sm hover:shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              className={`px-4 py-2 text-white rounded-md transition-all duration-300 shadow-sm hover:shadow ${confirmButtonClass || 'bg-red-600 hover:bg-red-700'}`}
              onClick={onConfirm}
              disabled={isLoading}
            >
              {isLoading ? 'Processing...' : (confirmText || 'Delete')}
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}