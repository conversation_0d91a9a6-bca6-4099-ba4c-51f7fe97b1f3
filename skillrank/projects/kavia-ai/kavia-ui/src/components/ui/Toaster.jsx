import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, XCircle, AlertTriangle, Info, X } from 'lucide-react';
import { cn } from '@/lib/utils';

const iconMap = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
};

const Toaster = ({
  type = 'info',
  title,
  message,
  onClose,
  className = '',
}) => {
  const Icon = iconMap[type] || iconMap.info;

  return (
    <div className={cn('toaster', `toaster-${type}`, className)}>
      <div className="toaster-content">
        <div className="toaster-icon">
          <Icon className="w-full h-full" />
        </div>

        <div className="toaster-body">
          <div className="toaster-title">
            {title}
          </div>
          {message && (
            <div className="toaster-message">
              {message}
            </div>
          )}
        </div>

        {onClose && (
          <button
            onClick={onClose}
            className="toaster-close"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default Toaster;