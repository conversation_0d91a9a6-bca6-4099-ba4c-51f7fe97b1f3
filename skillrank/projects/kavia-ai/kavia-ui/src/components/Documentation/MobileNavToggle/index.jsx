import React from 'react';

const ActionButtons = ({ 
  onGeneratePDF, 
  onSyncToS3, 
  onNavigateToDocuments, 
  generatingPdf, 
  syncingToS3 
}) => {
  return (
    <div className="space-y-2">
      <button
        onClick={onGeneratePDF}
        disabled={generatingPdf}
        className={`inline-flex items-center px-3 py-2 typography-body-sm bg-primary text-white rounded-md
          ${generatingPdf ? 'opacity-50 cursor-not-allowed' : 'hover:bg-primary-600'}
          transition-colors duration-200 w-full justify-center`}
      >
        {generatingPdf ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Generating...
          </>
        ) : (
          <>
            <svg className="w-4 h-4 mr-2" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download PDF
          </>
        )}
      </button>

      <button
        onClick={onSyncToS3}
        disabled={syncingToS3}
        className={`inline-flex items-center px-3 py-2 typography-body-sm bg-green-600 text-white rounded-md 
          ${syncingToS3 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-green-700'} 
          transition-colors duration-200 w-full justify-center`}
      >
        {syncingToS3 ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </>
        ) : (
          <>
            <svg className="w-4 h-4 mr-2" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
            Save Current State
          </>
        )}
      </button>

      <button
        onClick={onNavigateToDocuments}
        className="inline-flex items-center px-3 py-2 typography-body-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 
          transition-colors duration-200 w-full justify-center"
      >
        <svg className="w-4 h-4 mr-2" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
          <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
        </svg>
        View Documents
      </button>
    </div>
  );
};

export default ActionButtons;