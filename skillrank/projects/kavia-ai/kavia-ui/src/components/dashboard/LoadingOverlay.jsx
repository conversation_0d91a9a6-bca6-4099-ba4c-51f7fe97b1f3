// components/dashboard/LoadingOverlay.jsx
const LoadingOverlay = ({ show, message = 'Loading...' }) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-5 flex items-center justify-center z-50">
      <div className="rounded-lg p-6 flex items-center space-x-3 shadow-lg" style={{ backgroundColor: 'hsl(var(--card))' }}>
        <div className="animate-spin w-6 h-6 border-2 border-t-transparent rounded-full" style={{ borderColor: 'hsl(var(--primary))', borderTopColor: 'transparent' }}></div>
        <span className="font-weight-medium" style={{ color: 'hsl(var(--card-foreground))' }}>{message}</span>
      </div>
    </div>
  );
};

export default LoadingOverlay;