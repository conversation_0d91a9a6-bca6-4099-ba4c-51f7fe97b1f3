// components/dashboard/StatsCards.jsx
import { formatCurrency } from '../../lib/utils';

const StatCard = ({ icon, label, value, color = 'orange', loading = false, error = false }) => {
  const colorClasses = {
    orange: 'bg-primary-100 text-primary-600',
    green: 'bg-semantic-green-100 text-semantic-green-600',
    purple: 'bg-semantic-purple-100 text-semantic-purple-600',
    yellow: 'bg-semantic-yellow-100 text-semantic-yellow-600',
    red: 'bg-semantic-red-100 text-semantic-red-600',
    gray: 'bg-semantic-gray-100 text-semantic-gray-600'
  };

  // Fallback to gray if invalid color provided
  const cardColor = colorClasses[color] || colorClasses.gray;
  
  // Safe value rendering
  const displayValue = () => {
    if (loading) return '...';
    if (error) return 'Error';
    if (value === null || value === undefined) return '--';
    return value;
  };

  // Safe icon rendering
  const displayIcon = () => {
    if (loading) return '⏳';
    if (error) return '⚠️';
    return icon || '📊';
  };

  return (
    <div className={`bg-white rounded-lg shadow p-6 transition-all duration-200 ${
      error ? 'border-l-4 border-red-400' : ''
    } ${loading ? 'animate-pulse' : ''}`}>
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`w-8 h-8 ${cardColor} rounded-full flex items-center justify-center transition-colors`}>
            <span className="font-weight-semibold typography-body-sm" role="img" aria-label={label}>
              {displayIcon()}
            </span>
          </div>
        </div>
        <div className="ml-4 flex-1 min-w-0">
          <p className="typography-body-sm font-weight-medium text-gray-500 truncate" title={label}>
            {label || 'Unknown Metric'}
          </p>
          <p className={`typography-heading-2 font-weight-semibold ${
            error ? 'text-red-600' : 'text-gray-900'
          } truncate`} title={String(displayValue())}>
            {displayValue()}
          </p>
          {error && (
            <p className="typography-caption text-red-500 mt-1">
              Failed to load data
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

const LoadingSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    {Array.from({ length: 4 }, (_, i) => (
      <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gray-200 rounded-full" />
          <div className="ml-4 flex-1">
            <div className="h-4 bg-gray-200 rounded mb-2 w-24" />
            <div className="h-6 bg-gray-200 rounded w-16" />
          </div>
        </div>
      </div>
    ))}
  </div>
);

const ErrorState = ({ onRetry }) => (
  <div className="bg-white rounded-lg shadow p-8 mb-8">
    <div className="text-center">
      <div className="typography-heading-1 mb-4">⚠️</div>
      <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-2">
        Unable to Load Statistics
      </h3>
      <p className="typography-body-sm text-gray-500 mb-4">
        There was an error loading the dashboard statistics. Please try again.
      </p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
        >
          Retry
        </button>
      )}
    </div>
  </div>
);

const StatsCards = ({ 
  stats, 
  loading = false, 
  error = null, 
  onRetry = null,
  customCards = null 
}) => {
  // Safe formatting functions
  const safeFormatCurrency = (amount) => {
    try {
      if (amount === null || amount === undefined || isNaN(amount)) {
        return '--';
      }
      return formatCurrency(Number(amount));
    } catch (error) {
      console.warn('Error formatting currency:', error);
      return '--';
    }
  };

  const safeFormatNumber = (number) => {
    try {
      if (number === null || number === undefined || isNaN(number)) {
        return '--';
      }
      return Number(number).toLocaleString();
    } catch (error) {
      console.warn('Error formatting number:', error);
      return '--';
    }
  };

  // Handle loading state
  if (loading && !stats) {
    return <LoadingSkeleton />;
  }

  // Handle error state
  if (error && !stats) {
    return <ErrorState onRetry={onRetry} />;
  }

  // Safe stats extraction with fallbacks
  const safeStats = stats || {};
  const totalSessions = safeStats.total_sessions;
  const activeSessions = safeStats.active_sessions;
  const totalTenants = safeStats.total_tenants;
  const totalCost = safeStats.total_cost;

  // Default card configurations
  const defaultCards = [
    {
      icon: "📊",
      label: "Total Sessions",
      value: safeFormatNumber(totalSessions),
      color: "primary",
      error: totalSessions === undefined
    },
    {
      icon: "✅",
      label: "Active Sessions",
      value: safeFormatNumber(activeSessions),
      color: "green",
      error: activeSessions === undefined
    },
    {
      icon: "🏢",
      label: "Total Tenants",
      value: safeFormatNumber(totalTenants),
      color: "purple",
      error: totalTenants === undefined
    },
    {
      icon: "💰",
      label: "Total Cost",
      value: safeFormatCurrency(totalCost),
      color: "yellow",
      error: totalCost === undefined
    }
  ];

  // Use custom cards if provided, otherwise use defaults
  const cardsToRender = customCards || defaultCards;

  // Validate cards array
  if (!Array.isArray(cardsToRender) || cardsToRender.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-8 mb-8">
        <div className="text-center text-gray-500">
          <p>No statistics available to display.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {cardsToRender.map((card, index) => {
          // Validate card object
          if (!card || typeof card !== 'object') {
            return (
              <StatCard
                key={index}
                icon="❓"
                label="Invalid Data"
                value="--"
                color="red"
                error={true}
              />
            );
          }

          return (
            <StatCard
              key={card.key || `stat-${index}`}
              icon={card.icon}
              label={card.label}
              value={card.value}
              color={card.color}
              loading={loading}
              error={card.error || false}
            />
          );
        })}
      </div>
      
      {/* Optional summary or additional info */}
      {stats && !loading && (
        <div className="mt-4 typography-caption text-gray-500 text-center">
          {/* Last updated: {new Date().toLocaleString()} */}
          {error && (
            <span className="ml-2 text-primary-600">
              • Some data may be incomplete
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default StatsCards;