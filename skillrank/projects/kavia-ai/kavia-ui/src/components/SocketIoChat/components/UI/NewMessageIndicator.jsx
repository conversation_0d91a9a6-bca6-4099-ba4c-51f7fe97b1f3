import React from 'react';

// Update the NewMessageIndicator to be more prominent
const NewMessageIndicator = ({ 
  hasNewMessages, 
  messagesContainerRef, 
  setAutoScroll, 
  setHasNewMessages 
}) => {
  if (!hasNewMessages) return null;

  return (
    <button
      onClick={() => {
        // Scroll to bottom when clicked
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
        // Enable auto-scroll again
        setAutoScroll(true);
        setHasNewMessages(false);
      }}
      className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-10
                bg-primary hover:bg-primary-600 text-white p-1.5 rounded-full shadow-md
                flex items-center justify-center animate-bounce-slow"
      style={{ pointerEvents: 'auto' }}
      aria-label="Scroll to new messages"
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
           strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
        <polyline points="6 9 12 15 18 9"></polyline>
      </svg>
    </button>
  );
};

export default NewMessageIndicator; 