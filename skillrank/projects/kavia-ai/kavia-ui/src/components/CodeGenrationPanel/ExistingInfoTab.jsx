import React, { useState, useEffect } from 'react';
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { Loading2 } from '../Loaders/Loading';
import { useParams } from 'next/navigation';
import { fetchLowLevelDesignDetails } from "@/utils/architectureAPI";
import { fetchNodeBasedOnDataModelById } from "@/utils/api";
import FigmaSection from "../../components/BrowsePanel/Architecture/FigmaSection";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import Badge from "@/components/UIComponents/Badge/Badge";

const ExistingInfo = () => {
  const { architectureId } = useCodeGeneration();
  const params = useParams();
  const [designDetails, setDesignDetails] = useState(null);
  const [projectInfo, setProjectInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [designData, projectData] = await Promise.all([
          fetchLowLevelDesignDetails(architectureId),
          fetchNodeBasedOnDataModelById(params.projectId, 'project')
        ]);

        if (designData) {
          setDesignDetails(designData[0]);
        }
        setProjectInfo(projectData);
        setError(null);
      } catch (err) {
        
        setError("Failed to fetch information");
      } finally {
        setLoading(false);
      }
    };

    if (architectureId && params.projectId) {
      fetchData();
    }
  }, [architectureId, params.projectId]);

  const handleFigmaUpdate = async () => {
    try {
      const updatedData = await fetchLowLevelDesignDetails(architectureId);
      setDesignDetails(updatedData);
    } catch (err) {
      
    }
  };

  const renderChildNode = (node) => {
    if (!node) return null;
    
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <div className="flex items-center gap-2">
          <h6 className="font-weight-medium text-gray-700">
            {node.Title || "Untitled Node"}
          </h6>
          {node.Type && (
            <Badge className="bg-primary-100 text-primary-800" type={node.Type} />
          )}
        </div>
        <div className="text-gray-600">
          {node.Description}
        </div>
      </div>
    );
  };

  if (loading) {
    return <Loading2 />;
  }

  if (error) {
    return (
      <div className="text-red-500 p-4 bg-red-50 rounded-lg">
        {error}
      </div>
    );
  }

  if (!designDetails) {
    return <EmptyStateView type="designDetails" />;
  }

  return (
    <div className="p-4 space-y-6">
      {/* Design Specifications */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h2 className="typography-body-lg font-weight-semibold flex items-center gap-2">
            {designDetails.properties?.Title}
          </h2>
        </div>
        {designDetails?.ui_metadata && (
          <PropertiesRenderer
            properties={designDetails.properties}
            metadata={designDetails.ui_metadata}
            to_skip={["Title", "Type"]}
            onUpdate={() => {}} // Read-only in this context
          />
        )}
      </div>

      {/* Figma Section */}
      <div className="bg-white rounded-lg shadow">
        <FigmaSection 
          designNode={designDetails}
          projectId={params.projectId}
          onUpdate={handleFigmaUpdate}
        />
      </div>

      {/* Child Nodes */}
     
    </div>
  );
};

export default ExistingInfo;