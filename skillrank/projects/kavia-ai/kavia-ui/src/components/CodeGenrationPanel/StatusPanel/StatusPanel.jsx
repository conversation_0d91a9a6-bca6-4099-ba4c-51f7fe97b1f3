import React, { useState, useEffect, useRef } from "react";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import Cookies from "js-cookie";
import { useSearchParams } from "next/navigation";
import { Loading2 } from "@/components/Loaders/Loading";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { FaChevronDown, FaChevronUp, FaRedo } from "react-icons/fa";
import { retryCodeGeneration } from "@/utils/batchAPI";
import { getHeadersRaw } from "@/utils/api";

const StatusItem = ({ label, children }) => (
  <p className="flex flex-col md:flex-row gap-2 mb-4">
    <span className="font-weight-medium text-gray-600 min-w-[100px]">{label}:</span>
    {children}
  </p>
);

const StatusBadge = ({ status }) => {
  const getStatusColor = (status) => {
    if (!status) return "bg-gray-200 text-gray-700";

    const statusMap = {
      running: "bg-green-500 text-white",
      paused: "bg-yellow-500 text-white",
      completed: "bg-primary-500 text-white",
      failed: "bg-red-500 text-white",
      in_progress: "bg-purple-500 text-white",
    };

    return statusMap[status.toLowerCase()] || "bg-gray-200 text-gray-700";
  };

  const formatText = (text) => {
    if (!text) return "No Status";
    return text
      .toLowerCase()
      .replace(/[-_]/g, " ")
      .split(" ")
      .filter((word) => word.length > 0)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
      .trim();
  };

  return (
    <span className={`px-3 py-1 rounded-full typography-body-sm font-weight-medium ${getStatusColor(status)}`}>
      {formatText(status)}
    </span>
  );
};

const StatusPanel = () => {
  const {
    llmModel,
    setLlmModel,
    statusData,
    setStatusData,
    taskStatus,
    setTaskStatus,
    setCurrentIframeUrl,
    setCurrentIp,
    gitStatus
  } = useCodeGeneration();
  const [isLoading, setIsLoading] = useState(true);
  const [isStatusExpanded, setIsStatusExpanded] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);
  const statusContainerRef = useRef(null);
  const searchParams = useSearchParams();
  const currentTaskDetailsId = searchParams.get("task_id");
  const idToken = Cookies.get("idToken");
  const tenant_id = Cookies.get("tenant_id");
  useEffect(() => {
    if (!currentTaskDetailsId) return;

    const abortController = new AbortController();
    setIsLoading(true);

    const eventSource = fetchEventSource(
      `${process.env.NEXT_PUBLIC_API_URL}/batch/callback/status/${currentTaskDetailsId}`,
      {
        headers: getHeadersRaw(),
        openWhenHidden: true,
        signal: abortController.signal,
        onmessage: (event) => {
          setIsLoading(false);
          const data = JSON.parse(event.data);
          if (data.status_output) {
            setStatusData(data.status_output);
            setTaskStatus(data.status_output.status);
            // setLlmModel(data.status_output.llm_model);
          }
        },
        onerror: (error) => {

          setIsLoading(false);
          abortController.abort();
        },
        onclose: () => {
          setIsLoading(false);
        },
      }
    );

    return () => abortController.abort();
  }, [currentTaskDetailsId, idToken,isRetrying] );

  const handleRetry = async () => {
    if (!currentTaskDetailsId || isRetrying) return;

    try {
      setIsRetrying(true);
      let response =  await retryCodeGeneration(currentTaskDetailsId);
      if (response.ip){
        setCurrentIp(response.ip)
      }
      if (response.iframe){
        setCurrentIframeUrl(response.iframe)
      }
    } catch (error) {

    } finally {
      setIsRetrying(false);
    }
  };

  const showRetryButton = false;

  if (isLoading) {
    return <Loading2 />;
  }

  return (
    <div ref={statusContainerRef} className="bg-white rounded-lg shadow p-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsStatusExpanded(!isStatusExpanded)}
            className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
          >
            <span className="typography-body-lg font-weight-medium">Current Status</span>
            {isStatusExpanded ? (
              <FaChevronUp className="h-4 w-4" />
            ) : (
              <FaChevronDown className="h-4 w-4" />
            )}
          </button>
          <div className="flex space-x-3">
            {showRetryButton && (
              <button
                onClick={handleRetry}
                disabled={isRetrying}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-white ${
                  isRetrying
                    ? 'bg-red-400 cursor-not-allowed'
                    : 'bg-red-500 hover:bg-red-600'
                }`}
              >
                <FaRedo className={`h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                <span>{isRetrying ? 'Retrying...' : 'Retry'}</span>
              </button>
            )}
          </div>
        </div>

        {isStatusExpanded && (
          <div className="space-y-4 mt-4">
            <StatusItem label="Status">
              <StatusBadge status={statusData?.status || taskStatus} />
            </StatusItem>

            {gitStatus?.current_branch  && (
                <StatusItem label="Branch">
                <span className="text-gray-700">
                  {gitStatus?.current_branch || statusData?.branch_name || 'Not available'}
                </span>
              </StatusItem>
            )}


            {statusData?.failure_reason && (
              <StatusItem label="Cause">
                <span className="text-red-600">
                  {statusData.failure_reason}
                </span>
              </StatusItem>
            )}

            {currentTaskDetailsId && (
              <StatusItem label="Task Id">

                <a
                  href={`https://us5.datadoghq.com/logs?query=task_id%3A${currentTaskDetailsId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary hover:underline"
                  >
                  {currentTaskDetailsId}
                </a>
              </StatusItem>
            )}

            {llmModel && (
              <StatusItem label="LLM model">
                <span className="text-gray-700">{llmModel}</span>
              </StatusItem>
            )}

            {statusData?.description && (
              <StatusItem label="Description">
                <span className="text-gray-700">
                  {statusData.description}
                </span>
              </StatusItem>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default StatusPanel;