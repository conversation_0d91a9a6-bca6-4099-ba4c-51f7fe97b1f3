import React, { useState } from 'react';
import { 
  Download, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Smartphone
} from 'lucide-react';

const AppetizeLogs = ({ 
  appetizeState, 
  onClearLogs 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const downloadLogs = () => {
    if (appetizeState.debugLogs.length === 0) return;

    const logsText = appetizeState.debugLogs
      .map(log => `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`)
      .join('\n');

    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `appetize-logs-${new Date().toISOString().slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = () => {
    switch (appetizeState.connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Smartphone className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    if (appetizeState.isInitializing) {
      return 'Initializing...';
    }
    
    switch (appetizeState.connectionStatus) {
      case 'connected':
        return appetizeState.session ? 'Session Active' : 'Connected - Waiting for session';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Error';
      default:
        return 'Disconnected';
    }
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'error':
        return 'bg-red-50 border-red-500 text-red-800';
      case 'warn':
        return 'bg-yellow-50 border-yellow-500 text-yellow-800';
      case 'network':
        return 'bg-blue-50 border-blue-500 text-blue-800';
      case 'interaction':
        return 'bg-purple-50 border-purple-500 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-300 text-gray-800';
    }
  };

  const getLevelTextColor = (level) => {
    switch (level) {
      case 'error':
        return 'text-red-600';
      case 'warn':
        return 'text-yellow-600';
      case 'network':
        return 'text-blue-600';
      case 'interaction':
        return 'text-purple-600';
      default:
        return 'text-blue-600';
    }
  };

  if (!appetizeState.isAppetizeUrl) return null;

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="text-sm font-medium text-gray-900">
              Appetize Debug Monitor
            </span>
          </div>
          <span className="text-xs text-gray-500">
            {getStatusText()}
          </span>
          {appetizeState.isRecordingLogs && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-red-600 font-medium">Recording</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {appetizeState.debugLogs.length > 0 && (
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              {appetizeState.debugLogs.length} events
            </span>
          )}
          <span className="text-gray-400">
            {isExpanded ? '−' : '+'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {appetizeState.logError && (
        <div className="px-3 pb-3 border-t border-gray-100">
          <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
            {appetizeState.logError}
          </div>
        </div>
      )}

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          {/* Controls */}
          <div className="flex items-center justify-between p-3 bg-gray-50">
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-600">
                Session: {appetizeState.session?.token ? 
                  `${appetizeState.session.token.substring(0, 8)}...` : 
                  'None'
                }
              </span>
              {appetizeState.session?.device && (
                <>
                  <span className="text-xs text-gray-400">•</span>
                  <span className="text-xs text-gray-600">
                    {appetizeState.session.device.name || 'Unknown Device'}
                  </span>
                </>
              )}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={downloadLogs}
                disabled={appetizeState.debugLogs.length === 0}
                className="p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                title="Download Logs"
              >
                <Download className="h-4 w-4 text-gray-600" />
              </button>
              <button
                onClick={onClearLogs}
                disabled={appetizeState.debugLogs.length === 0}
                className="p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                title="Clear Logs"
              >
                <Trash2 className="h-4 w-4 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Logs Display */}
          <div className="max-h-64 overflow-y-auto p-3">
            {appetizeState.debugLogs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Smartphone className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm">No events captured yet</p>
                <p className="text-xs mt-1">
                  {appetizeState.isInitializing 
                    ? "Setting up Appetize connection..."
                    : appetizeState.session 
                    ? "Start interacting with the app to see logs" 
                    : "Waiting for Appetize session to start"
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-1 font-mono text-xs">
                {appetizeState.debugLogs.map((log) => (
                  <div 
                    key={log.id}
                    className={`p-2 rounded border-l-2 ${getLevelColor(log.level)}`}
                  >
                    <div className="flex items-start gap-2">
                      <span className="text-gray-500 text-xs shrink-0">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                      <span className={`font-semibold text-xs shrink-0 ${getLevelTextColor(log.level)}`}>
                        {log.level.toUpperCase()}
                      </span>
                      <span className="break-all">{log.message}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AppetizeLogs;