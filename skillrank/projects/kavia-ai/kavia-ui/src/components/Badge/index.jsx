import React from "react";

const badgeColors = {
    default: "border-transparent bg-primary text-primary-foreground",
    destructive: "bg-destructive border-transparent text-destructive-foreground",
    success: "bg-success border-transparent text-success-foreground",
    info: "bg-info border-transparent text-info-foreground",
    warning: "bg-warning border-transparent text-warning-foreground",
    secondary: "bg-secondary border-transparent text-secondary-foreground",
    dark: "bg-semantic-gray-900 border-transparent text-semantic-gray-50",
};

const badgeVariants = {
  outline: "border border-current bg-transparent",
  soft: "bg-opacity-100 ",
};

function Badge({ className = "", color = "default", variant = "outline", ...props }) {
  const colorClasses = badgeColors[color] || badgeColors.default;
  const variantClasses = badgeVariants[variant] || badgeVariants.outline;

  return (
    <div
      className={`inline-flex items-center rounded-sm px-2.5 py-0.5 typography-caption font-weight-semibold  ${colorClasses} ${variantClasses} ${className}`}
      {...props}
    />
  );
}

export default Badge;
