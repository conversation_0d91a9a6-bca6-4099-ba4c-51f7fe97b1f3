// src/components/UserOnboarding/ui/StatsCard.tsx
import { FC } from 'react';

interface StatsCardProps {
  title: string;
  value: string | number;
  total: string | number;
  subtitle: string;
  progress: number;
}

const StatsCard: FC<StatsCardProps> = ({
  title,
  value,
  total,
  subtitle,
  progress
}) => {
  return (
    <div className="p-6 bg-white rounded-lg shadow-xl">
      <h3 className="project-panel-heading">{title}</h3>
      <div className="mt-2 flex items-baseline">
        <span className="typography-heading-1 font-weight-semibold">{value}</span>
        <span className="ml-2 typography-body-sm text-gray-500">of {total} {subtitle}</span>
      </div>
      <div className="mt-4">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary-600 h-2 rounded-full"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default StatsCard;