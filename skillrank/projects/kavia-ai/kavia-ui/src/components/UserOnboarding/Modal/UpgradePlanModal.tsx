"use client";

import React, { useState } from "react";
import { X, Check, Loader2 } from "lucide-react";

interface UpgradePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  fetchUsers :() => void;
  onUpgrade: (planId: string, planName: string, credits: number) => Promise<void>;
  selectedUser: {
    id: string;
    name: string;
    email: string;
    current_plan_name?: string;
  } | null;
  isLoading?: boolean;
}

// Plan mapping with credits allocation
const PLAN_OPTIONS = [
  {
    id: "price_1RWBTrCI2zbViAE2WZFApvc8",
    name: "Premium Starter",
    displayName: "Premium Starter",
    credits: 220000,
    description: "Perfect for getting started with premium features"
  },
  {
    id: "price_1RIosGCI2zbViAE25Ny8rOkc", 
    name: "Premium Advanced",
    displayName: "Premium Advanced",
    credits: 550000,
    description: "Advanced features for growing teams"
  },
  {
    id: "price_1RIozwCI2zbViAE2beuA7CDk",
    name: "Premium Pro", 
    displayName: "Premium Pro",
    credits: 1100000,
    description: "Full access to all premium features"
  }
];

const UpgradePlanModal: React.FC<UpgradePlanModalProps> = ({
  isOpen,
  onClose,
  onUpgrade,
  selectedUser,
  isLoading = false,
  fetchUsers
}) => {
  const [selectedPlan, setSelectedPlan] = useState<string>("");

  if (!isOpen || !selectedUser) return null;

  const handleUpgrade = async () => {
    if (!selectedPlan) return;
    
    const plan = PLAN_OPTIONS.find(p => p.id === selectedPlan);
    if (!plan) return;

    try {
      await onUpgrade(selectedPlan, plan.name, plan.credits);
      setSelectedPlan("");
      fetchUsers()
      onClose();
    } catch {
      // Error handling is done in the parent component
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setSelectedPlan("");
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="typography-heading-3 font-weight-semibold text-gray-900">
            Upgrade User Plan
          </h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* User Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="typography-body-md font-weight-medium text-gray-900 mb-1">
              {selectedUser.name}
            </h3>
            <p className="typography-body-sm text-gray-600 mb-2">
              {selectedUser.email}
            </p>
            <p className="typography-body-sm text-gray-500">
              Current Plan: <span className="font-weight-medium">{selectedUser.current_plan_name || 'Free Plan'}</span>
            </p>
          </div>

          {/* Plan Selection */}
          <div className="mb-6">
            <h4 className="typography-body-md font-weight-medium text-gray-900 mb-3">
              Select New Plan
            </h4>
            <div className="space-y-3">
              {PLAN_OPTIONS.map((plan) => (
                <div
                  key={plan.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedPlan === plan.id
                      ? 'border-primary bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => !isLoading && setSelectedPlan(plan.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h5 className="typography-body-md font-weight-medium text-gray-900">
                          {plan.displayName}
                        </h5>
                        {selectedPlan === plan.id && (
                          <Check className="w-4 h-4 text-primary ml-2" />
                        )}
                      </div>
                      <p className="typography-body-sm text-gray-600 mt-1">
                        {plan.description}
                      </p>
                      <p className="typography-body-sm text-gray-500 mt-1">
                        Credits: <span className="font-weight-medium">{plan.credits.toLocaleString()}</span>
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="px-4 py-2 typography-body-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            onClick={handleUpgrade}
            disabled={!selectedPlan || isLoading}
            className="px-4 py-2 typography-body-sm text-white bg-primary rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
            {isLoading ? 'Upgrading...' : 'Upgrade Plan'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpgradePlanModal;
