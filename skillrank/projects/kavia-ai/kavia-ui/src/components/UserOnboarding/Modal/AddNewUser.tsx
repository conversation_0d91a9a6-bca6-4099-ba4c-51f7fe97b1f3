  // components/Organizations/Groups/AddNewUserModal.tsx
  "use client";

  import React, { useState, useContext, useEffect } from "react";
  import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
  import { X, UserPlus } from "lucide-react";
  import { Toggle } from "@/components/UserOnboarding/ui/Toggle";
  import { AlertContext } from "@/components/NotificationAlertService/AlertList";
  import Image from "next/image";
  import { addOrganizationUser } from '@/utils/api'; 
  import { ContactNumberField } from "../ui/ContactNumberField";
  import { useUser } from "@/components/Context/UserContext";
import { UserCredModal } from "@/components/UIComponents/Modals/UserCredModal";

  interface AccessControlState {
    userGroup: string;
    sendWelcomeEmail: boolean;
    requireMFA: boolean;
    autoGeneratePassword: boolean;
    isAdmin: boolean;
  }

  interface GroupFormState {
    firstName: string;
    lastName: string;
    workEmail: string;
    contactNumber: string;
    employeeId: string;
    designation: string;
    photo?: File;
    accessControl: AccessControlState;
  }

  interface AddNewUserModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (formData: GroupFormState) => void;
  }

  interface SuccessModalState {
    show: boolean;
    inviteUrl: string;
    loginUrl: string;
  }

  export const AddNewUserModal: React.FC<AddNewUserModalProps> = ({
    isOpen,
    onClose,
    onSubmit,
  }) => {
    const [currentStep, setCurrentStep] = useState<"userInfo" | "accessControl">(
      "userInfo"
    );
    const [isLoading, setIsLoading] = useState(false);
    const { showAlert } = useContext(AlertContext);
    const { tenant_id } = useUser();
    const [formData, setFormData] = useState<GroupFormState>({
      firstName: "",
      lastName: "",
      workEmail: "",
      contactNumber: "",
      employeeId: "",
      designation: "",
      accessControl: {
        userGroup: "",
        sendWelcomeEmail: true,
        requireMFA: false,
        autoGeneratePassword: true,
        isAdmin: false,
      },
    });

    const[countryCode, setCountryCode] = useState<string>("+91");
    const[plainNumber, setPlainNumber] = useState<string>("");

    const handleContactNumberChange = (field: string, value: string) => {
      if (field === "countryCode") {
        setCountryCode(value);
        setFormData((prev) => ({ ...prev, contactNumber: value + plainNumber }));
      } else {
        setPlainNumber(value);
        setFormData((prev) => ({ ...prev, contactNumber: countryCode + value }));
      }
    }

    const [photoPreview, setPhotoPreview] = useState<string>("");

    const [errors, setErrors] = useState({
      firstName: "",
      lastName: "",
      workEmail: "",
      contactNumber: "",
      designation: ""
    })

    const [isDataValid, setIsDataValid] = useState(false);

    const isValidName = (name: string) : boolean => name.trim().length > 0;
    const isValidEmail = (email: string): boolean => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    const isValidContactNumber = (number: string): boolean => {
      // Check basic format first
      if (!/^\+[0-9]{2,3}[0-9]{10}$/.test(number)) {
        return false;
      }
      // Extract the actual number part after country code
      const numberPart = number.slice(3); // Skip the '+' and country code
      // Check if it's all zeros
      return !/^0+$/.test(numberPart);
    };
    const isValidDesignation = (designation: string): boolean => {
      return designation.trim().length >= 2 && designation.trim().length <= 50;
    };

    useEffect(() => {
      const validateData = () => {
        const { firstName, lastName, workEmail, contactNumber, designation } = formData;

        const isFirstNameValid = isValidName(firstName);
        const isLastNameValid = isValidName(lastName);
        const isWorkEmailValid = isValidEmail(workEmail);
        
        // Contact number validation - now required
        const isContactValid = isValidContactNumber(contactNumber);
        
        // Designation validation - now required
        const isDesignationValid = isValidDesignation(designation);

        return (
          isFirstNameValid &&
          isLastNameValid &&
          isWorkEmailValid &&
          isContactValid &&
          isDesignationValid
        );
      };

      setIsDataValid(validateData());
    }, [formData, plainNumber]);

    const checkFieldError = (field: keyof GroupFormState) => {
      setErrors((prevErrors) => {
        const updatedErrors = { ...prevErrors };
        
        if(field == 'firstName'){
            if (!isValidName(formData['firstName'])) {
                updatedErrors.firstName = "First Name is required.";
            }
        }

        if(field == 'lastName'){
          if (!isValidName(formData['lastName'])) {
              updatedErrors.lastName = "Last Name is required.";
          }
        }

        if(field == 'workEmail'){
            if (!isValidEmail(formData['workEmail'])) {
                updatedErrors.workEmail = "Invalid or empty email.";
            }
        }

        if(field == 'contactNumber'){
          if (!isValidContactNumber(formData['contactNumber'])) {
            updatedErrors.contactNumber = "Invalid or empty contact number.";
          }
        }

        if(field === 'designation') {
          if (!isValidDesignation(formData.designation)) {
            updatedErrors.designation = "Designation must be between 2 and 50 characters.";
          }
        }

        return updatedErrors;
      });
    }


  const removeError = (field: keyof typeof errors) => {
      setErrors((prevErrors) => {
          const updatedErrors = { ...prevErrors };
          delete updatedErrors[field];
          return updatedErrors;
      });
  }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        setFormData((prev) => ({ ...prev, photo: file }));
        const reader = new FileReader();
        reader.onloadend = () => {
          setPhotoPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
    };

    const [successModal, setSuccessModal] = useState<SuccessModalState>({
      show: false,
      inviteUrl: "",
      loginUrl: ""
    });

    const handleCreate = async () => {
      try {
        setIsLoading(true);
        if (!formData.workEmail || !formData.firstName || !formData.lastName) {
          throw new Error("Required fields missing");
        }
    
        const userData = {
          email: formData.workEmail,
          name: `${formData.firstName} ${formData.lastName}`.trim(),
          contact_number: formData.contactNumber || "",
          department: formData.designation || "",  
          temporary_password: formData.accessControl.autoGeneratePassword ? 
            Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8) :
            "DefaultTemp123!",
          is_admin: formData.accessControl.isAdmin
        };
    
        const organizationId = tenant_id;
        const response = await addOrganizationUser(organizationId, userData);
        
        if (response.error) {
          // Handle specific error cases
          if (response.error.includes("already exists")) {
            throw new Error("A user with this email already exists");
          } else if (response.error.includes("invalid email")) {
            throw new Error("Please enter a valid email address");
          } else if (response.error.includes("organization not found")) {
            throw new Error("Organization not found. Please try again later");
          } else {
            // For any other specific error messages from the API
            throw new Error(response.error);
          }
        }
        
        const setPasswordUrl = `${window.location.protocol}//${window.location.host}/users/set_password?tenant_id=${encodeURIComponent(tenant_id)}&email=${encodeURIComponent(userData.email)}`;
        const loginUrl = `${window.location.protocol}//${window.location.host}/users/login?tenant_id=${encodeURIComponent(tenant_id)}&email=${encodeURIComponent(userData.email)}`;
        
        setSuccessModal({
          show: true,
          inviteUrl: setPasswordUrl,
          loginUrl: loginUrl
        });
        
        showAlert("User created successfully!", "success");
      } catch (error: any) {
        // Show the specific error message
        showAlert(error.message || "An unexpected error occurred. Please try again.", "danger");
      } finally {
        setIsLoading(false);
      }
    };

    const copyInviteUrl = async () => {
      try {
        await navigator.clipboard.writeText(successModal.inviteUrl);
        showAlert("Invite URL copied to clipboard!", "success");
      } catch (err) {
        showAlert("Failed to copy URL", "danger");
      }
    };

    const downloadCredentials = () => {
      const content = `User Credentials Information

Set Password URL:
${successModal.inviteUrl}

Login URL (after setting password):
${successModal.loginUrl}

Note: Please keep this information secure and delete after use.
      `;

      const blob = new Blob([content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'user_credentials.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    };

    const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
      // Check if the click is on the overlay itself and not its children
      if (e.target === e.currentTarget) {
        onClose();
      }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      const newValue = name === 'workEmail' ? value.toLowerCase() : value;
      setFormData((prev) => ({ ...prev, [name]: newValue }));
    };

    
    const handleNext = () => {
      if (currentStep === "userInfo") {
        setCurrentStep("accessControl");
      } else {
        handleCreate(); // Changed from onSubmit(formData)
      }
    };

    // Add a function to reset all form states
    const resetFormState = () => {
      setCurrentStep("userInfo");
      setFormData({
        firstName: "",
        lastName: "",
        workEmail: "",
        contactNumber: "",
        employeeId: "",
        designation: "",
        accessControl: {
          userGroup: "",
          sendWelcomeEmail: true,
          requireMFA: false,
          autoGeneratePassword: true,
          isAdmin: false,
        },
      });
      setPhotoPreview("");
      setCountryCode("+91");
      setPlainNumber("");
      setErrors({
        firstName: "",
        lastName: "",
        workEmail: "",
        contactNumber: "",
        designation: ""
      });
    };

    if (!isOpen) return null;

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        onClick={handleOverlayClick}
      >
        <div className="bg-white rounded-lg w-full max-w-[1000px] mx-4 p-8  relative">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="typography-heading-4 font-weight-semibold">Add new user</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          {/* Progress Indicator */}
          <div className="flex items-center mb-8">
            <div
              className={`flex items-center ${
                currentStep === "userInfo" ? "text-primary" : "text-gray-500"
              }`}
            >
              <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2">
                {currentStep === "accessControl" ? (
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.1272 7.89384L18.2355 7.00102C18.0469 6.81332 17.9436 6.56305 17.9436 6.29789V5.03464C17.9436 3.39201 16.6071 2.05527 14.9648 2.05527H13.7017C13.4406 2.05527 13.1844 1.949 12.9997 1.76428L12.107 0.871465C10.9453 -0.290488 9.0567 -0.290488 7.89495 0.871465L7.0003 1.76428C6.81561 1.949 6.55943 2.05527 6.29828 2.05527H5.03525C3.39291 2.05527 2.0564 3.39201 2.0564 5.03464V6.29789C2.0564 6.56305 1.95313 6.81332 1.76547 7.00102L0.872803 7.89284C0.309801 8.45594 0 9.20476 0 10.0002C0 10.7957 0.310793 11.5446 0.872803 12.1067L1.76447 12.9995C1.95313 13.1872 2.0564 13.4374 2.0564 13.7026V14.9659C2.0564 16.6085 3.39291 17.9452 5.03525 17.9452H6.29828C6.55943 17.9452 6.81561 18.0515 7.0003 18.2362L7.89296 19.13C8.47384 19.71 9.23642 20 9.99901 20C10.7616 20 11.5242 19.71 12.1051 19.129L12.9977 18.2362C13.1844 18.0515 13.4406 17.9452 13.7017 17.9452H14.9648C16.6071 17.9452 17.9436 16.6085 17.9436 14.9659V13.7026C17.9436 13.4374 18.0469 13.1872 18.2355 12.9995L19.1272 12.1077C19.6892 11.5446 20 10.7967 20 10.0002C20 9.20376 19.6902 8.45594 19.1272 7.89384ZM14.5229 8.84028L8.56519 12.8128C8.39738 12.925 8.20475 12.9796 8.0141 12.9796C7.75792 12.9796 7.50372 12.8803 7.31209 12.6886L5.32618 10.7024C4.93794 10.3141 4.93794 9.68642 5.32618 9.29811C5.71443 8.9098 6.34197 8.9098 6.73022 9.29811L8.14021 10.7083L13.4207 7.18773C13.8785 6.88284 14.4941 7.00598 14.7979 7.46282C15.1028 7.91966 14.9796 8.53639 14.5229 8.84028Z" fill="#1C64F2"/>
                  </svg>
                ) : (
                  <div className="w-6 h-6" />
                )}
              </div>
              <span>User information</span>
            </div>
            <div className="flex-1 mx-4 border-t border-gray-300" />
            <div
              className={`flex items-center ${
                currentStep === "accessControl"
                  ? "text-primary"
                  : "text-gray-500"
              }`}
            >
              <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2">
                  <div className="w-6 h-6" />
              </div>
              <span>Access Control</span>
            </div>
          </div>

          {currentStep === "userInfo" ? (
            <div className="space-y-6">
              <div>
                <h3 className="text-md p-2 bg-gray-50 font-weight-medium mb-4 text-[#1C64F2]">
                  Personal Information
                </h3>

                {/* Photo Upload */}
                <div className="mb-6">
                  <label className="block typography-body-sm font-weight-medium mb-2">
                    Upload Photo
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-full bg-gray-100 overflow-hidden">
                      {photoPreview ? (
                        <Image
                          src={photoPreview}
                          alt="Preview"
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200" />
                      )}
                    </div>
                    <div>
                      <button
                        onClick={() =>
                          document.getElementById("photo-upload")?.click()
                        }
                        className="px-4 py-2 bg-gray-100 rounded-md typography-body-sm hover:bg-gray-200"
                      >
                        Choose file
                      </button>
                      <input
                        id="photo-upload"
                        type="file"
                        className="hidden"
                        accept="image/jpeg,image/png,image/gif"
                        onChange={handleFileChange}
                      />
                      <p className="typography-caption text-gray-500 mt-1">
                        JPG, GIF or PNG. Max size of 800k
                      </p>
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block typography-body-sm font-weight-medium mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      onFocus={() => removeError("firstName")}
                      onBlur={() => checkFieldError("firstName")}
                      className={`w-full px-3 py-2 border-2 rounded-md bg-gray-50 ${errors.firstName? "border-red-300" : "border-gray-200"}`}
                      placeholder="First Name"
                    />
                    {errors.firstName && 
                      <p className="text-red-400">{errors.firstName}</p>
                    }
                  </div>
                  <div>
                    <label className="block typography-body-sm font-weight-medium mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      onFocus={() => removeError("lastName")}
                      onBlur={() => checkFieldError("lastName")}
                      className={`w-full px-3 py-2 border-2 rounded-md bg-gray-50 ${errors.lastName? "border-red-300" : "border-gray-200"}`}
                      placeholder="Last Name"
                    />
                    {errors.lastName && 
                      <p className="text-red-400">{errors.lastName}</p>
                    }
                  </div>
                  {/* Email and Contact in a flex container */}
                  <div className="col-span-2 flex gap-4">
                    <div className="flex-[3]">
                      {" "}
                      {/* 75% width */}
                      <label className="block typography-body-sm font-weight-medium mb-2">
                        Work Email
                      </label>
                      <input
                        type="email"
                        name="workEmail"
                        value={formData.workEmail}
                        onFocus={() => removeError("workEmail")}
                        onBlur={() => checkFieldError("workEmail")}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border-2 rounded-md bg-gray-50 ${errors.workEmail? "border-red-300" : "border-gray-200"}`}
                        placeholder="<EMAIL>"
                      />
                      {errors.workEmail && 
                        <p className="text-red-400">{errors.workEmail}</p>
                      }
                    </div>
                    <div className="flex-1">
                      {" "}
                      {/* 25% width */}
                      <label className="block typography-body-sm font-weight-medium mb-2">
                        Contact Number
                      </label>
                      
                      {/*<input
                        type="tel"
                        name="contactNumber"
                        value={formData.contactNumber}
                        onChange={handleInputChange}
                        onFocus={() => removeError("contactNumber")}
                        onBlur={() => checkFieldError("contactNumber")}
                        className={`w-full px-3 py-2 border-2 rounded-md bg-gray-50 ${errors.workEmail? "border-red-300" : "border-gray-200"}`}
                        placeholder="+1**************"
                      />*/}

                      <ContactNumberField
                        placeholder="0000000000"
                        countryCode={countryCode}
                        plainNumber={plainNumber}
                        onCountryCodeChange={(e) => handleContactNumberChange("countryCode", e.target.value)}
                        onPlainNumberChange={(e) => handleContactNumberChange("plainNumber", e.target.value)}
                        onFocus={() => removeError("contactNumber")}
                        onBlur={() => checkFieldError("contactNumber")}
                      />
                      {errors.contactNumber && 
                        <p className="text-red-400">{errors.contactNumber}</p>
                      }
                    </div>
                  </div>
                  <div>
                    <label className="block typography-body-sm font-weight-medium mb-2">
                      Employee ID
                    </label>
                    <input
                      type="text"
                      name="employeeId"
                      value={formData.employeeId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border-2 border-gray-200 rounded-md bg-gray-50"
                      placeholder="EMP-123"
                    />
                  </div>
                  <div>
                    <label className="block typography-body-sm font-weight-medium mb-2">
                      Designation
                    </label>
                    <input
                      type="text"
                      name="designation"
                      value={formData.designation}
                      onChange={handleInputChange}
                      onFocus={() => removeError("designation")}
                      onBlur={() => checkFieldError("designation")}
                      className={`w-full px-3 py-2 border-2 rounded-md bg-gray-50 ${
                        errors.designation ? "border-red-300" : "border-gray-200"
                      }`}
                      placeholder="Enter Designation"
                    />
                    {errors.designation && 
                      <p className="text-red-400">{errors.designation}</p>
                    }
                  </div>
                  <div className="col-span-2 mt-4">
                    <div className="flex items-center justify-end space-x-2">
                      <label htmlFor="isAdmin" className="typography-body-sm font-weight-medium text-gray-700">
                        Admin User
                      </label>
                      <input
                        type="checkbox"
                        id="isAdmin"
                        checked={formData.accessControl.isAdmin}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            accessControl: {
                              ...prev.accessControl,
                              isAdmin: e.target.checked,
                            },
                          }))
                        }
                        className="h-4 w-4 text-primary rounded border-2 border-gray-300 focus:ring-primary"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div>
              {/* Access Control content */}

              <div>
                <h3 className="text-md p-2 bg-gray-50 font-weight-medium mb-2 -mt-2 text-[#1C64F2]">
                  Access Control
                </h3>

                <div className="space-y-6">
                  <div className="space-y-4 relative">
                    <h4 className="typography-body-sm font-weight-medium">Access Settings</h4>

                    {/* Send Welcome Email Toggle */}
                    <div className="flex items-center justify-between py-2">
                      <div>
                        <h5 className="typography-body-sm font-weight-medium">
                          Send Welcome Email
                        </h5>
                        <p className="typography-body-sm text-gray-500">
                          Automatically send login credentials
                        </p>
                      </div>
                      <Toggle
                        enabled={formData.accessControl.sendWelcomeEmail}
                        onChange={(enabled) =>
                          setFormData((prev) => ({
                            ...prev,
                            accessControl: {
                              ...prev.accessControl,
                              sendWelcomeEmail: enabled,
                            },
                          }))
                        }
                        size="small"
                        disabled={true}
                      />
                    </div>

                    {/* Require MFA Toggle */}
                    <div className="flex items-center justify-between py-2">
                      <div>
                        <h5 className="typography-body-sm font-weight-medium">Require MFA</h5>
                        <p className="typography-body-sm text-gray-500">
                          Enable two-factor authentication
                        </p>
                      </div>
                      <Toggle
                        enabled={formData.accessControl.requireMFA}
                        onChange={(enabled) =>
                          setFormData((prev) => ({
                            ...prev,
                            accessControl: {
                              ...prev.accessControl,
                              requireMFA: enabled,
                            },
                          }))
                        }
                        size="small"
                        disabled={true}
                      />
                    </div>

                    {/* Auto-generate Password Toggle */}
                    <div className="flex items-center justify-between py-2">
                      <div>
                        <h5 className="typography-body-sm font-weight-medium">
                          Auto-generate Password
                        </h5>
                        <p className="typography-body-sm text-gray-500">
                          Generate secure temporary password
                        </p>
                      </div>
                      <Toggle
                        enabled={formData.accessControl.autoGeneratePassword}
                        onChange={(enabled) =>
                          setFormData((prev) => ({
                            ...prev,
                            accessControl: {
                              ...prev.accessControl,
                              autoGeneratePassword: enabled,
                            },
                          }))
                        }
                        size="small"
                        disabled={true}
                      />
                    </div>

                    {/* Coming Soon Watermark */}
                    <div className="absolute inset-0 flex items-center justify-center bg-white/50">
                      <span className="typography-heading-4 font-weight-semibold text-gray-400 rotate-[-30deg]">Coming Soon</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer buttons are already in place in your original code */}
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between gap-4 mt-8">
            <div className={`${currentStep === 'accessControl'? "opacity-1" : "opacity-0"}`}>
              { currentStep === 'accessControl' &&
                <DynamicButton variant="ghost" text="Back" onClick={() => setCurrentStep('userInfo')} />
              }
            </div>
            <div className="flex gap-4">
              <DynamicButton variant="ghost" text="Close" onClick={onClose} />
              <DynamicButton
                variant="primary"
                text={currentStep === "userInfo" ? "Next: Access Control" : "Create User"}
                disabled={currentStep === "userInfo" ? 
                  !isDataValid
                  : isLoading}
                loading={currentStep === "accessControl" && isLoading}
                onClick={handleNext}
              />
            </div>
          </div>
        </div>

        {/* Success Modal */}
        {successModal.show && (
          <UserCredModal
            isOpen={successModal.show}
            title="User Added Successfully!"
            message="Share these URLs with the new user"
            inviteUrl={successModal.inviteUrl}
            loginUrl={successModal.loginUrl}
            icon={UserPlus}
            iconClassName="w-8 h-8 text-green-500"
            onClose={() => {
              setSuccessModal({ show: false, inviteUrl: "", loginUrl: "" });
              onSubmit(formData);
              resetFormState();
              onClose();
            }}
          />
        )}
      </div>
    );
  };

  export default AddNewUserModal;
