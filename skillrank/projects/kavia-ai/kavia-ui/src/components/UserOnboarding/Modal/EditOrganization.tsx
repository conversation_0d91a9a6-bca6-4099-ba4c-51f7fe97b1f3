// components/UserOnboarding/Modal/EditOrganization.tsx
import React, { useState, useRef, useEffect } from 'react';
import { Toggle } from "@/components/UserOnboarding/ui/Toggle";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { X } from 'lucide-react';

interface EditOrganizationProps {
  isOpen: boolean;
  onClose: () => void;
  initialData: {
    name: string;
    email: string;
    isSuspended: boolean;
  };
  onSave: (data: { name: string; email: string; isSuspended: boolean }) => void;
}

const EditOrganization: React.FC<EditOrganizationProps> = ({
  isOpen,
  onClose,
  initialData,
  onSave
}) => {
  const [formData, setFormData] = useState(initialData);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newValue = name === 'email' ? value.toLowerCase() : value;
    setFormData(prev => ({ ...prev, [name]: newValue }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-lg w-full max-w-xl mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="typography-heading-4 font-weight-semibold">Edit Organization</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form Content */}
        <div className="p-6 space-y-6">
          {/* Name Input */}
          <div>
            <label className="block typography-body-sm text-gray-700 mb-2">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Name"
            />
          </div>

          {/* Email Input */}
          <div>
            <label className="block typography-body-sm text-gray-700 mb-2">Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="<EMAIL>"
            />
          </div>

          {/* Suspend Toggle */}
          <div>
            <label className="block typography-body-sm text-gray-700 mb-2">Suspend</label>
            <Toggle
              enabled={formData.isSuspended}
              onChange={(enabled) => setFormData(prev => ({ ...prev, isSuspended: enabled }))}
              size="small"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50 rounded-b-lg">
          <DynamicButton
            variant="primary"
            text="Save"
            onClick={() => {
              onSave(formData);
              onClose();
            }}
          />
          <DynamicButton
            variant="ghost"
            text="Close"
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  );
};

export default EditOrganization;