'use client';

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>ard,
  BookCheck,
  Smile,
  PenTool,
  Link,
  Headphones,
  Code,
} from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { ToggleButton } from "@/components/UserOnboarding/ui/ToggleButton";
import { fetchOrganizationPlans } from '@/utils/api';
import Loader from "@/components/UserOnboarding/ui/Loader"
import Cookies from 'js-cookie';

// Types and Interfaces
type BillingCycle = "monthly" | "yearly";
type OrganizationStep = "details" | "adminSetup" | "planSelection" | "featureConfiguration" | "review";

interface PlanSelectionProps {
  selectedPlan?: number;
  billingCycle?: BillingCycle;
  onNext: (planIndex: number, billingCycle: BillingCycle) => void;
  back: (step: OrganizationStep) => void;
}

interface Feature {
  text: string;
  available: boolean;
  icon: keyof typeof featureIcons;
}

interface APIFeatures {
  workflow_templates: string;
  workflow_management: string;
  security: string;
  workflow_analysis: boolean;
  custom_workflow: boolean;
  integration: string | boolean;
  support: string | boolean;
  developer_platform: boolean;
}

interface APIPlan {
  _id: string;
  id: string;
  name: string;
  price: number;
  interval: 'monthly' | 'yearly';
  features: APIFeatures;
  created_at: string;
  updated_at: string;
}

// Constants
const COOKIE_NAME = 'org_details_form';

const featureIcons = {
  workflow: CreditCard,
  management: Smile,
  security: BookCheck,
  analysis: BookCheck,
  creation: PenTool,
  integration: Link,
  support: Headphones,
  platform: Code,
} as const;

const PlanSelection: React.FC<PlanSelectionProps> = ({
  selectedPlan: initialPlan,
  billingCycle: initialCycle,
  onNext,
  back
}) => {
  const [selectedPlan, setSelectedPlan] = useState<number | undefined>(initialPlan);
  const [billingCycle, setBillingCycle] = useState<BillingCycle>(initialCycle || "monthly");
  const [apiPlans, setApiPlans] = useState<APIPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPlans = async () => {
      try {
        const data = await fetchOrganizationPlans();
        setApiPlans(data);
      } catch (error) {
        setError('Failed to load plans');
        
      } finally {
        setIsLoading(false);
      }
    };

    loadPlans();
  }, []);

  // Reset selected plan when billing cycle changes
  useEffect(() => {
    setSelectedPlan(undefined);
  }, [billingCycle]);

  const filteredPlans = apiPlans.filter(plan => plan.interval === billingCycle);

  useEffect(() => {
    const savedData = Cookies.get(COOKIE_NAME);
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setSelectedPlan(parsedData.planIndex != undefined ? parsedData.planIndex : undefined);
        setBillingCycle(parsedData.billing_interval ? parsedData.billing_interval : "monthly");
      } catch (error) {
        
      }
    }

  }, []);

  const mapAPIFeaturesToUIFeatures = (plan: APIPlan): Feature[] => [
    {
      text: `${plan.features.workflow_templates} workflow templates`,
      available: true,
      icon: "workflow"
    },
    {
      text: `${plan.features.workflow_management} workflow management`,
      available: true,
      icon: "management"
    },
    {
      text: `${plan.features.security} security features`,
      available: true,
      icon: "security"
    },
    {
      text: "Workflow analysis & reporting",
      available: plan.features.workflow_analysis,
      icon: "analysis"
    },
    {
      text: "Custom workflow creation",
      available: plan.features.custom_workflow,
      icon: "creation"
    },
    {
      text: "Integration capabilities",
      available: !!plan.features.integration,
      icon: "integration"
    },
    {
      text: plan.features.support === "24x7"
        ? "24×7 phone, chat, and email support"
        : plan.features.support === "business-hours"
          ? "Business hours support"
          : "Basic support",
      available: !!plan.features.support,
      icon: "support"
    },
    {
      text: "Robust developer platform",
      available: plan.features.developer_platform,
      icon: "platform"
    }
  ];

  const handlePlanSelect = (planIndex: number) => {
    setSelectedPlan(planIndex);

    // Save selected plan ID and billing interval to cookie
    if (filteredPlans[planIndex]) {
      const selectedPlanData = filteredPlans[planIndex];
      const existingData = Cookies.get(COOKIE_NAME);

      if (existingData) {
        const parsedData = JSON.parse(existingData);
        const updatedData = {
          ...parsedData,
          planIndex: planIndex,
          plan_id: selectedPlanData.id,
          billing_interval: billingCycle
        };
        Cookies.set(COOKIE_NAME, JSON.stringify(updatedData));
      } else {
        Cookies.set(COOKIE_NAME, JSON.stringify({
          planIndex: planIndex,
          plan_id: selectedPlanData.id,
          billing_interval: billingCycle
        }));
      }
    }
  };

  const handleSaveAsDraft = () => {
    // Save current state to cookie
    if (selectedPlan !== undefined && filteredPlans[selectedPlan]) {
      const existingData = Cookies.get(COOKIE_NAME);
      if (existingData) {
        const parsedData = JSON.parse(existingData);
        const updatedData = {
          ...parsedData,
          plan_id: filteredPlans[selectedPlan].id,
          billing_interval: billingCycle
        };
        Cookies.set(COOKIE_NAME, JSON.stringify(updatedData));
      }
    }
  };

  if (isLoading) {
    return <Loader type="plans" />;
  }

  if (error) {
    return <div className="w-full text-center py-8 text-red-500">{error}</div>;
  }

  return (
    <div className="w-full mt-2 max-h-[73vh]">
      <div className="flex flex-col">
        <div className="-mt-4">
          <ToggleButton
            billingCycle={billingCycle}
            setBillingCycle={setBillingCycle}
          />
        </div>

        <div className="grid grid-cols-3 gap-8 mt-8 mb-8 max-w-[1000px] mx-auto">
          {filteredPlans.map((plan, index) => {
            const isSelected = selectedPlan === index;
            const features = mapAPIFeaturesToUIFeatures(plan);

            return (
              <div
                key={plan.id}
                onClick={() => handlePlanSelect(index)}
                className={`h-[450px] p-7 rounded-lg flex flex-col cursor-pointer transition-all duration-200 
                  ${isSelected
                    ? "border-2 border-primary shadow-lg transform scale-[1.02]"
                    : "border border-gray-200 hover:border-primary-300"}
                `}
              >
                <div className="mb-3">
                  <h3 className="typography-body-lg font-weight-medium text-gray-900">{plan.name}</h3>
                  <div className="mt-2">
                    <span className="typography-heading-1 font-weight-bold">${plan.price}</span>
                    <span className="typography-body-sm text-gray-500 ml-1">
                      USD per {billingCycle === "monthly" ? "month" : "year"}
                    </span>
                  </div>
                </div>

                <ul className="space-y-2.5 flex-1 mb-2">
                  {features.map((feature, idx) => {
                    const Icon = featureIcons[feature.icon];
                    return (
                      <li key={idx} className="flex items-start typography-body-sm">
                        <Icon
                          className={`w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-primary`}
                        />
                        <span className={feature.available
                          ? "text-gray-900"
                          : "text-gray-500 line-through"}
                        >
                          {feature.text}
                        </span>
                      </li>
                    );
                  })}
                </ul>

                <DynamicButton
                  variant={isSelected ? "primary" : "primaryOutline"}
                  text="Select"
                  fullWidth
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePlanSelect(index);
                  }}
                />
              </div>
            );
          })}
        </div>

        <div className="flex justify-between py-1 mt-2 mb-6">
          <DynamicButton
            variant="ghost"
            text="Back"
            onClick={() => back("adminSetup")}
          />
          <div className="flex gap-4">
            <DynamicButton
              variant="ghostPrimary"
              text="Save as Draft"
              onClick={handleSaveAsDraft}
            />

              <DynamicButton
                variant="primary"
                text="Cancel"
                onClick={() => window.location.href = '/dashboard/organizations'}
              />

            <DynamicButton
              variant="primary"
              text="Next: Feature Configuration"
              disabled={selectedPlan === undefined}
              onClick={() => {
                if (selectedPlan !== undefined) {
                  onNext(selectedPlan, billingCycle);
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlanSelection;