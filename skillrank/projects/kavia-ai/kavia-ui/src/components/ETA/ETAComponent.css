
/* Optional: Add transition effects for color changes */
.eta-component {
    transition: all 0.3s ease;
  }
  
  /* Optional: Custom shadow effects for different states */
  .eta-green {
    box-shadow: 0 1px 3px hsl(var(--semantic-green-500) / 0.1);
  }

  .eta-yellow {
    box-shadow: 0 1px 3px hsl(var(--semantic-yellow-500) / 0.1);
  }

  .eta-red {
    box-shadow: 0 1px 3px hsl(var(--semantic-red-500) / 0.1);
  }

  /* Optional: Hover effects */
  .eta-component:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px hsl(var(--semantic-gray-900) / 0.1);
  }
  
  /* Container specific styles */
  .eta-container {
    min-width: 280px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .eta-component {
      padding: 12px;
    }
    
    .eta-component .text-sm {
      font-size: 0.75rem;
    }
  }