// src/components/GraphView.js

import React, { useEffect, useRef } from "react";
import { Network } from "vis-network";
import { DataSet } from "vis-data";

const GraphView = ({ graphData, onNodeClick, networkRef, graphRef }) => {
  
  const networkInstance = useRef(null);
  useEffect(() => {
    
    if (graphData.nodes.length > 0 && graphRef.current) {
      const container = graphRef.current;

      const nodes = new DataSet(graphData.nodes);
      const edges = new DataSet(graphData.edges);

      const data = { nodes, edges };

      const options = {
        nodes: {
          shape: "dot",
          size: 20,
          font: {
            size: 12,
            face: "Inter, Arial, sans-serif",
            multi: "html",
            align: "center",
            color: "hsl(215 25% 27%)",
          },
          borderWidth: 2,
          shadow: {
            enabled: true,
            color: "rgba(0,0,0,0.1)",
            size: 5,
            x: 2,
            y: 2
          },
        },
        edges: {
          width: 2,
          color: { color: "hsl(215 20% 65%)", highlight: "hsl(var(--primary-500))" },
          font: {
            size: 10,
            align: "middle",
            color: "hsl(215 25% 27%)",
          },
          arrows: {
            to: { enabled: true, scaleFactor: 0.5 },
          },
          smooth: { type: "continuous" },
        },
        layout: {
          improvedLayout: true,
          hierarchical: false,
        },
        physics: {
          enabled: true,
          stabilization: {
            enabled: true,
            iterations: 1000,
            updateInterval: 100,
            onlyDynamicEdges: false,
            fit: true,
          },
          barnesHut: {
            gravitationalConstant: -2000,
            centralGravity: 0.3,
            springLength: 95,
            springConstant: 0.04,
            damping: 0.09,
            avoidOverlap: 0.1,
          },
        },
        interaction: {
          dragNodes: true,
          dragView: true,
          zoomView: true,
          selectable: true,
          selectConnectedEdges: false,
          hoverConnectedEdges: true,
          navigationButtons: true,
          keyboard: {
            enabled: true,
            speed: { x: 10, y: 10, zoom: 0.1 },
            bindToWindow: true,
          },
        },
      };
      networkInstance.current = new Network(container, data, options);

      // Assign the network to the ref so it can be accessed from the parent component
      if (networkRef) {
        networkRef.current = networkInstance.current;
      }
    }
  }, []);

  useEffect(() => {
    if (networkRef) {
      const nodes = networkRef.current.body.data.nodes;

      const handleNodeClick = (params) => {
        if (params.nodes.length > 0) {
          const nodeId = params.nodes[0];
          const node = nodes.get(nodeId);
          onNodeClick(node);
        }
      };

      networkRef.current.on("selectNode", handleNodeClick);
    }
  }, [onNodeClick]);

  return (
    <div
      ref={graphRef}
      style={{ width: "100%", height: "100%" }}
      className="border border-custom-border bg-custom-bg-primary"
    ></div>
  );
};

export default GraphView;
