import React, { useState, useCallback, useEffect, useRef } from 'react';
import ForceGraph2D from 'react-force-graph-2d';
import { useParams } from "next/navigation";
import PropTypes from 'prop-types';

const NODE_R = 5;

const Graph2d = ({ graphData, isSearchResult, onNodeClick = () => {}, networkRef }) => {
  const [data, setData] = useState({ nodes: [], links: [] });
  const [highlightNodes, setHighlightNodes] = useState(new Set());
  const [highlightLinks, setHighlightLinks] = useState(new Set());
  const params = useParams();
  const projectId = params.projectId;
  const [hoverNode, setHoverNode] = useState(null);

  const fgRef = useRef();
  const [initialZoomDone, setInitialZoomDone] = useState(false);

  useEffect(() => {
    setData(graphData.data);
  }, [graphData]);

  useEffect(() => {
    if (fgRef.current && !initialZoomDone) {
      const fg = fgRef.current;
      fg.zoomToFit();
      setInitialZoomDone(true);
    }
  }, [data, initialZoomDone]);

  useEffect(() => {
    if (fgRef.current) {
      const fg = fgRef.current;
      networkRef.current = {
        zoomIn: () => {
          const currentZoom = fg.zoom();
          fg.zoom(currentZoom * 1.2, 400);
        },
        zoomOut: () => {
          const currentZoom = fg.zoom();
          fg.zoom(currentZoom / 1.2, 400);
        },
        reset: () => {
          fg.zoomToFit(400);
        }
      };
    }
  }, [networkRef]);

  const handleNodeHover = (node) => {
    const newHighlightNodes = new Set();
    const newHighlightLinks = new Set();

    if (node) {
      newHighlightNodes.add(node);
      node.neighbors.forEach(neighbor => newHighlightNodes.add(neighbor));
      node.links.forEach(link => newHighlightLinks.add(link));
    }

    setHoverNode(node || null);
    setHighlightNodes(newHighlightNodes);
    setHighlightLinks(newHighlightLinks);
  };

  const handleNodeClick = (node) => {
    if (node && typeof onNodeClick === 'function') {
      onNodeClick(node);
    }
  };

  const handleLinkHover = (link) => {
    highlightNodes.clear();
    highlightLinks.clear();

    if (link) {
      highlightLinks.add(link);
      highlightNodes.add(link.source);
      highlightNodes.add(link.target);
    }

    setHoverNode(null);
  };

  const paintRing = useCallback((node, ctx, globalScale) => {
    const fillColor = node === hoverNode
      ? 'hsl(0 84% 60%)'
      : highlightNodes.has(node)
      ? 'hsl(var(--primary-500))'
      : node.color || 'hsl(215 20% 65%)';

    ctx.beginPath();
    ctx.arc(node.x, node.y, NODE_R * 1.4, 0, 2 * Math.PI, false);
    ctx.fillStyle = fillColor;
    ctx.fill();

    ctx.strokeStyle = 'hsl(215 25% 27%)';
    ctx.lineWidth = 1;
    ctx.stroke();

    const label = node.type;
    const fontSize = Math.max(3, 12 / globalScale);
    ctx.font = `${fontSize}px Sans-Serif`;
    ctx.fillStyle = 'black';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const displayLabel = label.length > 4 ? label.slice(0, 4) + '..' : label;
    ctx.fillText(displayLabel, node.x, node.y);
  }, [hoverNode, highlightNodes]);


  const paintLink = useCallback((link, ctx, globalScale) => {
    const start = link.source;
    const end = link.target;

    // Draw the link
    ctx.beginPath();
    ctx.moveTo(start.x, start.y);
    ctx.lineTo(end.x, end.y);
    ctx.strokeStyle = highlightLinks.has(link) ? 'hsl(var(--primary-500))' : 'hsl(215 20% 65%)';
    ctx.lineWidth = highlightLinks.has(link) ? 2 : 1;
    ctx.stroke();

    // Draw the link label only when zoomed out
    if (link.type && globalScale > 3) {
      const midX = (start.x + end.x) / 2;
      const midY = (start.y + end.y) / 2;

      ctx.font = `${Math.max(8, 12 / globalScale)}px Sans-Serif`;
      ctx.fillStyle = 'black';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(link.type, midX, midY);
    }
  }, [highlightLinks]);

  return (
    <div className='fixed'>
      <ForceGraph2D
        ref={fgRef}
        graphData={data}
        nodeRelSize={NODE_R}
        width={1050}
        height={600}
        autoPauseRedraw={false}
        nodeAutoColorBy="type"
        linkLabel='type'
        nodeLabel='type'
        linkWidth={1}
        linkDirectionalParticles={4}
        linkDirectionalParticleWidth={link => highlightLinks.has(link) ? 4 : 0}
        nodeCanvasObjectMode={() => 'after'}
        nodeCanvasObject={paintRing}
        linkCanvasObjectMode={() => 'after'}
        linkCanvasObject={paintLink}
        onNodeHover={handleNodeHover}
        onLinkHover={handleLinkHover}
        onNodeClick={handleNodeClick}
        // d3AlphaDecay={0.01}
        d3AlphaDecay={isSearchResult ? 0.1 : 0.01}
        // d3VelocityDecay={0.08}
        d3VelocityDecay={isSearchResult ? 0.8 : 0.08}
        // cooldownTicks={isSearchResult ? 2 : 100}
        // onEngineStop={() => fgRef.current.zoomToFit(300)}
      />
    </div>
  );
};

Graph2d.propTypes = {
  graphData: PropTypes.object.isRequired,
  isSearchResult: PropTypes.bool,
  onNodeClick: PropTypes.func,
  networkRef: PropTypes.object,
};

export default Graph2d;