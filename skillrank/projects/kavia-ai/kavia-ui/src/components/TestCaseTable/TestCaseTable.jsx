import React, { useState } from 'react';
import { ChevronUp, ChevronDown, CheckCircle, Eye, AlertTriangle, BookOpen } from 'lucide-react';
import Pagination from '../UIComponents/Paginations/Pagination';

const TableHeader = ({ requestSort, sortConfig }) => {
  const renderColumn = (field) => {
    const commonProps = {
      requestSort: requestSort,
      sortConfig: sortConfig
    };

    switch (field) {
      case 'id':
        return <SortableHeader title="#" align="center" field="id" {...commonProps} />;
      case 'title':
        return <SortableHeader title="TITLE" field="title" {...commonProps} />;
      case 'type':
        return <SortableHeader title="TYPE" align="center" field="type" {...commonProps} />;
      case 'category':
        return <SortableHeader title="CATEGORY" align="center" field="category" {...commonProps} />;
      case 'actions':
        return <th scope="col" className="px-3 py-3"></th>;
      default:
        return null;
    }
  };

  const columns = ['id', 'title', 'type', 'category', 'actions'];

  return (
    <thead className="typography-heading-6 text-[#687182] bg-[#F7F9FCCC] border-b" style={{ position: 'sticky', top: 0, zIndex: 1 }}>
      <tr>
        {columns.map((column, index) => (
          <React.Fragment key={index}>
            {renderColumn(column)}
          </React.Fragment>
        ))}
      </tr>
    </thead>
  );
};

const SortableHeader = ({ title, field, requestSort, sortConfig, icon, align }) => (
  <th scope="col" className={`px-3 py-3 cursor-pointer ${align === 'center' ? 'text-center' : 'text-left'}`} onClick={() => requestSort(field)}>
    <div className={`flex items-center gap-1 whitespace-nowrap ${align === 'center' ? 'justify-center' : ''}`}>
      {icon}
      {title}
      <div className="inline-block ml-1">
        <ChevronUp
          className={`h-3 w-3 ${sortConfig.key === field && sortConfig.direction === 'ascending'
            ? 'text-primary'
            : 'text-[#A1A9B8]'
            }`}
        />
        <ChevronDown
          className={`h-3 w-3 ${sortConfig.key === field && sortConfig.direction === 'descending'
            ? 'text-primary'
            : 'text-[#A1A9B8]'
            }`}
        />
      </div>
    </div>
  </th>
);

const TypeBadge = ({ type }) => {
  const typeConfig = {
    'FunctionalTestCase': {
      icon: CheckCircle,
      styles: 'bg-primary-100 text-primary-800 border-primary-200',
      iconColor: 'text-primary',
      displayText: 'Functional'
    },
    'NonFunctionalTestCase': {
      icon: Eye,
      styles: 'bg-purple-100 text-purple-800 border-purple-200',
      iconColor: 'text-purple-600',
      displayText: 'Non-Functional'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-gray-100 text-gray-600 border-gray-200',
      iconColor: 'text-gray-500',
      displayText: 'Unknown'
    }
  };

  const config = typeConfig[type || 'default'] || typeConfig['default'];
  const Icon = config.icon || AlertTriangle;

  return (
    <span className={`inline-flex items-center px-2 py-1 typography-caption font-weight-medium rounded-lg border whitespace-nowrap ${config.styles}`}>
      <Icon className={`w-3 h-3 mr-1.5 ${config.iconColor}`} />
      {config.displayText}
    </span>
  );
};

const CategoryBadge = ({ category }) => {
  const categoryConfig = {
    'User Interface': {
      styles: 'bg-green-100 text-green-800 border-green-200',
    },
    'Usability': {
      styles: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    },
    'Performance': {
      styles: 'bg-primary-100 text-primary-800 border-primary-200',
    },
    'default': {
      styles: 'bg-gray-100 text-gray-600 border-gray-200',
    }
  };

  const config = categoryConfig[category || 'default'] || { styles: 'bg-gray-100 text-gray-600 border-gray-200' };

  return (
    <span className={`inline-flex items-center px-2 py-1 typography-caption font-weight-medium rounded-lg border whitespace-nowrap ${config.styles}`}>
      {category || 'Not Set'}
    </span>
  );
};

const TableRow = ({ item, handleItemClick }) => {
  const renderCell = (type) => {
    switch (type) {
      case 'id':
        return <td className="px-3 py-3 text-gray-500 text-center">{item.id}</td>;
      case 'title':
        return (
          <td className="px-3 py-3">
            <div
              className="font-weight-medium text-gray-700 cursor-pointer hover:text-primary hover:underline"
              onClick={() => handleItemClick(item)}
            >
              {item.properties.Title || 'Untitled Test Case'}
            </div>
            <div className="typography-caption text-gray-500 mt-1 line-clamp-1">
              {item.properties.Description || 'No description available'}
            </div>
          </td>
        );
      case 'type':
        return (
          <td className="px-3 py-3 text-center">
            <TypeBadge type={item.properties.Type} />
          </td>
        );
      case 'category':
        return (
          <td className="px-3 py-3 text-center">
            <CategoryBadge category={item.properties.Category} />
          </td>
        );
      case 'actions':
        return (
          <td className="px-3 py-3 text-center">
            <button
              className="text-gray-400 hover:text-primary p-1 rounded-full hover:bg-gray-100"
              onClick={() => handleItemClick(item)}
              title="View details"
            >
              <BookOpen className="w-4 h-4" />
            </button>
          </td>
        );
      default:
        return <td className="px-3 py-3"></td>;
    }
  };

  const cellTypes = ['id', 'title', 'type', 'category', 'actions'];

  return (
    <tr className="bg-white border-b hover:bg-gray-50">
      {cellTypes.map((cellType, index) => (
        <React.Fragment key={index}>
          {renderCell(cellType)}
        </React.Fragment>
      ))}
    </tr>
  );
};

const TestCaseTable = ({
  testCases,
  onItemClick,
  currentPage = 1,
  pageSize = 10,
  onPageChange,
  onPageSizeChange
}) => {
  const [sortConfig, setSortConfig] = useState({
    key: 'id',
    direction: 'ascending',
  });

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const sortedTestCases = React.useMemo(() => {
    if (!testCases) return [];

    const sortableItems = [...testCases];

    sortableItems.sort((a, b) => {
      if (sortConfig.key === 'id') {
        return sortConfig.direction === 'ascending'
          ? a.id - b.id
          : b.id - a.id;
      } else if (sortConfig.key === 'title') {
        const aValue = a.properties.Title || '';
        const bValue = b.properties.Title || '';
        return sortConfig.direction === 'ascending'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else if (sortConfig.key === 'type' || sortConfig.key === 'category') {
        const aValue = a.properties[sortConfig.key === 'type' ? 'Type' : 'Category'] || '';
        const bValue = b.properties[sortConfig.key === 'type' ? 'Type' : 'Category'] || '';
        return sortConfig.direction === 'ascending'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      return 0;
    });

    return sortableItems;
  }, [testCases, sortConfig]);

  // Calculate pagination
  const pageCount = Math.ceil((testCases?.length || 0) / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedTestCases = sortedTestCases.slice(startIndex, startIndex + pageSize);

  const handlePageSizeChange = (newSize) => {
    if (onPageSizeChange) {
      onPageSizeChange(newSize);
    }
  };

  const handlePaginationPageChange = (page) => {
    if (onPageChange) {
      onPageChange(page);
    }
  };

  if (!testCases || testCases.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-40 border rounded-md bg-gray-50">
        <AlertTriangle className="w-10 h-10 text-gray-400 mb-2" />
        <p className="text-gray-500">No test cases available</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto shadow-sm rounded-lg border border-gray-200">
      <table className="w-full typography-body-sm text-left">
        <TableHeader
          requestSort={requestSort}
          sortConfig={sortConfig}
        />
        <tbody>
          {paginatedTestCases.map((testCase) => (
            <TableRow
              key={testCase.id}
              item={testCase}
              handleItemClick={onItemClick}
            />
          ))}
        </tbody>
      </table>

      {testCases.length > 5 && (
        <div className="bg-white border-t border-gray-200 px-4 py-3">
          <Pagination
            currentPage={currentPage}
            pageCount={pageCount}
            pageSize={pageSize}
            totalItems={testCases.length}
            onPageChange={handlePaginationPageChange}
            onPageSizeChange={handlePageSizeChange}
            pageSizeOptions={[5, 10, 20, 50]}
          />
        </div>
      )}
    </div>
  );
};

export default TestCaseTable;