'use client';

import { useState, useEffect } from 'react';
import { FileText, Folder } from 'lucide-react'
import { getDocumentationTypes } from '@/utils/documentationAPI';

const DOC_TYPE_ICONS = {
  "PRD": { name: "Product Requirements", icon: FileText },
  "SAD": { name: "System Architecture", icon: FileText },
  "API": { name: "API Documentation", icon: FileText },
  "SAVED": { name: "Saved Documentation", icon: FileText }
};

export function DocumentSidebar({ onTypeSelect, selectedType }) {
  const [documentTypes, setDocumentTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDocTypes = async () => {
      try {
        setIsLoading(true);
        const types = await getDocumentationTypes();
        setDocumentTypes(types);
      } catch (err) {
        setError('Failed to load document types');
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocTypes();
  }, []);

  if (isLoading) {
    return (
      <div className="w-64 bg-[#f8f9fa] border-r border-gray-200 h-full flex items-center justify-center">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-64 bg-[#f8f9fa] border-r border-gray-200 h-full flex items-center justify-center">
        <div className="text-red-500 typography-body-sm px-4 text-center">{error}</div>
      </div>
    );
  }

  return (
    <div className="w-64 bg-[#f8f9fa] border-r border-gray-200 h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h2 className="typography-body-sm font-weight-medium text-gray-900">Documents</h2>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {documentTypes.map((type) => {
          const IconComponent = DOC_TYPE_ICONS[type]?.icon || Folder;
          return (
            <div key={type} className="border-b border-gray-100 last:border-0">
              <button
                onClick={() => onTypeSelect(type)}
                className={`w-full flex items-center px-3 py-2 hover:bg-gray-100 typography-body-sm ${
                  selectedType === type ? 'bg-primary-50 text-primary' : 'text-gray-700'
                }`}
              >
                <IconComponent 
                  className="mr-2 text-gray-400" 
                  size="20" 
                />
                <span className="flex-1 text-left">
                  {DOC_TYPE_ICONS[type]?.name || type}
                </span>
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
}