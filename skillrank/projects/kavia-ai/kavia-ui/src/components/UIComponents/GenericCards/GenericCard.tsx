import React from 'react';

// Define the props interface
interface GenericCardProps {
    title?: string;
    description?: string;
    id?: string;
    type?: string;
    containerName?: string;
    branch?: string; // Add this new prop
    isHasActionClick?: boolean;
    onActionClick?: (e: React.MouseEvent) => void;
    onClick?: () => void;
}

const GenericCard: React.FC<GenericCardProps> = ({
    title,
    description,
    id,
    type,
    containerName,
    branch = null,
    isHasActionClick = false,
    onActionClick,
    onClick
}) => {
    // Determine container label color based on name
    const getContainerLabelClass = (name: string): string => {
        // If no name is provided, return a default color
        if (!name) return 'bg-semantic-purple-100 text-semantic-purple-800';

        // Generate a hash code from the name length
        const hashCode = name.length % 10;

        // Define color palettes with different intensity levels using semantic colors
        const colorPalettes = [
            { bg: 'bg-primary-100', text: 'text-primary-800' },
            { bg: 'bg-semantic-green-100', text: 'text-semantic-green-800' },
            { bg: 'bg-semantic-red-100', text: 'text-semantic-red-800' },
            { bg: 'bg-semantic-yellow-100', text: 'text-semantic-yellow-800' },
            { bg: 'bg-primary-100', text: 'text-primary-800' },
            { bg: 'bg-semantic-red-100', text: 'text-semantic-red-800' },
            { bg: 'bg-semantic-purple-100', text: 'text-semantic-purple-800' },
            { bg: 'bg-semantic-green-100', text: 'text-semantic-green-800' },
            { bg: 'bg-primary-100', text: 'text-primary-800' },
            { bg: 'bg-primary-100', text: 'text-primary-800' }
        ];

        // Select a color palette based on the hash code
        const selectedPalette = colorPalettes[hashCode];

        return `${selectedPalette.bg} ${selectedPalette.text}`;
    };

    // Handle the action click to prevent event propagation
    const handleActionClick = (e: React.MouseEvent) => {
        // Stop the click from bubbling up to the parent div
        e.stopPropagation();

        // Call the onActionClick handler if provided
        if (onActionClick) {
            onActionClick(e);
        }
    };

    return (
        <div
            onClick={onClick}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:border-primary transition-all duration-200"
        >
            {containerName && (
                <div className="flex items-center justify-between mb-3">
                    <span className={`px-2 py-1 rounded-md typography-caption ${getContainerLabelClass(containerName || "Untitled Container")}`}>
                        {containerName || "Untitled Container"}
                    </span>
                </div>
            )}

            <h3 className="typography-body-lg font-weight-semibold text-gray-900 mb-2">
                {title || "Untitled"}
            </h3>

            <p
                className="typography-body-sm text-gray-600 mb-4 custom-table-text-ellipsis"
                title={description}
            >
                {description || "No description available"}
            </p>

            <div className="flex items-center justify-between typography-body-sm text-gray-500">
                <div className="flex items-center gap-2">
                    <span>{id}</span>
                    {
                        branch && (
                        <span className="px-2 py-1 rounded-md typography-caption bg-gray-100 text-gray-700">
                            {branch}
                        </span>
                        )
                    }

                </div>
                {type && (
                    <span className={`px-2 py-1 rounded-md typography-caption ${getContainerLabelClass(type || "N/A")}`}>
                        {type || "N/A"}
                    </span>
                )}
            </div>


        </div>
    );
};

export default GenericCard;