import React from 'react';
import Badge from "@mui/material/Badge";
import { BootstrapTooltip } from '../ToolTip/Tooltip-material-ui';
import { styled } from '@mui/material/styles';

import '@/styles/components/UI.css';

interface IconButtonProps {
  icon: React.ReactNode;
  text?: string; // New prop for text
  tooltip?: string;
  onClick: (e?: React.MouseEvent) => void;
  className?: string;
  badgeContent?: number;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  variant?: string;
  theme?: 'light' | 'dark';
}

// Custom styled Badge component for theme support
const ThemedBadge = styled(Badge, {
  shouldForwardProp: (prop) => prop !== 'customTheme',
})<{ customTheme?: 'light' | 'dark' }>(({ customTheme }) => ({
  '& .MuiBadge-badge': {
    backgroundColor: 'hsl(var(--primary))', // Use theme primary color
    color: 'hsl(var(--primary-foreground))',
  },
}));

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  text,
  tooltip,
  onClick,
  className = '',
  badgeContent,
  placement = 'bottom-end',
  variant = 'default',
  theme = 'light'
}) => {
  const buttonContent = (
    <>
      {badgeContent !== undefined ? (
        <ThemedBadge
          badgeContent={badgeContent}
          customTheme={theme}
          anchorOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          {icon}
        </ThemedBadge>
      ) : (
        icon
      )}
      {text && <span className="ml-2 typography-body-sm text-gray-600 font-weight-medium">{text}</span>}
    </>
  );

  return (
    <BootstrapTooltip title={tooltip || ''} placement={placement}>
      <button
        onClick={onClick}
        className={`${className} ${variant === 'small' ? 'p-2' : 'px-3 py-2'} icon-button group flex items-center`}
        aria-label={tooltip}
      >
        {buttonContent}
      </button>
    </BootstrapTooltip>
  );
};