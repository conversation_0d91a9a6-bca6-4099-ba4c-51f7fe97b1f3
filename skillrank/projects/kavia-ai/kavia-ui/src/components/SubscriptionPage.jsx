'use client';

import { useEffect, useState } from 'react';
import { getStripeBillingPortal } from '@/utils/api';
import { useUser } from '@/components/Context/UserContext';
import { CreditCard, ExternalLink } from 'lucide-react';
import EmptyStateView from "@/components/Modal/EmptyStateModal";

export default function SubscriptionPage() {
  const [billingUrl, setBillingUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { email } = useUser();

  useEffect(() => {
    if (email) {
      fetchBillingPortal();
    } else {
      setIsLoading(false);
      setError('User email not available');
    }
  }, [email]);

  const fetchBillingPortal = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await getStripeBillingPortal(email);


      if (response?.url) {
        setBillingUrl(response.url);
      } else {
        // No URL in response
        setError('No billing portal URL available');
      }
    } catch (err) {

      setError('Failed to load billing information');
    } finally {
      setIsLoading(false);
    }
  };

  const openBillingPortal = () => {
    if (billingUrl) {
      window.open(billingUrl, '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-12 px-4">
        <h2 className="typography-heading-4 font-weight-semibold mb-6 flex items-center">
          <CreditCard className="mr-2" size={20} />
          Manage Subscriptions
        </h2>
        <div className="bg-white p-8 rounded-lg shadow-sm flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // No billing URL available - use EmptyStateView with the new type
  if (!billingUrl || error) {
    return (
      <div className="container mx-auto py-12 px-4">
        <h2 className="typography-heading-4 font-weight-semibold mb-6 flex items-center">
          <CreditCard className="mr-2" size={20} />
          Manage Subscriptions
        </h2>
        <EmptyStateView
          type="noSubscriptionData"
          onClick={fetchBillingPortal}
        />
      </div>
    );
  }

  // If we have a valid billing URL
  return (
    <div className="container mx-auto py-12 px-4">
      <h2 className="typography-heading-4 font-weight-semibold mb-6 flex items-center">
        <CreditCard className="mr-2" size={20} />
        Manage Subscriptions
      </h2>

      <div className="bg-white p-8 rounded-lg shadow-sm">
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center">
            <CreditCard className="text-primary" size={20} />
          </div>
        </div>

        <h3 className="typography-body-lg font-weight-medium text-center mb-2">Stripe Billing Portal</h3>
        <p className="text-gray-600 text-center typography-body-sm mb-6">
          View your current subscription and invoice history.
        </p>

        <div className="bg-primary-50 border-l-4 border-primary p-4 mb-6">
          <p className="typography-body-sm text-primary-800">You can manage:</p>
          <ul className="typography-body-sm ml-5 mt-2 text-primary-700 list-disc">
            <li>Current subscription details</li>
            <li>Past invoices and payment history</li>
          </ul>
        </div>

        <div className="flex justify-center">
          <button
            onClick={openBillingPortal}
            className="bg-primary hover:bg-primary-600 text-white px-6 py-2 rounded-md flex items-center"
          >
            <ExternalLink size={16} className="mr-2" />
            View Billing Details
          </button>
        </div>
      </div>
    </div>
  );
}