//@ts-ignore
//@ts-nocheck
"use client";

import * as React from "react";
import { ChatContext } from "./Context/ChatContext";
import { useContext, useState, useRef, useEffect, useCallback } from "react";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { getCookie } from "../utils/auth";
import ConfirmationModal from "./Modal/ConfirmationModal";
import Image from "next/image";
import CodeBlock from "./CodeBlock";
import chaticon from "../../public/images/chat_icon.svg";
import { AlertContext } from "./NotificationAlertService/AlertList";
import { TopBarContext } from "./Context/TopBarContext";
import { fetchChatHistory } from "@/utils/api";
import { CSSTransition } from "react-transition-group";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { formatUTCToLocal } from "@/utils/datetime";
import { extractText, uploadFile } from "@/utils/fileAPI";
import FileContentModal from "./Modal/FileContentModal";
import AttachmentButton from "./Buttons/AttachmentButton";
import AttachmentWindow from "./File/AttachmentWindow";
import FileAttachment from "./File/FileAttachment";
import { BootstrapTooltip } from "./UIComponents/ToolTip/Tooltip-material-ui";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Cookies from "js-cookie";
import { useUser } from "@/components/Context/UserContext";

interface Tab {
  href: string;
  title: string;
  // Add any other properties that a tab object might have
}
interface ChatPanelProps {
  maxFiles?: number;
  acceptedFileTypes?: string[];
  attachmentEnabled?: boolean;
}

interface ChatHistoryItem {
  discussion_id: string | null;
  name: string;
  created_at: string;
}

export default function ChatPanel({
  maxFiles = 5,
  acceptedFileTypes = [".pdf", ".png"],
  attachmentEnabled = true,
}: ChatPanelProps) {
  const router = useRouter();

  const { is_admin, tenant_id, fetchUserData } = useUser();

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [functionCallData, setFunctionCallData] = useState(null);
  const [projectId, setProjectId] = useState("");
  const { updateTabHref } = useContext(TopBarContext); //to set the tabzz
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const kebabButtonRef = useRef<HTMLButtonElement>(null);
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDiscussionName, setSelectedDiscussionName] =
    useState("Discussion");
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const projectID = parseInt(pathname.split("/")[3], 10);
  const currentDiscussionId = searchParams.get("discussion_id");
  const [username, setUsername] = useState("");

  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [showAttachmentWindow, setShowAttachmentWindow] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const attachmentButtonRef = useRef<HTMLButtonElement>(null);

  const [fileStatuses, setFileStatuses] = useState({});
  const [selectedFile, setSelectedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const dropZoneRef = useRef(null);

  const { showAlert } = useContext(AlertContext);

  const handleAttachmentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (attachedFiles.length === 0) {
      fileInputRef.current?.click();
    } else {
      setShowAttachmentWindow((prev) => !prev);
    }
  };

  const handleAddAttachment = (e: React.MouseEvent) => {
    e.preventDefault();
    fileInputRef.current?.click();
  };

  const handleClearAttachments = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setAttachedFiles([]);
    setFileStatuses({});
    setShowAttachmentWindow(false);
  };

  const handleFileUpload = useCallback(
    async (files) => {
      setAttachedFiles((prevAttachedFiles) => {
        const updatedFiles = [...prevAttachedFiles];
        const newFileStatuses = { ...fileStatuses };

        if (updatedFiles.length + files.length <= maxFiles) {
          files.forEach((file) => {
            const existingFileIndex = updatedFiles.findIndex(
              (f) => f.file_name === file.name
            );
            if (existingFileIndex !== -1) {
              return;
            }

            const newFile = {
              file_name: file.name,
              file_type: file.type,
              file_size: file.size,
              file_kind: file.type.startsWith("image/") ? "image" : "document",
              extracted_content: "Loading...",
            };
            updatedFiles.push(newFile);
            newFileStatuses[file.name] = "loading";
          });

          setFileStatuses(newFileStatuses);

          files.forEach(async (file) => {
            try {
              const formData = new FormData();
              formData.append("file", file);
              setIsUploading(true);
              let response;
              if (file.type.startsWith("image/")) {
                response = await uploadFile(file, discussionId);
              } else {
                response = await extractText(formData);
              }

              setAttachedFiles((prevFiles) => {
                const updatedFiles = [...prevFiles];
                const index = updatedFiles.findIndex(
                  (f) => f.file_name === file.name
                );
                if (index !== -1) {
                  if (file.type.startsWith("image/")) {
                    updatedFiles[index] = {
                      ...updatedFiles[index],
                      ...response,
                    };
                  } else {
                    updatedFiles[index].extracted_content =
                      response.text || response.extracted_content;
                  }
                }
                return updatedFiles;
              });

              setFileStatuses((prevStatuses) => ({
                ...prevStatuses,
                [file.name]: "loaded",
              }));
            } catch (error) {

              let errorMessage = "An error occurred while processing the file.";
              if (error.message.includes("400: Unsupported file type")) {
                errorMessage = `Unsupported file type: ${file.name}`;
              } else if (
                error.response &&
                error.response.data &&
                error.response.data.detail
              ) {
                errorMessage = error.response.data.detail;
              }
              showAlert(errorMessage, "error");
              setAttachedFiles((prevFiles) =>
                prevFiles.filter((f) => f.file_name !== file.name)
              );
              setFileStatuses((prevStatuses) => {
                const newStatuses = { ...prevStatuses };
                delete newStatuses[file.name];
                return newStatuses;
              });
            } finally {
              setIsUploading(false);
            }
          });
        } else {
          showAlert(`You can only attach up to ${maxFiles} files.`, "warning");
        }
        return updatedFiles;
      });
    },
    [maxFiles, fileStatuses, showAlert]
  );

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      await handleFileUpload(Array.from(files));
    }
  };

  const handleFileClick = (file) => {
    setSelectedFile(file);
  };

  useEffect(() => {
    const handlePaste = async (e) => {
      const items = e.clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf("image") !== -1) {
          e.preventDefault();
          const file = items[i].getAsFile();
          await handleFileUpload([file]);
        }
      }
    };

    const handleDragOver = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.add("bg-primary-100");
    };

    const handleDragLeave = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.remove("bg-primary-100");
    };

    const handleDrop = async (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.remove("bg-primary-100");
      const files = Array.from(e.dataTransfer.files);
      await handleFileUpload(files);
    };

    document.addEventListener("paste", handlePaste);
    dropZoneRef.current.addEventListener("dragover", handleDragOver);
    dropZoneRef.current.addEventListener("dragleave", handleDragLeave);
    dropZoneRef.current.addEventListener("drop", handleDrop);

    return () => {
      document.removeEventListener("paste", handlePaste);
      dropZoneRef.current?.removeEventListener("dragover", handleDragOver);
      dropZoneRef.current?.removeEventListener("dragleave", handleDragLeave);
      dropZoneRef.current?.removeEventListener("drop", handleDrop);
    };
  }, [handleFileUpload]);

  // Update handleFormSubmit
  const handleFormSubmit = (event) => {
    event.preventDefault();
    if (isUploading || loading) return;

    if (currentMessage.trim() !== "" || attachedFiles.length > 0) {
      setLoading(true);
      const newMessage = {
        id: Date.now(),
        text: currentMessage,
        sender: "user",
        file_attachments: attachedFiles,
      };
      addMessage(newMessage);
      addMessage({ text: "Processing...", sender: "AI" });
      setCurrentMessage("");
      setAttachedFiles([]);
      const textarea = document.getElementById("chat-input");
      if (textarea) {
        textarea.style.height = "auto";
        textarea.style.overflowY = "hidden";
      }
    }
    handleMessageSubmit(currentMessage, attachedFiles);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        attachmentButtonRef.current &&
        !attachmentButtonRef.current.contains(event.target as Node) &&
        !document
          .querySelector(".attachment-window")
          ?.contains(event.target as Node)
      ) {
        setShowAttachmentWindow(false);
      }
    };

    // Only add the event listener when the attachment window is open
    if (showAttachmentWindow) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showAttachmentWindow]);

  useEffect(() => {
    (async () => {
      const email = await getCookie("email");
      setUsername(email ? email : "User");
    })();
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        kebabButtonRef.current &&
        !kebabButtonRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  const [isNewDiscussion, setIsNewDiscussion] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("isNewDiscussion") === "true";
    }
    return false;
  });

  const fetchData = async () => {
    setLoading(true);
    try {
      const filteredHistory = await fetchChatHistory(undefined, projectID);
      const history = filteredHistory.filter(
        (item: any) => item.name !== "Untitled Discussion"
      );
      if (history.length === 0) {
        setChatHistory([]);
      } else {
        setChatHistory(history);

        const currentDiscussionId = searchParams.get("discussion_id");
        const currentDiscussion = history.find(
          (item: ChatHistoryItem) => item.discussion_id === currentDiscussionId
        );
        if (currentDiscussion) {
          setSelectedDiscussionName(currentDiscussion.name);
        } else {
          setSelectedDiscussionName("Discussion");
        }

        // Only append discussion_id if it's not a new discussion and there's no discussion_id in the URL
        if (!isNewDiscussion && !searchParams.get("discussion_id")) {
          const sortedHistory = history.sort(
            (a: ChatHistoryItem, b: ChatHistoryItem) =>
              new Date(b.created_at).getTime() -
              new Date(a.created_at).getTime()
          );
          const mostRecent = sortedHistory[0];
          if (mostRecent && mostRecent.discussion_id) {
            const url = new URL(window.location.href);
            const params = new URLSearchParams(url.search);
            params.set("discussion_id", mostRecent.discussion_id.toString());
            const newUrl = `${url.pathname}?${params.toString()}`;
            router.push(newUrl);
          }
        }
      }
      // Save the current projectID to localStorage
      localStorage.setItem("lastFetchedProjectID", projectID.toString());
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const handleNewDiscussion = () => {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    params.delete("discussion_id");
    const newUrl = `${url.pathname}?${params.toString()}`;
    router.push(newUrl, { scroll: false });
    setIsNewDiscussion(true);
    localStorage.setItem("isNewDiscussion", "true");
    setSelectedDiscussionName("New Discussion");
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles(attachedFiles.filter((_, i) => i !== index));
  };

  useEffect(() => {
    const lastFetchedProjectID = localStorage.getItem("lastFetchedProjectID");
    const currentDiscussionId = searchParams.get("discussion_id");

    if (projectID.toString() !== lastFetchedProjectID) {
      // ProjectID has changed, fetch new data
      setIsNewDiscussion(false);
      localStorage.removeItem("isNewDiscussion");
      fetchData();
    } else if (currentDiscussionId && !isNewDiscussion) {
      // There's a discussion_id in the URL and it's not a new discussion
      setIsNewDiscussion(false);
      localStorage.removeItem("isNewDiscussion");
      // Don't fetch data here, as it's already fetched for this projectID
    }
  }, [projectID]);

  const toggleDropdown = () => {
    setIsDropdownOpen((prev) => !prev);
    if (!isDropdownOpen) {
      fetchData();
    }
  };

  const handleDropdownItemClick = (
    discussionId: string | null,
    discussionName: string
  ) => {
    if (discussionId) {
      const url = new URL(window.location.href);
      const params = new URLSearchParams(url.search);

      // Update or add the discussion_id parameter
      params.set("discussion_id", discussionId);

      // Construct the new URL with the updated parameters
      const newUrl = `${url.pathname}?${params.toString()}`;

      // Use the router to navigate to the new URL
      router.push(newUrl);
      setSelectedDiscussionName(discussionName);
    }
    setIsDropdownOpen(false);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const filteredChatHistory = chatHistory.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getProjectIdFromUrl = () => {
    if (typeof window !== "undefined") {
      const urlObj = new URL(window.location.href);
      const pathname = urlObj.pathname;
      const pathSegments = pathname.split("/");
      const projectIdIndex =
        pathSegments.findIndex((segment) => segment === "project") + 1;
      return pathSegments[projectIdIndex];
    }
    return null;
  };

  useEffect(() => {
    const projectId = getProjectIdFromUrl();
    if (projectId) {
      setProjectId(projectId);
    }
  }, []);

  useEffect(() => {
    if (isDropdownOpen) {
      setIsDropdownOpen(false);
    }
  }, [projectID]);

  // useEffect(() => {
  //   fetchData();
  // }, [projectID]);

  const handleConfirmation = (data: any) => {
    addMessage({ text: data, sender: "AI" });
  };

  const {
    messages = {},
    setMessages,
    addMessage,
    removeLastMessage,
    discussionId,
    setDiscussionId,
  } = useContext(ChatContext);

  const [currentMessage, setCurrentMessage] = useState("");
  const messageContainerRef = useRef<HTMLDivElement>(null);

  const handleTextareaResize = (e: any) => {
    const textarea = e.target;

    // Reset the height
    textarea.style.height = "auto";

    // Calculate the new height based on the content
    const newHeight = textarea.scrollHeight;

    // Set the textarea height based on the content
    textarea.style.height = `${newHeight}px`;

    // Define the maximum height before scrollbar should appear
    const maxHeight = 150; // Set this to whatever maximum height you want

    // If content height exceeds maxHeight, allow scrolling
    if (newHeight > maxHeight) {
      textarea.style.height = `${maxHeight}px`; // Restrict the height to maxHeight
      textarea.style.overflowY = "scroll"; // Enable scrolling
    } else {
      textarea.style.overflowY = "hidden"; // Disable scrolling if content fits
    }
  };

  // Handle input change
  const handleInputChange = (event: any) => {
    setCurrentMessage(event.target.value);
  };

  const handleKeyPress = (event: any) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleFormSubmit(event);
    }
  };

  const handleMessageSubmit = async (message: any, file_attachments: any) => {
    if (loading) return; // Prevent multiple submissions

    let url = `${
      process.env.NEXT_PUBLIC_API_URL
    }/conversation/chat?project_id=${projectId}&message=${encodeURIComponent(
      message
    )}`;

    if (discussionId) {
      url += `&discussion_id=${discussionId}`;
    }

    setLoading(true);

    const controller = new AbortController();
    controller.signal.addEventListener("abort", () => {});

    const eventSource = fetchEventSource(url, {
      signal: controller.signal,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${Cookies.get("idToken")}`,
        "X-Tenant-Id": `${Cookies.get("tenant_id")}`,
      }, // Changed to use getHeaders() which returns proper Headers object
      openWhenHidden: true,
      onopen: (response) => {
        return Promise.resolve();
      },
      onmessage: async (event) => {
        try {
          // stop the event using stop key from backend
          if (event.event === "stop" && event.data === "stopped") {
            controller.abort();
            setLoading(false);
            return;
          }

          let json = JSON.parse(event.data);
          // const content = await renderHTML(json.content);
          const content = json.content;

          if (json.discussion_id) {
            const updated_project_id = getProjectIdFromUrl();
            const { buildProjectUrl } = require('@/utils/navigationHelpers');
            const chatUrl = `${buildProjectUrl(updated_project_id, 'overview')}?discussion_id=${json.discussion_id}`;

            updateTabHref(updated_project_id, chatUrl);
            router.push(`?discussion_id=${json.discussion_id}`, {
              scroll: false,
            });
          } else {
            removeLastMessage();

            if (content) {
              addMessage({ text: content, sender: "AI" });
            }
          }

          if (json.function_call) {
            // let streamingResponse = "Asking for confirmation";
            // addMessage({ text: streamingResponse, sender: "AI" });
            if (json.function_call["function"] == "configure_node") {
              // handleModalToggle();
            } else {
              setFunctionCallData(json.function_call);
              setShowModal(true);
            }
          }
        } catch (error) {
          controller.abort();
          setLoading(false);
          removeLastMessage(); // Remove the "Typing..." message
          addMessage({
            text: "Error: Unable to process the response.",
            sender: "AI",
          });
        }
      },
      onerror: (error) => {

        return null;
      },
      onclose: () => {
        setLoading(false);

        return Promise.resolve();
      },
    });
  };

  // Ensure the controller is only aborted once

  useEffect(() => {
    messageContainerRef.current?.scrollTo({
      top: messageContainerRef.current.scrollHeight,
      behavior: "smooth",
    });
  }, [messages]);

  return (
    <div
      className="chat-panel flex flex-col h-full overflow-hidden bg-white pt-2"
      ref={dropZoneRef}
    >
      <div className="flex justify-between items-center px-2 bg-gray-50 text-gray-600 shadow-sm border-b">
        <div>
          <h2 className="project-panel-heading ml-1">
            {selectedDiscussionName}
          </h2>
        </div>
        <div className="flex -mr-0.5">
          <BootstrapTooltip title="Chat History" placement="bottom">
            <button
              ref={kebabButtonRef}
              onClick={toggleDropdown}
              className="p-2 rounded-full hover:bg-gray-200 focus:outline-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="size-6"
              >
                <path
                  fillRule="evenodd"
                  d="M4.848 2.771A49.144 49.144 0 0 1 12 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 0 1-3.476.383.39.39 0 0 0-.297.17l-2.755 4.133a.75.75 0 0 1-1.248 0l-2.755-4.133a.39.39 0 0 0-.297-.17 48.9 48.9 0 0 1-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97ZM6.75 8.25a.75.75 0 0 1 .75-.75h9a.75.75 0 0 1 0 1.5h-9a.75.75 0 0 1-.75-.75Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H7.5Z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </BootstrapTooltip>

          <button
            onClick={handleNewDiscussion}
            className="mr-2 bg-white text-black my-1.5 px-2 rounded flex items-center justify-center typography-body-sm border-[1.5px] border-gray-200"
          >
            <BootstrapTooltip
              title="Start an new discussion"
              placement="bottom"
            >
              {" "}
              <span className="typography-body">New</span>
            </BootstrapTooltip>
          </button>
        </div>
      </div>
      {isDropdownOpen && (
        <div className="fixed inset-0 bg-black opacity-50 z-20"></div>
      )}

      <CSSTransition
        in={isDropdownOpen}
        timeout={100}
        classNames="dropdown"
        unmountOnExit
      >
        <div
          ref={dropdownRef}
          className="dropdown fixed top-1/2 left-1/2 transform w-2xl min-w-[30%] max-w-[30%] -translate-x-1/2 -translate-y-1/2 z-30 bg-white shadow-lg rounded-lg border max-h-80 main-content-area overflow-auto border-gray-200"
        >
          <div className="sticky top-0 bg-white ">
            <div className="flex justify-between items-center px-3 py-2 border-b bg-white">
              <h1 className="font-weight-bold typography-body-lg p-1">
                Chat history for this project
              </h1>
              <button
                onClick={toggleDropdown}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            {chatHistory.length > 0 && (
              <div className="px-3 py-2 border-b bg-white">
                <input
                  type="text"
                  placeholder="Search chat history..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            )}
          </div>
          <ul className="py-2 overflow-y-auto">
            {loading ? (
              <li className="px-4 py-2 text-center list-none h-32 mt-9 flex justify-center items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </li>
            ) : chatHistory.length === 0 ? (
              <li className="px-4 py-2 text-center list-none ">
                <EmptyStateView type="chat" onClick={() => {}} />
              </li>
            ) : filteredChatHistory.length === 0 ? (
              <li className="px-4 py-2 text-center -mt-20">
                {" "}
                <EmptyStateView type="noSearchResult" onClick={() => setSearchQuery("")} />
              </li>
            ) : (
              filteredChatHistory.map((item) => (
                <li
                  key={item.discussion_id}
                  className={`px-4 py-2 hover:bg-gray-100 cursor-pointer  border-b list-none mx-3  ${
                    item.discussion_id === currentDiscussionId
                      ? "bg-primary-900"
                      : ""
                  }`}
                  onClick={() =>
                    handleDropdownItemClick(item.discussion_id, item.name)
                  }
                >
                  <div className="font-weight-bold typography-body-sm">{item.name}</div>
                  <div className="typography-caption text-gray-500">
                    {formatUTCToLocal(item.created_at)}
                  </div>
                </li>
              ))
            )}
          </ul>
        </div>
      </CSSTransition>
      {showModal && (
        <ConfirmationModal
          showModal={showModal}
          functionCallData={functionCallData}
          onCancel={() => setShowModal(false)}
          onConfirm={(data: any) => {
            addMessage({ text: data, sender: "AI" });
          }}
        />
      )}
      <div
        ref={messageContainerRef}
        className="flex-1 overflow-y-auto typography-body-sm custom-scrollbar leading-6 text-slate-900 mt-1 sm:typography-body sm:leading-7"
      >
        {Object.values(messages).map((message: any, index) => (
          <div key={message.id} className="mb-2">
            {message.sender === "user" ? (
              <div className="flex flex-row px-2 py-2 sm:px-2">
                <Image
                  className="mr-2 flex h-8 w-8 rounded-full"
                  src={`https://ui-avatars.com/api/?name=${username}&background=00000&color=FFFFFF`}
                  alt="User Avatar"
                  width={32}
                  height={32}
                />
                <div className="flex max-w-3xl items-center">
                  <p className="typography-body-sm">{message.text}</p>
                </div>
                {message.file_attachments &&
                  message.file_attachments.length > 0 && (
                    <div className="mt-2 space-y-2">
                      {message.file_attachments.map(
                        (attachment: any, index: any) => (
                          <FileAttachment
                            key={index}
                            attachment={attachment}
                            onView={() => setSelectedFile(attachment)}
                          />
                        )
                      )}
                    </div>
                  )}
              </div>
            ) : (
              <div className="px-2 w-full">
                <div className="flex w-full px-3 py-3 bg-gray-100 sm:px-3 rounded-lg">
                  <CodeBlock
                    markdownString={message.text}
                    message_end={message.message_end}
                  />
                </div>
              </div>
            )}
          </div>
        ))}
        {(loading || isUploading) && (
          <div className="flex space-x-2 animate-pulse pl-5 pt-1 pb-5">
            <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        )}
      </div>

      <div className="border-t border-gray-200 p-2 bg-gray-100">
        <form
          onSubmit={handleFormSubmit}
          className="flex items-center relative"
        >
          <textarea
            id="chat-input"
            className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none overflow-auto"
            placeholder="Enter your prompt"
            value={currentMessage}
            onChange={handleInputChange}
            onInput={handleTextareaResize}
            onKeyDown={handleKeyPress}
            rows={1} // initial row count
          />
          {attachmentEnabled && (
            <div className="relative">
              <AttachmentButton
                ref={attachmentButtonRef}
                onClick={handleAttachmentClick}
                fileCount={attachedFiles.length}
              />
              {showAttachmentWindow && attachedFiles.length > 0 && (
                <div className="attachment-window">
                  <AttachmentWindow
                    files={attachedFiles}
                    onAdd={handleAddAttachment}
                    onClear={handleClearAttachments}
                    onClose={() => setShowAttachmentWindow(false)}
                    maxFiles={maxFiles}
                    fileStatuses={fileStatuses}
                    onFileClick={handleFileClick}
                  />
                </div>
              )}
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                multiple={maxFiles > 1}
                accept={acceptedFileTypes.join(",")}
                className="hidden"
              />
            </div>
          )}
          <button
            type="submit"
            disabled={loading || isUploading}
            className={`ml-2 p-2 ${
              loading || isUploading ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            <Image src={chaticon} alt="Chat icon" />
          </button>
        </form>
      </div>

      {selectedFile && (
        <>
          <FileContentModal
            file={selectedFile}
            onClose={() => setSelectedFile(null)}
          />
        </>
      )}


    </div>
  );
}
