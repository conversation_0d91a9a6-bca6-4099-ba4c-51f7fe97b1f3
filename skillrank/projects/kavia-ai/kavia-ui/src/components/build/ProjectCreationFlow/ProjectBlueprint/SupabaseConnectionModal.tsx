// src/components/build/ProjectCreationFlow/ProjectBlueprint/SupabaseConnectionModal.tsx

import React, { useState, useEffect, useContext } from 'react';
import { X, Check, Loader2, Plus, Search, Eye, EyeOff, Copy, <PERSON><PERSON><PERSON><PERSON>gle, RefreshCcw } from 'lucide-react';
import { connectToSupabase, listSupabaseOrg, listSupabaseProjects, createSupabaseProject,updateSupabaseDB } from '@/utils/api';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';

// Props for the modal component
interface SupabaseConnectionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConnectionComplete: (selectedProject: SupabaseProject | null) => void;
}

// Represents a Supabase project
interface SupabaseProject {
    id: string;
    name: string;
    region: string;
    dashboard_url: string;
}

// Defines the steps in the connection flow
type ConnectionStep =
    | 'intro'
    | 'redirecting'
    | 'database_selection'
    | 'confirm_selection'
    | 'create_project_form'
    | 'creating_project'
    | 'success';

// --- SVG Icons as React Components ---

// Main "Bolt" Icon for Supabase, used across multiple steps.
const SupabaseBoltIcon = () => (
    <svg width="48" height="48" viewBox="0 0 112 113" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="56" cy="56.5" r="56" fill="#EBFBEE" />
        <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear_supabase_bolt)" />
        <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear_supabase_bolt)" fillOpacity="0.2" />
        <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E" />
        <defs>
            <linearGradient id="paint0_linear_supabase_bolt" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
                <stop stopColor="#249361" />
                <stop offset="1" stopColor="#3ECF8E" />
            </linearGradient>
            <linearGradient id="paint1_linear_supabase_bolt" x1="36.1558" y1="30.578" x2="54.4844" y2="65.0806" gradientUnits="userSpaceOnUse">
                <stop stopColor="white" />
                <stop offset="1" stopColor="white" stopOpacity="0" />
            </linearGradient>
        </defs>
    </svg>
);

// Icon for the "No projects found" state
const NoProjectsIcon = () => (
    <div className="inline-block bg-slate-100 p-3 rounded-full mb-4">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 6C4 4.89543 4.89543 4 6 4H9.41421C9.93464 4 10.4346 4.21071 10.8284 4.58579L12.5858 6.34315C12.9796 6.7182 13.4796 6.92893 14 6.92893H18C19.1046 6.92893 20 7.82436 20 8.92893V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6Z" fill="#CBD5E1" />
        </svg>
    </div>
);

// Supabase regions - based on AWS regions where Supabase is available
const SUPABASE_REGIONS = [
    { value: 'us-east-1', label: 'US East (N. Virginia)', flag: '🇺🇸' },
    { value: 'us-west-1', label: 'US West (N. California)', flag: '🇺🇸' },
    { value: 'us-west-2', label: 'US West (Oregon)', flag: '🇺🇸' },
    { value: 'ca-central-1', label: 'Canada (Central)', flag: '🇨🇦' },
    { value: 'sa-east-1', label: 'South America (São Paulo)', flag: '🇧🇷' },
    { value: 'eu-west-1', label: 'Europe (Ireland)', flag: '🇮🇪' },
    { value: 'eu-west-2', label: 'Europe (London)', flag: '🇬🇧' },
    { value: 'eu-west-3', label: 'Europe (Paris)', flag: '🇫🇷' },
    { value: 'eu-central-1', label: 'Europe (Frankfurt)', flag: '🇩🇪' },
    { value: 'eu-north-1', label: 'Europe (Stockholm)', flag: '🇸🇪' },
    { value: 'ap-south-1', label: 'Asia Pacific (Mumbai)', flag: '🇮🇳' },
    { value: 'ap-northeast-1', label: 'Asia Pacific (Tokyo)', flag: '🇯🇵' },
    { value: 'ap-northeast-2', label: 'Asia Pacific (Seoul)', flag: '🇰🇷' },
    { value: 'ap-northeast-3', label: 'Asia Pacific (Osaka)', flag: '🇯🇵' },
    { value: 'ap-southeast-1', label: 'Asia Pacific (Singapore)', flag: '🇸🇬' },
    { value: 'ap-southeast-2', label: 'Oceania (Sydney)', flag: '🇦🇺' },
];

const SupabaseConnectionModal: React.FC<SupabaseConnectionModalProps> = ({
    isOpen,
    onClose,
    onConnectionComplete
}) => {
    const [currentStep, setCurrentStep] = useState<ConnectionStep>('intro');
    const [projects, setProjects] = useState<SupabaseProject[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedProject, setSelectedProject] = useState<SupabaseProject | null>(null);
    const [newProject, setNewProject] = useState({ name: 'my-awesome-app', password: '', region: '' });
    const [formErrors, setFormErrors] = useState<{ name?: string; password?: string; region?: string }>({});
    const [showPassword, setShowPassword] = useState(false);
    const [copiedPassword, setCopiedPassword] = useState(false);
    const [loadBtn, setLoadBtn] = useState(false)
    const [isConnected, setIsConnected] = useState(false);
    const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
    const { showAlert } = useContext(AlertContext)
    const [isBackDisabled, setIsBackDisabled] = useState(false)
    const [isVisible, setIsVisible] = useState(false);
    const [dbPassword, setDbPassword] = useState('');
    const [dbPasswordError, setDbPasswordError] = useState('');
    const [showDbPassword, setShowDbPassword] = useState(false);

    // Reset state when the modal is closed
    useEffect(() => {
        if (!isOpen) {
            setTimeout(() => {
                setCurrentStep('intro');
                setProjects([]);
                setSearchQuery('');
                setSelectedProject(null);
                setNewProject({ name: 'my-awesome-app', password: '', region: '' });
                setFormErrors({});
                setShowPassword(false);
                setCopiedPassword(false);
                setDbPassword(''); // Add this line
                setDbPasswordError(''); // Add this line
                setShowDbPassword(false); // Add this line
            }, 300);
        }
    }, [isOpen]);



    // --- Step Navigation Handlers ---

    const handleContinueToIntro = async () => {
        setLoadBtn(true);
        try {
            const projectId = sessionStorage.getItem('generated_project_id');
            const response = await connectToSupabase(projectId?.toString());

            if (response && response.auth_url) {
                setLoadBtn(false);

                const popup = window.open(
                    response.auth_url,
                    'supabase_oauth',
                    'width=500,height=600,scrollbars=yes,resizable=yes'
                );

                const checkPopupClosed = setInterval(async () => {


                    if (popup?.closed) {
                        clearInterval(checkPopupClosed);


                        // ✅ First fetch projects, then update the step
                        try {
                            setCurrentStep('redirecting');
                            setIsBackDisabled(true)
                            await fetchUserProjects();

                        } catch (fetchError) {
                            console.error('Failed to fetch projects:', fetchError);
                            // Optional: Show error state
                        }
                    }
                }, 1000);

                setTimeout(() => {
                    if (!popup?.closed) {
                        clearInterval(checkPopupClosed);

                    }
                }, 120000); // 2 minutes timeout
            }
        } catch (error) {
            console.error('Error connecting to Supabase:', error);
            setLoadBtn(false);
        }
    };


    const fetchUserProjects = async () => {
        try {
            // Step 1: Get organizations
            const projectId = sessionStorage.getItem('generated_project_id');
            const organizationsResponse = await listSupabaseOrg(projectId?.toString());

            if (!organizationsResponse?.data?.length) {

                setProjects([]);
                setCurrentStep('database_selection');
                return;
            }

            // Step 2: Get the first organization's ID
            const organizationId = organizationsResponse.data[0].id;

            // Step 3: Fetch Supabase projects for this organization
            const projectsResponse = await listSupabaseProjects(projectId?.toString(), organizationId);

            if (projectsResponse?.data) {
                // Transform API response to match SupabaseProject interface
                const transformedProjects: SupabaseProject[] = projectsResponse.data
                    .filter((project: any) => project.status === 'ACTIVE_HEALTHY') // Only show healthy projects
                    .map((project: any) => ({
                        id: project.id,
                        name: project.name,
                        region: project.region,
                        dashboard_url: project.dashboard_url.split('/project')[0]
                    }));


                setProjects(transformedProjects);
            } else {

                setProjects([]);
            }

            // Show project selection after 2 seconds (to maintain the loading UX)
            setTimeout(() => {
                setCurrentStep('database_selection');
            }, 2000);

        } catch (error) {

            // Fallback to empty projects list
            setProjects([]);
            setCurrentStep('database_selection');
        }
    };

    const handleCreateProjectFlow = () => {
        setCurrentStep('create_project_form');
    };

    const handleRefresh = async () => {
  try {
    setCurrentStep('redirecting');
    setIsBackDisabled(true);
    await fetchUserProjects();
  } catch (error) {
  } 
};

    // Copy password to clipboard
    const copyPassword = async () => {
        if (newProject.password) {
            try {
                await navigator.clipboard.writeText(newProject.password);
                setCopiedPassword(true);
                setTimeout(() => setCopiedPassword(false), 2000);
            } catch (err) {

            }
        }
    };

    const InfoBanner = () => {
        return (
            <div className="w-full flex items-center bg-yellow-50 p-4 border border-yellow-200 rounded-md shadow-sm">
                <div className="w-3 h-3 rounded-full bg-yellow-500 mr-3 animate-pulse"></div>
                <AlertTriangle className="text-yellow-600 mr-3 flex-shrink-0" size={20} />
                <span className="text-sm text-yellow-900 flex-1">
                    Project creation failed: You've reached the limit of 2 active free projects. Delete, pause, or upgrade an existing project to continue.
                </span>
            </div>
        );
    };



    // Generate a strong password
    const generatePassword = () => {
        const length = 16;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        let password = "";
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        setNewProject({ ...newProject, password });
        if (formErrors.password) setFormErrors({ ...formErrors, password: undefined });
        setShowPassword(true); // Show the generated password
    };

    // Validate form fields
    const validateForm = (): boolean => {
        const errors: { name?: string; password?: string; region?: string } = {};

        // Project name validation
        if (!newProject.name.trim()) {
            errors.name = 'Project name is required';
        } else if (newProject.name.length < 3) {
            errors.name = 'Project name must be at least 3 characters';
        } else if (!/^[a-zA-Z0-9-]+$/.test(newProject.name)) {
            errors.name = 'Project name can only contain letters, numbers, and hyphens';
        }

        // Password validation
        if (!newProject.password) {
            errors.password = 'Password is required';
        } else if (newProject.password.length < 8) {
            errors.password = 'Password must be at least 8 characters';
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/.test(newProject.password)) {
            errors.password = 'Password must contain uppercase, lowercase, and numbers';
        }

        // Region validation
        if (!newProject.region) {
            errors.region = 'Please select a region';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleCreateProject = async () => {
        if (!validateForm()) {
            return;
        }

        setCurrentStep('creating_project');

        try {
            // Step 1: Get organization ID
            const projectId = sessionStorage.getItem('generated_project_id');
            const organizationsResponse = await listSupabaseOrg(projectId?.toString());

            if (!organizationsResponse?.data?.length) {

                setCurrentStep('create_project_form');
                setFormErrors({ region: 'Unable to fetch organization. Please try again.' });
                return;
            }

            const organizationId = organizationsResponse.data[0].id;

            // Step 2: Create the Supabase project
            const createProjectData = {
                name: newProject.name,
                organization_id: organizationId,
                region: newProject.region,
                db_password: newProject.password
            };


            const createResponse = await createSupabaseProject(projectId, createProjectData);

            // Check if we have a successful response with project data
            if (createResponse && createResponse.data) {
                const projectData = createResponse.data;
                const createdProjectId = projectData.project_id; // Fixed: use project_id from response

                // Step 3: Refresh the projects list to get updated data
                try {
                    const refreshedProjectsResponse = await listSupabaseProjects(projectId, organizationId);

                    if (refreshedProjectsResponse?.data) {
                        // Transform the refreshed projects list
                        const updatedProjects: SupabaseProject[] = refreshedProjectsResponse.data
                            .filter((project: any) => project.status === 'ACTIVE_HEALTHY')
                            .map((project: any) => ({
                                id: project.id,
                                name: project.name,
                                region: project.region,
                                dashboard_url: project.dashboard_url ? project.dashboard_url.split('/project')[0] : ''
                            }));

                        // Update the projects list
                        setProjects(updatedProjects);

                        // Find and select the newly created project
                        const newlyCreatedProject = updatedProjects.find(p => p.id === createdProjectId);
                        if (newlyCreatedProject) {
                            setSelectedProject(newlyCreatedProject);
                        } else {
                            // Fallback: create project object from response if not found in list
                            const fallbackProject: SupabaseProject = {
                                id: projectData.project_id,
                                name: projectData.name,
                                region: projectData.region,
                                dashboard_url: (projectData.dashboard_url || createResponse.dashboard_url || '').split('/project')[0]
                            };
                            setSelectedProject(fallbackProject);
                        }
                    } else {
                        // Fallback: use the created project data if refresh fails
                        const createdProject: SupabaseProject = {
                            id: projectData.project_id,
                            name: projectData.name,
                            region: projectData.region,
                            dashboard_url: (projectData.dashboard_url || createResponse.dashboard_url || '').split('/project')[0]
                        };
                        setProjects(prev => [...prev, createdProject]);
                        setSelectedProject(createdProject);
                    }
                } catch {
                    // Fallback: add the created project to existing list
                    const createdProject: SupabaseProject = {
                        id: projectData.project_id,
                        name: projectData.name,
                        region: projectData.region,
                        dashboard_url: (projectData.dashboard_url || createResponse.dashboard_url || '').split('/project')[0]
                    };
                    setProjects(prev => [...prev, createdProject]);
                    setSelectedProject(createdProject);
                }

                // Step 4: Navigate to database selection and show success
                setCurrentStep('database_selection');
                showAlert("Project created successfully", "success");

            } else {
                setIsVisible(true)
                showAlert("Failed to create project", "error");
                throw new Error('Failed to create project - invalid response structure');
            }

        } catch (error) {
            setIsVisible(true)

            showAlert("Failed to create project", "error");

            // Handle different types of errors
            if (error instanceof Error) {
                if (error.message.includes('name')) {
                    setFormErrors({ name: 'Project name is invalid or already exists' });
                } else if (error.message.includes('password')) {
                    setFormErrors({ password: 'Password does not meet requirements' });
                } else if (error.message.includes('region')) {
                    setFormErrors({ region: 'Selected region is not available' });
                } else {
                    setFormErrors({ region: 'Failed to create project. Please try again.' });
                }
            } else {
                setFormErrors({ region: 'An unexpected error occurred. Please try again.' });
            }

            setCurrentStep('create_project_form');
        }
    };

    const handleFinishConnection = () => {
        if (selectedProject) {
            setCurrentStep('confirm_selection');
        }
    };

    const handleConfirmConnection = async () => {
        if (!selectedProject?.id) return;

        // Validate database password
        if (!dbPassword.trim()) {
            setDbPasswordError('Database password is required');
            return;
        }

        if (dbPassword.length < 8) {
            setDbPasswordError('Password must be at least 8 characters');
            return;
        }

        try {
            // Store the selected project ID in session storage
            sessionStorage.setItem('selected_supabase_project_id', selectedProject.id);

            // Get the main project ID from session storage
            const projectId = sessionStorage.getItem('generated_project_id');

            if (!projectId) {
                showAlert("Project ID not found", "error");
                return;
            }

            // Show loading state
            setCurrentStep('creating_project');

            // Call the update database API with the database password
            const updateResponse = await updateSupabaseDB(projectId, selectedProject.id, dbPassword);

            if (updateResponse?.success) {
                showAlert("Database connection updated successfully", "success");
                setCurrentStep('success');

                setTimeout(() => {
                    onConnectionComplete(selectedProject);
                    onClose();
                }, 1500);
            } else {
                throw new Error(updateResponse?.message || 'Failed to update database connection');
            }

        } catch (error) {
            console.error('Error updating database connection:', error);
            showAlert("Failed to connect project to database", "error");

            // Go back to confirm selection step
            setCurrentStep('confirm_selection');
        }
    };
    const handleBack = () => {
        if (currentStep === 'database_selection') {
            setCurrentStep('intro');
        }
        if (currentStep === 'create_project_form') {
            setCurrentStep('database_selection');
        }
        if (currentStep === 'confirm_selection') {
            setCurrentStep('database_selection');
        }
    };

    // Filter projects based on the search query
    const filteredProjects = projects.filter(p =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // --- Dynamic Header and Content Rendering ---

    const getTitle = () => {
        switch (currentStep) {
            case 'intro':
            case 'redirecting':
                return 'Connect to Supabase';
            case 'database_selection':
                return 'Select Your Supabase Database';
            case 'confirm_selection':
                return `Connect ${selectedProject?.name} to Kavia`;
            case 'create_project_form':
            case 'creating_project':
                return 'Create Supabase Project';
            case 'success':
                return 'Connection Successful';
            default:
                return 'Connect to Supabase';
        }
    };

    const getBreadcrumb = () => {
        let steps = ['Configure App'];
        if (currentStep === 'database_selection' || currentStep === 'create_project_form' || currentStep === 'creating_project' || currentStep === 'confirm_selection') {
            steps.push('Select Database');
        } else {
            steps.push('Connect Supabase');
        }

        if (currentStep === 'create_project_form' || currentStep === 'creating_project') {
            steps.push('Create Project');
        }

        if (currentStep === 'confirm_selection') {
            steps.push('Confirm Connection');
        }

        return (
            <p className="text-xs text-slate-500 mt-1">
                {steps.map((step, index) => (
                    <React.Fragment key={index}>
                        <span className={index === steps.length - 1 ? "text-slate-700 font-medium" : "text-primary hover:underline cursor-pointer"}>
                            {step}
                        </span>
                        {index < steps.length - 1 && <span className="mx-1">›</span>}
                    </React.Fragment>
                ))}
            </p>
        );
    }



    const renderContent = () => {
        switch (currentStep) {
            case 'intro':
                return (
                    <div className="flex flex-col items-center text-center px-4 sm:px-8">
                        <SupabaseBoltIcon />
                        <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Connect to Supabase</h2>
                        <p className="text-sm text-slate-500 max-w-md mb-8">
                            Kavia needs access to your Supabase organization to set up your database and authentication.
                        </p>
                        {/* <div className="bg-slate-50 border border-slate-200 rounded-lg w-full text-left p-6">
                            <p className="font-medium text-slate-700 mb-4">This will allow us to:</p>
                            <ul className="space-y-3">
                                {['Create and manage database tables', 'Set up user authentication', 'Configure real-time subscriptions', 'Manage API keys and secrets'].map(text => (
                                    <li key={text} className="flex items-center text-sm text-slate-600">
                                        <Check size={16} className="text-green-500 mr-3 flex-shrink-0" />
                                        {text}
                                    </li>
                                ))}
                            </ul>
                        </div> */}
                    </div>
                );
            case 'redirecting':
                return (
                    <div className="flex flex-col items-center justify-center h-full py-28 text-center">
                        <Loader2 className="w-8 h-8 text-slate-400 animate-spin mb-4" />
                        <p className="text-slate-500">Redirecting to Supabase...</p>
                    </div>
                );
            case 'database_selection':
                return (
                    <div className="flex flex-col items-center text-center px-4 sm:px-8">
                        <SupabaseBoltIcon />
                        <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Select your database</h2>
                        <p className="text-sm text-slate-500 max-w-lg mb-8">
                            Connect with a Supabase project to manage your data, set up authentication, create backend functions, and more.
                        </p>
                        <div className="w-full text-left mb-2">
                            <label className="text-sm font-medium text-slate-700">Select a project</label>
                        </div>
                        <div className="w-full">
                            <div className='flex items-center justify-between gap-2'>
                                <div className="relative mb-4 w-full ">
                                    <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                                    <input
                                        type="text"
                                        placeholder="Search for a project"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="w-full p-2 pl-10 border border-slate-300 rounded-md focus:ring-2 focus:ring-green-500/50 focus:border-green-500 outline-none"
                                    />
                                </div>
                                <button
                                    onClick={handleRefresh}
                                    className="flex items-center justify-center -mt-4 px-3 py-2 px-3 py-2 border border-slate-300 rounded-md text-sm text-slate-600 hover:bg-slate-100 transition-colors duration-150"
                                    title="Refresh projects"
                                >
                                    <RefreshCcw className="mr-1.5 w-3.5 h-3.5"/>
                                    Refresh
                                </button>
                            </div>
                            {filteredProjects.length === 0 ? (
                                <div className="w-full text-center border-2 border-dashed border-slate-200 rounded-lg py-10 px-6">
                                    <NoProjectsIcon />
                                    <p className="font-semibold text-slate-700">No projects found</p>
                                    <p className="text-sm text-slate-500 mt-1">{searchQuery ? 'Try a different search term.' : 'You can create one below.'}</p>
                                </div>
                            ) : (
                                <div className="w-full border border-slate-200 rounded-lg text-left max-h-48 overflow-y-auto">
                                    {filteredProjects.map(p => (
                                        <label key={p.id} className={`flex items-center p-4 border-b border-slate-200 last:border-b-0 cursor-pointer hover:bg-slate-50 ${selectedProject?.id === p.id ? 'bg-green-50 border-green-300 ring-1 ring-green-300' : ''}`}>
                                            <input type="radio" name="project" checked={selectedProject?.id === p.id} onChange={() => setSelectedProject(p)} className="h-4 w-4 border-slate-300 text-green-600 focus:ring-green-500" />
                                            <span className="ml-3 font-medium text-slate-700">{p.name}</span>
                                        </label>
                                    ))}
                                </div>
                            )}
                            <div className="flex items-center my-6 w-full">
                                <div className="flex-grow border-t border-slate-200"></div>
                                <span className="flex-shrink mx-4 text-slate-400 text-xs font-medium">OR</span>
                                <div className="flex-grow border-t border-slate-200"></div>
                            </div>
                            <button onClick={handleCreateProjectFlow} className="w-full max-w-xs px-4 py-2 mx-auto bg-slate-800 text-white rounded-md hover:bg-slate-900 text-sm font-medium flex items-center justify-center">
                                <Plus size={16} className="mr-2" />
                                Create a new project
                            </button>
                        </div>
                    </div>
                );
            case 'confirm_selection':
                return (
                    <div className="flex flex-col items-center text-center px-4 sm:px-8">
                        <SupabaseBoltIcon />
                        <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Connect {selectedProject?.name} to Kavia</h2>
                        <p className="text-sm text-slate-500 max-w-md mb-8">
                            You&apos;re about to connect this project to Kavia. You can check project details before proceeding.
                        </p>

                        <div className="w-full max-w-md">
                            <div className="mb-6">
                                <label className="text-sm font-medium text-slate-700 mb-2 block">Database Password</label>
                                <div className="relative">
                                    <input
                                        type={showDbPassword ? "text" : "password"}
                                        placeholder="Enter your database password"
                                        value={dbPassword}
                                        onChange={(e) => {
                                            setDbPassword(e.target.value);
                                            if (dbPasswordError) setDbPasswordError('');
                                        }}
                                        className={`w-full p-2.5 pr-12 border rounded-md focus:ring-2 focus:ring-green-500/50 focus:border-green-500 outline-none transition-colors ${dbPasswordError ? 'border-red-500 focus:ring-red-500/50 focus:border-red-500' : 'border-slate-300'
                                            }`}
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowDbPassword(!showDbPassword)}
                                        className="absolute right-2 top-1/2 -translate-y-1/2 p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded transition-colors"
                                        title={showDbPassword ? "Hide password" : "Show password"}
                                    >
                                        {showDbPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                                    </button>
                                </div>
                                {dbPasswordError && (
                                    <p className="text-xs text-red-500 mt-1">{dbPasswordError}</p>
                                )}
                                <p className="text-xs text-slate-500 mt-1">
                                    This is the password you used when creating your Supabase project.
                                </p>
                            </div>
                            <div className="text-left mb-4">
                                <p className="text-sm font-medium text-slate-700 mb-2">Selected project</p>
                                <span className="text-sm text-slate-500">{selectedProject?.name}</span>
                            </div>

                            <div className="bg-slate-50 border border-slate-200 rounded-lg p-4 text-left">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h3 className="font-medium text-slate-800">{selectedProject?.name}</h3>
                                        <p className="text-sm text-slate-500 mt-1">{selectedProject?.region}</p>
                                    </div>
                                    <a
                                        href={selectedProject?.dashboard_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-sm font-medium text-green-600 hover:text-green-700 flex items-center"
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                                            <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6M15 3h6v6M10 14L21 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        </svg>
                                        View in Supabase
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div >
                );
            case 'create_project_form':
                return (
                    <div className="flex flex-col items-center px-4 sm:px-8">
                        <SupabaseBoltIcon />
                        <h2 className="text-xl font-semibold text-slate-800 mt-4 mb-2">Create your database</h2>
                        <p className="text-sm text-slate-500 mb-8">Configure your database information.</p>
                        <div className="w-full max-w-md text-left space-y-4">
                            {isVisible && (
                                <InfoBanner />)}
                            <div>
                                <label className="text-sm font-medium text-slate-700 mb-1 block">Project name</label>
                                <input
                                    type="text"
                                    value={newProject.name}
                                    onChange={e => {
                                        setNewProject({ ...newProject, name: e.target.value });
                                        if (formErrors.name) setFormErrors({ ...formErrors, name: undefined });
                                    }}
                                    placeholder="My project name"
                                    className={`w-full p-2.5 border rounded-md focus:ring-2 focus:ring-green-500/50 focus:border-green-500 outline-none transition-colors ${formErrors.name ? 'border-red-500 focus:ring-red-500/50 focus:border-red-500' : 'border-slate-300'
                                        }`}
                                />
                                {formErrors.name && (
                                    <p className="text-xs text-red-500 mt-1">{formErrors.name}</p>
                                )}
                            </div>
                            <div>
                                <label className="text-sm font-medium text-slate-700 mb-1 block">Database password</label>
                                <div className="relative">
                                    <input
                                        type={showPassword ? "text" : "password"}
                                        placeholder="Password"
                                        value={newProject.password}
                                        onChange={e => {
                                            setNewProject({ ...newProject, password: e.target.value });
                                            if (formErrors.password) setFormErrors({ ...formErrors, password: undefined });
                                        }}
                                        className={`w-full p-2.5 pr-24 border rounded-md focus:ring-2 focus:ring-green-500/50 focus:border-green-500 outline-none transition-colors ${formErrors.password ? 'border-red-500 focus:ring-red-500/50 focus:border-red-500' : 'border-slate-300'
                                            }`}
                                    />
                                    <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                                        {newProject.password && (
                                            <button
                                                type="button"
                                                onClick={copyPassword}
                                                className="p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded transition-colors"
                                                title="Copy password"
                                            >
                                                {copiedPassword ? (
                                                    <Check size={16} className="text-green-500" />
                                                ) : (
                                                    <Copy size={16} />
                                                )}
                                            </button>
                                        )}
                                        <button
                                            type="button"
                                            onClick={() => setShowPassword(!showPassword)}
                                            className="p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded transition-colors"
                                            title={showPassword ? "Hide password" : "Show password"}
                                        >
                                            {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                                        </button>
                                    </div>
                                </div>
                                {formErrors.password ? (
                                    <p className="text-xs text-red-500 mt-1">{formErrors.password}</p>
                                ) : (
                                    <p className="text-xs text-slate-500 mt-1">
                                        This is the password of your Postgres database, so it must be strong and hard to guess.
                                        <span
                                            onClick={generatePassword}
                                            className="text-green-600 underline cursor-pointer ml-1 hover:text-green-700"
                                        >
                                            Generate a password
                                        </span>
                                    </p>
                                )}
                            </div>
                            <div>
                                <label className="text-sm font-medium text-slate-700 mb-1 block">Region</label>
                                <select
                                    value={newProject.region}
                                    onChange={e => {
                                        setNewProject({ ...newProject, region: e.target.value });
                                        if (formErrors.region) setFormErrors({ ...formErrors, region: undefined });
                                    }}
                                    className={`w-full p-2.5 border rounded-md focus:ring-2 focus:ring-green-500/50 focus:border-green-500 outline-none appearance-none bg-white bg-no-repeat bg-right pr-10 transition-colors ${formErrors.region ? 'border-red-500 focus:ring-red-500/50 focus:border-red-500' : 'border-slate-300'
                                        }`}
                                    style={{
                                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                                        backgroundPosition: 'right 0.75rem center',
                                        backgroundSize: '1.25em 1.25em'
                                    }}
                                >
                                    <option value="">Select a region</option>
                                    {SUPABASE_REGIONS.map(region => (
                                        <option key={region.value} value={region.value}>
                                            {region.flag} {region.label}
                                        </option>
                                    ))}
                                </select>
                                {formErrors.region ? (
                                    <p className="text-xs text-red-500 mt-1">{formErrors.region}</p>
                                ) : (
                                    <p className="text-xs text-slate-500 mt-1">Select the region closest to your users for the best performance.</p>
                                )}
                            </div>
                        </div>
                    </div>
                );
            case 'creating_project':
                return (
                    <div className="flex flex-col items-center justify-center h-full py-28 text-center">
                        <Loader2 className="w-8 h-8 text-slate-400 animate-spin mb-4" />
                        <p className="text-slate-500">Creating Supabase project...</p>
                    </div>
                );
            case 'success':
                return (
                    <div className="flex flex-col items-center justify-center h-full py-28 text-center">
                        <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-4 ring-4 ring-green-100">
                            <Check className="w-6 h-6 text-white" />
                        </div>
                        <h2 className="text-xl font-semibold text-slate-800">Successfully Connected!</h2>
                        <p className="text-slate-500 mt-1">Your Supabase project is now active.</p>
                    </div>
                );
            default: return null;
        }
    };

    const renderFooter = () => {
        switch (currentStep) {
            case 'intro':
                return (
                    <div className="w-full flex justify-end items-center">
                        <button onClick={onClose} className="px-4 py-2 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 text-sm font-medium">Back</button>
                        <button
                            onClick={handleContinueToIntro}
                            className={`ml-3 px-4 py-2 rounded-md text-sm font-medium flex items-center justify-center transition-all duration-200 ${isConnected
                                ? 'bg-green-600 text-white cursor-default'
                                : connectionStatus === 'connecting'
                                    ? 'bg-primary text-white cursor-wait'
                                    : 'bg-green-500 text-white hover:bg-green-600'
                                }`}
                            disabled={loadBtn || isConnected || connectionStatus === 'connecting'}
                        >
                            {connectionStatus === 'connecting' || loadBtn ? (
                                <>
                                    <svg className="animate-spin h-5 w-5 text-white mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                                    </svg>
                                    Connecting...
                                </>
                            ) : isConnected ? (
                                <>
                                    <Check size={16} className="mr-2" />
                                    Connected
                                </>
                            ) : (
                                'Continue to Supabase'
                            )}
                        </button>

                    </div>
                );
            case 'database_selection':
                return (
                    <div className="w-full flex justify-between items-center">
                        <button
                            onClick={handleBack}
                            disabled={isBackDisabled}
                            className={`px-4 py-2 rounded-md text-sm font-medium ${isBackDisabled
                                ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                                : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                                }`}
                        >
                            Back
                        </button>
                        <button onClick={handleFinishConnection} disabled={!selectedProject} className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium disabled:bg-slate-200 disabled:text-slate-500 disabled:cursor-not-allowed">
                            Continue
                        </button>
                    </div>
                );
            case 'confirm_selection':
                return (
                    <div className="w-full flex justify-between items-center">
                        <button onClick={handleBack} className="px-4 py-2 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 text-sm font-medium">Back</button>
                        <button onClick={handleConfirmConnection} className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium">
                            Connect project
                        </button>
                    </div>
                );
            case 'create_project_form':
                return (
                    <div className="w-full flex justify-end items-center">
                        <button type="button" onClick={handleBack} className="px-4 py-2 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 text-sm font-medium">Back</button>
                        <button
                            onClick={handleCreateProject}
                            className="ml-3 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm font-medium flex items-center"
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                                <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            Create a new project
                        </button>
                    </div>
                );
            default: return null;
        }
    }

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 transition-opacity duration-300">
            <div className="bg-white rounded-xl w-full max-w-2xl shadow-xl flex flex-col max-h-[95vh]">
                <div className="px-6 py-4 border-b border-slate-200 flex justify-between items-center flex-shrink-0">
                    <div>
                        <h1 className="text-base font-semibold text-slate-800">{getTitle()}</h1>
                        {getBreadcrumb()}
                    </div>
                    <button onClick={onClose} className="p-1.5 rounded-full hover:bg-slate-100">
                        <X size={20} className="text-slate-500" />
                    </button>
                </div>

                <div className="flex-1 p-6 overflow-y-auto min-h-[300px]">
                    {renderContent()}
                </div>

                {currentStep !== 'redirecting' && currentStep !== 'creating_project' && currentStep !== 'success' && (
                    <div className={`px-6 py-4 bg-white border-t border-slate-200 flex-shrink-0 flex justify-end items-center space-x-3 rounded-b-xl ${currentStep === 'database_selection' || currentStep === 'confirm_selection' ? '!justify-between' : ''}`}>
                        {renderFooter()}
                    </div>
                )}
            </div>
        </div>
    );
};

export default SupabaseConnectionModal;