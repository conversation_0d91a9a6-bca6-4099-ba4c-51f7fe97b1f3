"use client";

import React, { useState, useEffect, useRef, useContext } from "react";
import Image from "next/image";
import { FaEllipsisH } from "react-icons/fa";
import image from "../../../public/images/projects.svg";
import kavia from "../../../public/images/kavia_logo.svg";
import Accordion from "./Accordion";
import { BookOpen } from 'lucide-react';
import {
  getTopLevelRequirements,
  fetchChildRequirements,
  listAllUsers,
  deleteNodeById,
  getAvailableStatus,
  updateNodeByPriority,
  deleteMultipleNodes,
  getReconfigNodeSectionFlag,
  getReconfigNodeStatus,
  exportRequirementsToExcel,
  importRequirementsFromExcel
} from "@/utils/api";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { Loading2 } from "@/components/Loaders/Loading";
import Search from "./TableComponents/Search";
import {
  ChevronDown,
  ChevronUp,
  CircleChevronDown,
  CircleChevronRight,
  CirclePlusIcon,
  Layers2,
  MoreVertical,
  Square,
  Trash,
  Plus,
  Settings,
  RefreshCw,
  X,
} from "lucide-react";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";
import CustomDropdown from '@/components/UIComponents/Dropdowns/CustomDropdown';

import ListUserAssignModal from "../Modal/ListUserAssignModal";
import CreateTaskModal from "../Modal/CreateTaskModel";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import ConfigureModal from "../Modal/ConfigureModel";
import DeleteProjectModal from "../Modal/DeleteProjectModal";
import { StateContext } from "../Context/StateContext";
import WorkItemTable from "../SimpleTable/WorkItemTable";

import EmptyStateView from "@/components/Modal/EmptyStateModal";
import en from "@/en.json";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { TableLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { ExecutionContext } from "../Context/ExecutionContext";
import ReConfigureModal from "../Modal/ReconfigureModel";
import RequirementsBanner from "../UIComponents/Badge/RequirementBanner";
import { DynamicReconfigButton } from "@/components/UIComponents/Buttons/DynamicReconfigButton";

const WorkItemTabContent = ({ setIsPastDiscussionOpen, setIsShowTab }) => {
  const [data, setData] = useState([]);
  const [expandedRows, setExpandedRows] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [childLoading, setChildLoading] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [nodeDetails, setNodeDetails] = useState({
    title: "",
    description: "",
    type: "",
    assignee_email: "",
    assignee_name: "",
    assignee_id: "",
    assigned_at: "",
  });
  const [nodeLoading, setNodeLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [requirementRootId, setRequirementRootId] = useState(null);
  const [configureNodeId, setConfigureNodeId] = useState(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true); // Added to track initial load
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const requirementType = searchParams.get("req_type");
  const requirement_Id = searchParams.get("req_id");
  const [checkAll, setCheckAll] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [_listAllUsers, _setListAllUsers] = useState(false);
  const [showUsersToAssign, setShowUsersToAssign] = useState(false);
  const projectId = pathname.split("/")[3];
  const id = pathname.split("/")[3];
  const [downChevronEnabled, setDownChevronEnabled] = useState(-1);
  const [isKebabDropdownOpen, setIsKebabDropdownOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const [configureModel, setConfigureModel] = useState(false);
  const [isNodeType, setNodeType] = useState(null);
  const openModal = () => setIsModalOpen(true);
  const closeModalFunc = () => setIsModalOpen(false);
  const [refresh, setRefresh] = useState(false);
  const [status, setStatus] = useState([]);
  const [statusMap, setStatusMap] = useState({});
  const [isDropdown, setIsDropdown] = useState(false);
  const [priorityMap, setPriorityMap] = useState({});
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isOpenDropdownOn, setIsOpenDropdownOn] = useState(null);
  const { setIsVertCollapse } = useContext(StateContext);
  const [isDeleteMultipleModal, setIsDeleteMultipleModal] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [error, setError] = useState(null);

  const [dropdownStyle, setDropdownStyle] = useState("");
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const {enableReconfig,requirementReconfigEnable,setRequirementReconfigEnable} = useContext(ExecutionContext)
   const [reconfigureModel,setReconfigureModel] = useState(false)
  const [loadingReConfigure,setLoadingReconfigure] = useState(false)
  const [reconfigCount,setReconfigCount] = useState(0)
  const [exporting, setExporting] = useState(false);

  const [openDropdownIndex, setOpenDropdownIndex] = useState(null);
  const [deleteParams, setDeleteParams] = useState({
    nodeId: null,
    nodeType: null,
  });
  const fileInputRef = useRef(null);
  const dropdownRefs = useRef([]);

  const getPositionStyle = (index, level) => {
    const dropdown = dropdownRefs.current[parseInt(`${index}${level}`)];
    if (!dropdown) {
      return "";
    }
    const rect = dropdown.getBoundingClientRect();
    const isBottomVisible = window.innerHeight - rect.bottom < 315;
    return isBottomVisible
      ? `bottom-5 ${window.innerHeight - rect.bottom}`
      : "top-3";
  };

  const handleBtn = () => {
    showAlert("Functionality was not implemented", "info");
  };

  const priorityBadgeColors = {
    Low: "bg-green-200 text-green-800",
    Medium: "bg-yellow-200 text-yellow-800",
    High: "bg-orange-200 text-orange-800",
    Critical: "bg-red-200 text-red-800",
  };

  const statusClasses = {
    "To Do": "bg-semantic-gray-100 text-semantic-gray-700",
    "In Progress": "bg-semantic-yellow-100 text-semantic-yellow-700",
    Review: "bg-primary-100 text-primary-700",
    Done: "bg-semantic-green-100 text-semantic-green-700",
    Blocked: "bg-semantic-red-100 text-semantic-red-700",
    default: "bg-semantic-gray-100 text-semantic-gray-700",
  };

  const typeBadgeColors = {
    Epic: "bg-semantic-purple-100 text-semantic-purple-700 max-w-14",
    UserStory: "bg-semantic-green-100 text-semantic-green-700 max-w-24",
    Task: "bg-primary-100 text-primary-700 max-w-14",
    default: "bg-custom-bg-primary max-w-20",
  };

  useEffect(() => {
    setIsShowTab(!selectedItem);
  }, [selectedItem]);


useEffect (()=>{
   const loadReconfigStatus = async () => {
        if (projectId) {
          try {
            const reconfigData = await getReconfigNodeSectionFlag(projectId);
            const reconfig = await getReconfigNodeStatus(projectId)
            const Count =
            (reconfig.Epic?.filter(item => item.reconfig_needed === true)?.length || 0)
            setReconfigCount(Count)

              if(reconfigData?.requirement_reconfig === true){
                setRequirementReconfigEnable(true)
              }


          } catch (error) {
            }
        }
      }

      loadReconfigStatus();
  const intervalId = setInterval(loadReconfigStatus, 300000);


  return () => clearInterval(intervalId)
},[projectId])

  const fetchRequirements = async () => {
    setLoading(true);
    try {
      const projectId = id;
      const topLevelRequirements = await getTopLevelRequirements(projectId);
      setData(topLevelRequirements);
      setRequirementRootId(topLevelRequirements[0].requirement_root_id);
      setIsInitialLoad(false);
    } catch (error) {

      setError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchRequirements = async () => {
      setLoading(true);
      try {
        const projectId = id;
        const topLevelRequirements = await getTopLevelRequirements(projectId);
        setData(topLevelRequirements);
        setRequirementRootId(topLevelRequirements[0].requirement_root_id);
        setIsInitialLoad(false); // Update initial load status
      } catch (error) {

      } finally {
        setLoading(false);
      }
    };

    const fetchallUsers = async () => {
      const list = await listAllUsers();
      _setListAllUsers(list);
    };

    fetchallUsers();
    fetchRequirements();
  }, [id, refresh, requirementType, requirement_Id]);

  useEffect(() => {
    const reqType = searchParams.get("req_type");
    const reqId = searchParams.get("req_id");
    if (reqType && reqId) {
      handleItemClick(reqType, reqId, false);
    }
  }, [refresh]);

  const dropdownRef = useRef(null);

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsDropdownOpen(null);
    }
  };

  useEffect(() => {
    if (isDropdownOpen !== null) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);



  const handleExpandRow = async (itemId, level) => {
    const isExpanded = expandedRows.some(
      (row) => row.id === itemId && row.level === level
    );
    if (isExpanded) {
      setExpandedRows(
        expandedRows.filter(
          (row) => !(row.id === itemId && row.level === level)
        )
      );
    } else {
      setExpandedRows([...expandedRows, { id: itemId, level }]);
      setChildLoading(itemId);
      try {
        const childData = await fetchChildRequirements(itemId, "Requirement");
        const addChildData = (items) => {
          return items.map((item) => {
            if (item.id === itemId) {
              return {
                ...item,
                subItems: childData.length
                  ? childData
                  : [
                    {
                      id: null,
                      title: "No child work items available",
                      status: "",
                      assignee_name: "",
                      subItems: [],
                    },
                  ],
              };
            }
            if (item.subItems) {
              return { ...item, subItems: addChildData(item.subItems) };
            }
            return item;
          });
        };
        setData(addChildData(data));
      } catch (error) {

      } finally {
        setChildLoading(null);
      }
    }
  };

  useEffect(() => {
    const fetchStatus = async () => {
      const response = await getAvailableStatus();
      setStatus(response);
    };

    fetchStatus();
  }, []);

  const updateStatus = async (status, nodeId, e) => {
    e.stopPropagation();
    const propertyName = "Status";
    try {
      const response = await updateNodeByPriority(nodeId, propertyName, status);
      setStatusMap((prevStatusMap) => ({
        ...prevStatusMap,
        [nodeId]: status,
      }));
      setOpenDropdownIndex(null);
      showAlert("Status Updated successfully", "success");
    } catch (error) {
      showAlert("Updating Status Failed", "danger");
    }
  };

  const handleSelectAll = () => {
    setCheckAll(!checkAll);
    if (!checkAll) {
      // Select all items
      const allItemIds = filteredData.map((item) => item.id);
      setSelectedItems(allItemIds);
    } else {
      // Deselect all items
      setSelectedItems([]);
    }
  };

  const handleCheckboxChange = (itemId) => {
    if (selectedItems.includes(itemId)) {
      // Deselect the item
      setSelectedItems(selectedItems.filter((id) => id !== itemId));
    } else {
      // Select the item
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const deleteConformation = () => {
    setIsDeleteMultipleModal(true);
  };

  const cancelDeleteConformation = () => {
    setIsDeleteMultipleModal(false);
  };

  const handleDeleteMultipleNodes = async () => {
    setIsDeleting(true);
    try {
      let nodeType = "Epic";
      await deleteMultipleNodes(selectedItems, nodeType);
      await getTopLevelRequirements(projectId).then((topLevelRequirements) => {
        setData(topLevelRequirements);
        setRequirementRootId(topLevelRequirements[0].requirement_root_id);
      });
      setSelectedItems([]);
      showAlert("All the Nodes are Deleted Successfully");
      setIsDeleteMultipleModal(false);
      setIsDeleting(false);
    } catch (error) {
      showAlert("Falied to Delete All Nodes", "danger");
    }
  };

  const handleDropdownToggle = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const handleCloseDropdown = () => {
    setDropdownOpen(false);
  };

  const handleChevronClick = (index, level, e) => {
    e.stopPropagation();
    e.preventDefault();

    // Toggle the dropdown for the clicked row
    if (openDropdownIndex === `${index}-${level}`) {
      setOpenDropdownIndex(null); // Close if already open
    } else {
      setOpenDropdownIndex(`${index}-${level}`); // Open the clicked row's dropdown
    }
  };

  const handleChevronDownClick = (e) => {
    e.stopPropagation();
    setIsDropdown(!isDropdown);
  };

  const handleUpdateRequirement = () => {
    if (!requirementRootId) {
      return null;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", requirementRootId);
    newSearchParams.set("node_type", "RequirementRoot");
    router.push(`?${newSearchParams.toString()}`);
  };

  const handleUpdateWorkitem = (id, type) => {
    if (!id) {
      return null;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", id);
    newSearchParams.set("node_type", type);
    router.push(`?${newSearchParams.toString()}`);
  };

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", requirementRootId);
    newSearchParams.set("discussion_type", "RequirementRoot");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleItemClick = async (type, id, updateQuery = true, logs = null) => {
    if (updateQuery) {
      router.replace(`${pathname}/${type}/${id}`);
    }
  };

  const handleBackClick = () => {
    setSelectedItem(null);
    setNodeDetails(null);
    const newParams = new URLSearchParams(searchParams);
    newParams.delete("req_type");
    newParams.delete("req_id");
    router.replace(`?${newParams.toString()}`);
  };

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [sortConfig, setSortConfig] = useState({
    key: "id",
    direction: "ascending",
  });

  let filteredData = data.filter(
    (item) =>
      (item.id && item.id.toString().includes(searchTerm)) ||
      (item.title &&
        item.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.type &&
        item.type.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.status &&
        item.status.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.assignee_name &&
        item.assignee_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort the sliced filteredData
  filteredData.sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Add this function to handle pagination
  const paginateData = (data) => {
    const startIndex = (currentPage - 1) * pageSize;
    return data.slice(startIndex, startIndex + pageSize);
  };

  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }

    setSortConfig({ key, direction });
  };

  const handleUserAssigned = (user) => {
    const updatedNodeDetails = {
      ...nodeDetails,
      assignee_email: user.Email,
      assignee_name: user.Name,
      assignee_id: user.Username,
      assigned_at: new Date().toISOString(),
    };

    setNodeDetails(updatedNodeDetails);
  };

  const handleConfigureClick = (id, type) => {
    if (type === "RequirementRoot") {
      setNodeType("RequirementRoot");
      setConfigureNodeId(requirementRootId);
      setConfigureModel(true);
      // setIsVertCollapse(false);
    } else {
      setNodeType(type);
      setConfigureNodeId(id);
      setConfigureModel(true);
      // setIsVertCollapse(false);
    }
  };

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Requirements"
        message={en.UnableToLoadRequirements}
        onRetry={() => fetchRequirements()}
        panelType="main"
      />
    );
  }

  const level0Table = (item, level) => {
    return (
      <>
        <td
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="px-5 pt-2.5"
        >
          {item.id != null && (
            <button
              className={item.has_child ? "" : "opacity-50 cursor-not-allowed"}
              disabled={!item.has_child}
              onClick={() => handleExpandRow(item.id, level)}
            >
              {expandedRows.some(
                (row) => row.id === item.id && row.level === level
              ) ? (
                <CircleChevronDown width={18} height={18} />
              ) : (
                <CircleChevronRight width={18} height={18} />
              )}
            </button>
          )}
        </td>
        <td className="px-5 pt-1 " onClick={(e) => e.stopPropagation()}>
          <input
            id="default-checkbox"
            onClick={(e) => {
              e.stopPropagation();
            }}
            type="checkbox"
            checked={selectedItems.includes(item.id)}
            onChange={() => handleCheckboxChange(item.id)}
            className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-0 cursor-pointer"
          />
        </td>

        <td scope="row" className="px-5 pt-1  project-panel-content">
          {item.id}
        </td>
        <td
          className="px-5 pt-1 w-full overflow-hidden whitespace-nowrap hover:underline cursor-pointer project-panel-content"
          onClick={() => {
            if (item.id != null)
              handleItemClick(item.type, item.id, true, {
                items: data,
                current_index: level,
              });
          }}
          onMouseEnter={() => setDownChevronEnabled(level)}
          onMouseLeave={() => setDownChevronEnabled(-1)}
        >
          <div
            className="  overflow-hidden whitespace-nowrap overflow-ellipsis"
            title="Click to view the details"
          >
            {item.title}
          </div>
        </td>
      </>
    );
  };

  const level1Table = (item, level = 0) => {
    return (
      <>
        <td className="px-5 pt-1" onClick={(e) => e.stopPropagation()}></td>
        <td
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="px-5 pt-1"
        >
          <div className="flex items-center">
            {item.id != null && (
              <button
                className={
                  item.has_child ? "" : "opacity-50 cursor-not-allowed"
                } // Style when disabled
                onClick={() => handleExpandRow(item.id, level)}
                disabled={!item.has_child} // Disable if no child
              >
                {expandedRows.some(
                  (row) => row.id === item.id && row.level === level
                ) ? (
                  <CircleChevronDown width={18} height={18} />
                ) : (
                  <CircleChevronRight width={18} height={18} />
                )}
              </button>
            )}
            <input
              id="default-checkbox"
              // style={{ marginTop: "-2px" }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              type="checkbox"
              checked={selectedItems.includes(item.id)}
              onChange={() => handleCheckboxChange(item.id)}
              className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-0 cursor-pointer ml-2" // Adjusted spacing (ml-2 for margin-left)
            />
          </div>
        </td>
        <td scope="row" className="px-5 pt-1 ">
          {item.id}
        </td>
        <td
          className="px-12 pt-1  font-weight-medium text-gray-900 overflow-hidden whitespace-nowrap hover:underline cursor-pointer"
          onClick={() => {
            if (item.id != null)
              handleItemClick(item.type, item.id, true, {
                items: data,
                current_index: level,
              });
          }}
          onMouseEnter={() => setDownChevronEnabled(level)}
          onMouseLeave={() => setDownChevronEnabled(-1)}
        >
          <div
            className="overflow-hidden  whitespace-nowrap overflow-ellipsis w-full"
            title={item.title}
          >
            {item.title}
          </div>
        </td>
      </>
    );
  };

  const level2Table = (item, level = 0) => {
    return (
      <>
        <td className="px-5 pt-1 "></td>
        <td className="px-5 pt-1 "></td>
        <td
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="px-5 pt-1 "
        >
          <div className="flex items-center">
            {item.id != null && (
              <button
                className={
                  item.has_child ? "" : "opacity-50 cursor-not-allowed"
                } // Style when disabled
                onClick={() => handleExpandRow(item.id, level)}
                disabled={!item.has_child} // Disable if no child
              >
                {expandedRows.some(
                  (row) => row.id === item.id && row.level === level
                ) ? (
                  <CircleChevronDown width={18} height={18} />
                ) : (
                  <CircleChevronRight width={18} height={18} />
                )}
              </button>
            )}
            <input
              id="default-checkbox"
              style={{ marginTop: "-2px" }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              type="checkbox"
              checked={selectedItems.includes(item.id)}
              onChange={() => handleCheckboxChange(item.id)}
              className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-0 cursor-pointer ml-2" // Adjusted spacing (ml-2 for margin-left)
            />
          </div>
        </td>
        <td
          className="px-20 pt-1 font-weight-medium text-gray-900 overflow-hidden whitespace-nowrap hover:underline cursor-pointer"
          onClick={() => {
            if (item.id != null)
              handleItemClick(item.type, item.id, true, {
                items: data,
                current_index: level,
              });
          }}
          onMouseEnter={() => setDownChevronEnabled(level)}
          onMouseLeave={() => setDownChevronEnabled(-1)}
        >
          <div
            className="w-80 overflow-hidden whitespace-nowrap overflow-ellipsis"
            title={item.title}
          >
            {item.id}&nbsp;&nbsp;{item.title}
          </div>
        </td>
      </>
    );
  };

  const levelTable = (item, level) => {
    if (level === 0) return level0Table(item, level);
    else if (level === 1) return level1Table(item, level);
    else return level2Table(item, level);
  };

  const toggleDropdown = (itemId) => {
    setIsDropdownOpen((prevIsDropdownOpen) =>
      prevIsDropdownOpen === itemId ? null : itemId
    );
  };

  const openEditModal = (nodeId, nodeType, e) => {
    e.preventDefault();
    setDeleteParams({ nodeId, nodeType });
    showAlert("Functionality was not Implemented", "info");
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const confirmAndDelete = (id, type, e) => {
    e.preventDefault();
    e.stopPropagation();
    setDeleteParams({ id, type });
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    const { id, type } = deleteParams;
    setIsDeleting(true);
    try {
      const nodeId = id;
      const node_type = type;

      const response = await deleteNodeById(nodeId, node_type);
      await getTopLevelRequirements(projectId).then((topLevelRequirements) => {
        setData(topLevelRequirements);
        setRequirementRootId(topLevelRequirements[0].requirement_root_id);
      });

      if (response.status >= 200 && response.status < 300) {
        showAlert("Requirement deleted successfully", "success");
      } else {
        throw new Error(response.statusText || "Unknown error");
      }
    } catch (error) {

      showAlert("Failed to delete the Requirement!", "danger");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };

  const clearSearchQuery = () => {
    setSearchTerm('');
  };
  const handleReconfigureClick = ()=>{
    setReconfigureModel(true)
  }
  const handleReconfigureClose = ()=>{
    setReconfigureModel(false)
  }

  // Handle export to Excel
  const handleExportToExcel = async () => {
    try {
      setExporting(true);
      
      const blob = await exportRequirementsToExcel(projectId);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `requirements-${projectId}-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Cleanup
      window.URL.revokeObjectURL(url);
      
      showAlert('Requirements exported successfully!', 'success');
    } catch (error) {
      console.error('Export failed:', error);
      showAlert('Failed to export requirements. Please try again.', 'error');
    } finally {
      setExporting(false);
    }
  };
  const handleImportFromExcel = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Add handler for file selection
  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      showAlert('Please select a valid Excel file (.xlsx or .xls)', 'error');
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      // Send FormData to the server
      const response = await importRequirementsFromExcel(projectId, formData);

      await getTopLevelRequirements(projectId).then((topLevelRequirements) => {
        setData(topLevelRequirements);
        setRequirementRootId(topLevelRequirements[0].requirement_root_id);
      });
      if (response.message == "Requirements imported successfully") {
        showAlert('Requirements imported successfully!', 'success');
      } else {
        throw new Error(response.statusText || 'Unknown error');
      }
    } catch (error) {
      console.error('Import failed:', error);
      showAlert('Failed to import requirements. Please try again.', 'error');
    } finally {
      setLoading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  const updateRequirements = (newTopLevelRequirements) => {
    setData(newTopLevelRequirements);
    setRequirementRootId(newTopLevelRequirements[0].requirement_root_id);
  };

  // Menu options for CustomDropdown
  const menuOptions = [
    {
      label: "History",
      icon: BookOpen,
      onClick: handleViewPastDiscussion,
      tooltip: TOOLTIP_CONTENT.Requirements.history || "View Past Discussions",
    },
    {
      label: exporting ? "Exporting..." : "Export to Excel",
      icon: exporting ? null : () => (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8.66699 7.33385L14.1337 1.86719" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M14.6668 4.53398V1.33398H11.4668" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M7.33301 1.33398H5.99967C2.66634 1.33398 1.33301 2.66732 1.33301 6.00065V10.0007C1.33301 13.334 2.66634 14.6673 5.99967 14.6673H9.99967C13.333 14.6673 14.6663 13.334 14.6663 10.0007V8.66732" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      ),
      onClick: handleExportToExcel,
      disabled: exporting,
      tooltip: "Export requirements to Excel",
    },
    {
      label: exporting ? "Importing..." : "Import to Excel",
      icon: exporting ? null : () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
          <polyline points="14,2 14,8 20,8" />
          <path d="M12 18v-6" />
          <path d="M9 15l3 3 3-3" />
        </svg>
      ),
      onClick: handleImportFromExcel,
      disabled: exporting,
      tooltip: "Import requirements to Excel",
    },
  ];

  const renderRows = (items, level = 0) => {
    return items.map((item, index) => (
      <React.Fragment key={item.id || `no-child-${level}`}>
        <tr className="bg-custom-bg-primary border-b border-custom-border hover:bg-semantic-gray-50 cursor-pointer relative">
          {levelTable(item, level)}
          <td className="table-content px-5 pt-1">
            <span
              className={`py-0.5 typography-body-sm font-weight-semibold rounded-md ${typeBadgeColors[item.type] || typeBadgeColors.default
                } items-center w-fit flex justify-center max-w-14`}
            >
              <Square fill="currentColor" className="w-2 h-2 ml-2" />
              <span className="pl-1 mr-3 whitespace-normal break-words ">
                {item.type || "Empty"}
              </span>
            </span>
          </td>
          <td scope="col" className=" table-content px-3 pt-2">
            <button
              ref={(el) =>
                (dropdownRefs.current[parseInt(`${index}${level}`)] = el)
              }
              onClick={(e) => {
                if (isOpenDropdownOn == `${index}-${level}`)
                  setIsOpenDropdownOn(null);
                else {
                  e.stopPropagation();
                  e.preventDefault();
                  setIsOpenDropdownOn(`${index}-${level}`);
                  setOpenDropdownIndex(null);
                  setDropdownStyle(getPositionStyle(index, level));
                }
              }}
            >
              <MoreVertical />
            </button>
            {isOpenDropdownOn === `${index}-${level}` && (
              <div
                ref={dropdownRef}
                className={`absolute right-10 bg-white shadow-lg rounded-lg my-4 z-10 border border-primary  ${dropdownStyle}`}
              >
                <button
                  onClick={handleBtn}
                  className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                >
                  Edit
                </button>
                <button
                  type="button"
                  onClick={(e) => confirmAndDelete(item.id, item.type, e)}
                  className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                >
                  Delete
                </button>
              </div>
            )}
          </td>
        </tr>

        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          childLoading === item.id && (
            <tr key={`loading-${item.id}`}>
              <td colSpan="7" className="py-2 px-4 border-b text-center">
                Loading...
              </td>
            </tr>
          )}
        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          item.subItems &&
          renderRows(item.subItems, level + 1)}
      </React.Fragment>
    ));
  };

  return (
    <div
      className="requirement-container pr-4"
      onClick={() => {
        setIsOpenDropdownOn(null);
        setOpenDropdownIndex(null);
      }}
    >
      {loading ? (
        <TableLoadingSkeleton />
      ) : !selectedItem ? (
        <>
          {requirementReconfigEnable ? (
            <div className="mt-2">
              <RequirementsBanner value={`One or more requirements `} />
            </div>

          ):null}
            <div className="flex items-center justify-between pt-2">
              {/* Search Bar */}
              <div className="requirementSearchTab">
                    {data.length > 0 &&
                      !data.some(
                        (item) => item.id === null || item.title === null
                      ) && (
                        <Search
                          searchTerm={searchTerm}
                          setSearchTerm={setSearchTerm}
                        />
                      )}
                  </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <DynamicButton
                  variant="square"
                  size="sqDefault"
                  tooltip="Create Epic"
                  placement="bottom"
                  icon={Plus}
                  onClick={openModal}
                />

                {selectedItems.length > 0 && (
                  <DynamicButton
                    variant="square"
                    size="sqDefault"
                    tooltip="Delete all nodes"
                    placement="bottom"
                    icon={Trash}
                    onClick={deleteConformation}
                  />
                )}

                <DynamicButton
                  variant="primary"
                  icon={Settings}
                  tooltip={filteredData.length == 0 ? TOOLTIP_CONTENT.Requirements.create : TOOLTIP_CONTENT.Requirements.update}
                  text={filteredData.length == 0 && !searchTerm ? "Create Requirements" : "Update Requirements"}
                  onClick={handleUpdateRequirement}
                />


                   <DynamicButton
                   variant="primaryLegacy"
                   icon={Plus}
                   text="Auto Configure"
                   isLoading={loadingAutoConfigure}
                   onClick={() => handleConfigureClick(requirementRootId, "RequirementRoot")}
                   tooltip={TOOLTIP_CONTENT.Requirements.autoConfigure}
                 />

                  {requirementReconfigEnable ? (
                    <DynamicReconfigButton
                      variant="primary"
                      text="Re-Configure"
                      icon={RefreshCw}
                      isLoading={loadingReConfigure}
                      onClick={handleReconfigureClick}
                      tooltip={TOOLTIP_CONTENT.overview.autoConfigure}
                      isBlynk={true}
                    />
                  ) : null}

                <CustomDropdown options={menuOptions} align="right" />
              </div>
            </div>

          {/* Modals */}
          {isDeleteModalOpen && (
            <DeleteProjectModal
              isOpen={isDeleteModalOpen}
              onClose={cancelDelete}
              onDelete={handleDelete}
              isDeleting={isDeleting}
              type="requirements"
            />
          )}

          {isDeleteMultipleModal && (
            <DeleteProjectModal
              isOpen={isDeleteMultipleModal}
              onClose={cancelDeleteConformation}
              onDelete={handleDeleteMultipleNodes}
              isDeleting={isDeleting}
              type="Multiple Requirements"
            />
          )}

          <CreateTaskModal
            isOpen={isModalOpen}
            onClose={closeModalFunc}
            id={requirementRootId}
            projectId={projectId}
            updateRequirements={updateRequirements}
          />

          {/* Content Area */}
          {filteredData.length == 0 ? (
            <div className="flex items-center justify-center p-8">
              {searchTerm ? (
                <EmptyStateView type="noSearchResult" onClick={clearSearchQuery} />
              ) : (
                <EmptyStateView type="requirements" />
              )}
            </div>
          ) : (
            <div className="border rounded-md mt-4 shadow-sm">
              <WorkItemTable
                data={filteredData}
                totalItems={filteredData.length}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={(newPageSize) => {
                  setPageSize(newPageSize);
                  setCurrentPage(1);
                }}
                checkAll={checkAll}
                handleSelectAll={handleSelectAll}
                requestSort={requestSort}
                sortConfig={sortConfig}
                handleExpandRow={handleExpandRow}
                expandedRows={expandedRows}
                handleItemClick={handleItemClick}
                handleCheckboxChange={handleCheckboxChange}
                selectedItems={selectedItems}
                typeBadgeColors={typeBadgeColors}
                isOpenDropdownOn={isOpenDropdownOn}
                setIsOpenDropdownOn={setIsOpenDropdownOn}
                confirmAndDelete={confirmAndDelete}
                handleBtn={handleBtn}
                handleViewPastDiscussion={handleViewPastDiscussion}
              />
            </div>
          )}
        </>
      ) : (
        // Detailed Item View
        <>
          {/* Header with navigation */}
          <div
            onMouseLeave={() => setIsKebabDropdownOpen(false)}
            className="flex items-center justify-between w-full border-b sticky top-0 left-0 z-30 bg-white p-2"
          >
            <div className="flex items-center gap-1">
              <button
                onClick={handleBackClick}
                className="p-2 hover:bg-gray-100 rounded-md"
                title="Close"
              >
                <X size={18} />
              </button>
              <button className="p-2 hover:bg-gray-100 rounded-md" title="View Layers">
                <Layers2 size={18} />
              </button>
              <button
                className="p-2 hover:bg-gray-100 rounded-md"
                title="Previous Item"
                onClick={() => {
                  if (nodeDetails.logs && nodeDetails.logs.current_index > 0) {
                    handleItemClick(
                      nodeDetails.type,
                      nodeDetails.logs.items[nodeDetails.logs.current_index - 1].id,
                      true,
                      {
                        items: nodeDetails.logs.items,
                        current_index: nodeDetails.logs.current_index - 1,
                      }
                    );
                  }
                }}
              >
                <ChevronUp size={18} />
              </button>
              <button
                className="p-2 hover:bg-gray-100 rounded-md"
                title="Next Item"
                onClick={() => {
                  if (nodeDetails.logs && nodeDetails.logs.current_index < nodeDetails.logs.items.length) {
                    handleItemClick(
                      nodeDetails.type,
                      nodeDetails.logs.items[nodeDetails.logs.current_index + 1].id,
                      true,
                      {
                        items: nodeDetails.logs.items,
                        current_index: nodeDetails.logs.current_index + 1,
                      }
                    );
                  }
                }}
              >
                <ChevronDown size={18} />
              </button>
              <p className="text-gray-700 font-weight-medium ml-2">
                {!nodeLoading && (nodeDetails?.title || "Title")}
              </p>
            </div>

            {/* Actions dropdown */}
            <div className="relative">
              <button
                className="p-2 rounded-md hover:bg-gray-100"
                onClick={() => setIsKebabDropdownOpen(!isKebabDropdownOpen)}
              >
                <FaEllipsisH />
              </button>

              {isKebabDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white border rounded-md shadow-lg z-40">
                  <button
                    onClick={() => setIsPastDiscussionOpen(true)}
                    className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                  >
                    History
                  </button>
                  <button className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full">
                    Action 2
                  </button>
                  <button
                    onClick={() => setIsKebabDropdownOpen(true)}
                    className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                  >
                    Action 3
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end items-center gap-2 p-3 bg-gray-50 border-b">
            <button
              className="bg-primary hover:bg-primary-600 text-white font-weight-medium rounded-md px-4 py-2"
              onClick={() => handleUpdateWorkitem(nodeDetails.id, nodeDetails.type || "Requirement")}
            >
              Update {nodeDetails.type || "Requirement"}
            </button>
            <button
              className="bg-primary hover:bg-primary-600 text-white font-weight-medium rounded-md px-4 py-2"
              onClick={() => handleConfigureClick(nodeDetails.id, nodeDetails.type)}
            >
              Auto Configure
            </button>
          </div>

          {nodeLoading ? (
            <div className="p-6 bg-white rounded-md">
              <Loading2 />
            </div>
          ) : (
            <div className="p-6 bg-white rounded-md">
              {/* Title and Description */}
              <h2 className="typography-heading-4 font-weight-bold text-gray-800 mb-3">
                {nodeDetails?.title || "Title"}
              </h2>
              <div className="mb-6">
                {nodeDetails.descriptionDataType === "LongString" ? (
                  <Accordion
                    title="Description"
                    content={nodeDetails.description}
                    preview="Open this for Description"
                    defaultOpen={true}
                  />
                ) : (
                  <div className="text-gray-700">
                    {nodeDetails.properties.Description || "Description"}
                  </div>
                )}
              </div>

              {/* Details Grid */}
              <div className="grid gap-4">
                {/* Assignees */}
                <div className="flex items-center">
                  <div className="w-1/6 font-weight-semibold text-gray-600">Assignees</div>
                  <div className="flex items-center">
                    <Image
                      src={image}
                      alt="profile"
                      width={24}
                      height={24}
                      className="rounded-full bg-black p-1 mr-2"
                    />
                    <span>
                      {nodeDetails.assignee_name || nodeDetails.assignee_email || "Unassigned"}
                    </span>
                    {!nodeDetails.assignee_name && !nodeDetails.assignee_email && (
                      <>
                        <ListUserAssignModal
                          users={_listAllUsers.users}
                          show={showUsersToAssign}
                          setShow={setShowUsersToAssign}
                          nodeid={nodeDetails.id}
                          refresh={refresh}
                          setRefresh={setRefresh}
                        />
                        <button
                          onClick={() => setShowUsersToAssign(true)}
                          className="ml-2"
                        >
                          <CirclePlusIcon
                            width={18}
                            height={18}
                            className="text-gray-600"
                          />
                        </button>
                      </>
                    )}
                  </div>
                </div>

                {/* Created by */}
                <div className="flex items-center">
                  <div className="w-1/6 font-weight-semibold text-gray-600">Created by</div>
                  <div className="flex items-center">
                    <Image
                      src={kavia}
                      alt="profile"
                      width={24}
                      height={24}
                      className="rounded-full bg-black p-1 mr-2"
                    />
                    <span>Kavia AI</span>
                  </div>
                </div>

                {/* Due date */}
                <div className="flex items-center">
                  <div className="w-1/6 font-weight-semibold text-gray-600">Due date</div>
                  <span>Non applicable</span>
                </div>

                {/* Type */}
                <div className="flex items-center">
                  <div className="w-1/6 font-weight-semibold text-gray-600">Type</div>
                  <span>{nodeDetails.type || "Non applicable"}</span>
                </div>

                {/* Priority */}
                <div className="flex items-center">
                  <div className="w-1/6 font-weight-semibold text-gray-600">Priority</div>
                  <div className="relative flex items-center">
                    <span
                      className={`py-1 px-3 typography-body-sm font-weight-medium rounded-md ${priorityBadgeColors[priorityMap[nodeDetails.id]] || priorityBadgeColors.Default
                        }`}
                    >
                      {priorityMap[nodeDetails.id] || "Low"}
                    </span>
                    <button
                      onClick={(e) => handleChevronDownClick(e)}
                      className="ml-2 p-1 hover:bg-gray-100 rounded"
                    >
                      <ChevronDown width={16} height={16} />
                    </button>

                    {isDropdown && (
                      <div className="absolute top-full left-0 bg-white shadow-lg rounded-lg mt-1 z-10 w-64">
                        <ul className="list-none m-0 p-0"></ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Configure Modal */}
      {configureModel && (
        <ConfigureModal
          id={id}
          isNodeType={isNodeType}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            const successMessage =
              isNodeType === "RequirementRoot"
                ? "RequirementRoot Configured Successfully"
                : "Node Configured Successfully";
            showAlert(successMessage, "success");
            setIsVertCollapse(false);
          }}
        />
      )}
      {reconfigureModel && (
        <ReConfigureModal
          id={id}

          isNodeType={"RequirementRoot"}

          closeModal={handleReconfigureClose}
          setLoadingAutoConfigure={setLoadingReconfigure}
          onSubmitSuccess={() => {
            const successMessage =
             "RequirementRoot Re-Configured Successfully"
          showAlert(successMessage, "success");
            setIsVertCollapse(false);
          }}
        />
      )}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept=".xlsx,.xls"
        onChange={handleFileSelect}
      />
    </div>
  );
};

export default WorkItemTabContent;
