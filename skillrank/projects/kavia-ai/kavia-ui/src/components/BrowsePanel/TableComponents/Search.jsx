import React from 'react'
import { FaSearch } from 'react-icons/fa'

export default function Search({ searchTerm, setSearchTerm }) {
  return (
    <div className="flex items-center justify-center relative">
      <div className="absolute left-0 pl-3 z-10">
        <FaSearch className="text-semantic-gray-400 w-4 h-4" />
      </div>
      <input
        type="text"
        placeholder="Search requirements..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="px-10 pr-12 py-2.5 typography-body font-weight-normal text-semantic-gray-900 bg-white border border-semantic-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors w-full placeholder:text-semantic-gray-500"
      />
      <div className="absolute right-0 pr-3">
        <span className="text-semantic-gray-400 bg-semantic-gray-100 px-1.5 py-0.5 rounded-sm typography-caption font-weight-medium">
          /
        </span>
      </div>
    </div>
  )
}
