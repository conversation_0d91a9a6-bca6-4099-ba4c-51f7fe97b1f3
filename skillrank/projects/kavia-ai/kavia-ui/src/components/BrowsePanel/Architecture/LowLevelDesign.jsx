"use client";
import React, { useState, useEffect, useContext, useMemo } from "react";
import "@/styles/tabs/architecture/designTab/design.css"
import { useParams, useRouter, useSearchParams, usePathname } from "next/navigation";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { ExecutionContext } from "@/components/Context/ExecutionContext";
import { StateContext } from "@/components/Context/StateContext";
import { startCodeGeneration ,getReconfigNodeStatus, updateNodeByPriority } from "@/utils/api";
import { fetchArchitectureLeafNodes } from "@/utils/architectureAPI";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import CodeGenerationHandler from "@/app/modal/CodeGenerationHandler";
import { getPastCodeGenerationTasks } from "@/utils/batchAPI";
import PastTasksModal from "@/components/Modal/PastTasksModal";
import en from "../../../en.json";
import { fetchActiveTask } from "@/utils/api";
import { getRepository } from "@/utils/repositoryAPI";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { ArrowLeft , BookOpen, Eye, CodeXml} from 'lucide-react';
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import GenericCardGrid from "@/components/UIComponents/GenericCards/GenericCardGrid"
import CardGroupSkeletonLoder from "@/components/UIComponents/Loaders/CardGroupSkeletonLoder"
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";
import { BranchSelector } from "@/components/Git/BranchSelector";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import { frameworks, Generic, backendFrameworks, mobileFrameworks } from "@/constants/code_gen/platforms";
import { buildProjectUrl } from '@/utils/navigationHelpers';

const FullScreenLoader = () => (
  <div className="loader-overlay">
    <div className="loader-container">
      <div className="loader-content">
        <div className="loader-spinner"></div>
        <p className="loader-text">Initiating instance for code generation, please wait.</p>
      </div>
    </div>
  </div>
);

const LowLevelDesignTable = ({ data, onRowClick, onGenerateCode, showBaner, reconfigCount, isModal = false }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const { projectId } = useParams();
  const [activeArchitectureId, setArchitectureId] = useState(null);
  const { isVisible } = useCodeGeneration();

  const headers = [
    { key: "id", label: "ID" },
    { key: "title", label: "Title" },
    { key: "type", label: "Type" },
    { key: "description", label: "Description" },
    { key: "action", label: "Actions", actionLabel: "Generate Code" }
  ];

  // Group data by containers
  const groupedData = useMemo(() => {
    const grouped = data.reduce((acc, item) => {
      const containerKey = item.container ? item.container.title : "Ungrouped Components";
      if (!acc[containerKey]) {
        acc[containerKey] = {
          containerId: item.container?.id,
          components: []
        };
      }
      acc[containerKey].components.push({
        ...item,
        description: item.description && item.description.length > 30
          ? `${item.description.slice(0, 30)}...`
          : item.description || "TODO",
      });
      return acc;
    }, {});
    return grouped;
  }, [data]);

  // Filter grouped data based on search term
  const filteredGroupedData = useMemo(() => {
    if (!searchTerm) return groupedData;

    const filtered = {};
    Object.entries(groupedData).forEach(([containerName, containerData]) => {
      const matchingComponents = containerData.components.filter(item =>
        Object.values(item).some(value =>
          value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
      if (matchingComponents.length > 0) {
        filtered[containerName] = {
          ...containerData,
          components: matchingComponents
        };
      }
    });
    return filtered;
  }, [groupedData, searchTerm]);

  function addVisibility(data, id) {
    if (id) {
      return data.map(item => ({
        ...item,
        visible: item.id === id || true
      }));
    } else {
      return data.map(item => ({
        ...item,
        visible: true
      }));
    }
  }

  const getWorkingDir = async () => {
    const response = await fetchActiveTask(projectId);
    if (response.task_id) {
      setArchitectureId(response.architecuture_id);
    }
  }

  useEffect(() => {
    if (!isVisible) {
      getWorkingDir();
    }
  }, [filteredGroupedData, isVisible]);

  return (
    <div>
      {showBaner && <RequirementsBanner value={`One or more designs`} />}
      <GenericCardGrid
        isModal={isModal}
        data={Object.entries(filteredGroupedData).map(([name, data]) => ({
          container_name: name,
          components: addVisibility(data.components, activeArchitectureId)
        }))}
        onCardClick={(comp) => onRowClick(comp.id)}
        actionButtons={isModal ? [
          {
            icon: <Eye className="h-4 w-4" />,
            label: "View",
            onClick: (comp) => onRowClick(comp.id),
            className: "bg-gray-50 text-gray-600 hover:bg-gray-100"
          },
          {
            icon: <CodeXml className="h-4 w-4" />,
            label: "Generate Code",
            onClick: (comp) => onGenerateCode(comp.id),
            className: "bg-primary-50 text-primary-600 hover:bg-primary-100"
          }
        ] : []}
        uniquePageIdentifier={`arch-design-list-${projectId}`}
      />
    </div>
  );
};

const LowLevelDesign = ({ isModal = false }) => {
  const [lowLevelDesignData, setLowLevelDesignData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null)
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isPastTasksModalOpen, setIsPastTasksModalOpen] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const { isVisible, setIsVisible, setCurrentIframeUrl, setLlmModel } =
    useCodeGeneration();
  const params = useParams();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const projectId = params.projectId;
  const { showAlert } = useContext(AlertContext);
  const { setCurrentTaskDetailsId } = useContext(ExecutionContext);
  const { setLowLevelDesignVal } = useContext(ArchitectureContext);
  const { setIsVertCollapse } = useContext(StateContext);
  const [architectureId, setArchitectureId] = useState(null);
  const [showBaner,setShowBaner] = useState(false)
  const [reconfigCount,setReconfigCount] = useState(0)

  // New state for repository setup flow
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [repository, setRepository] = useState(null);
  const [currentBranch, setCurrentBranch] = useState(null);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: <Generic /> });
  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState(null);

  const getLowLevelDesignList = async () => {
    setIsLoading(true);
    try {
      const data = await fetchArchitectureLeafNodes(projectId);
      setLowLevelDesignData(data);
      setLowLevelDesignVal(data);
      const reconfig = await getReconfigNodeStatus(projectId)
      let showBanner = false;

      if (reconfig) {
        const hasReconfigNeeded = [
          ...(reconfig.Sub_Section || []),
          ...(reconfig.RobustnessTest || []),
          ...(reconfig.PerformanceTest || []),
          ...(reconfig.IntegrationTest || []),
          ...(reconfig.UnitTest || []),
          ...(reconfig.StateDiagram || []),
          ...(reconfig.Sequence || []),
          ...(reconfig.ClassDiagram || []),
          ...(reconfig.Algorithm || []),
          ...(reconfig.Design || [])
      ].some((item) => item?.reconfig_needed === true);

      const Count = [
        ...(reconfig.Sub_Section || []),
        ...(reconfig.RobustnessTest || []),
        ...(reconfig.PerformanceTest || []),
        ...(reconfig.IntegrationTest || []),
        ...(reconfig.UnitTest || []),
        ...(reconfig.StateDiagram || []),
        ...(reconfig.Sequence || []),
        ...(reconfig.ClassDiagram || []),
        ...(reconfig.Algorithm || []),
        ...(reconfig.Design || [])
    ].filter(item => item?.reconfig_needed === true).length;
    setReconfigCount(Count)

        showBanner = hasReconfigNeeded;
      }
      setShowBaner(showBanner)
    } catch (error) {

      setError(error)
    } finally {
      setIsLoading(false);
    }
  };


  useEffect(() => {
    if (projectId) {
      getLowLevelDesignList();
    }
  }, [projectId])

  const handleGenerateCode = async (itemId) => {
    setIsGeneratingCode(true);

    const makeApiCall = async (retryCount = 0) => {
      try {
        const response = await startCodeGeneration(projectId, itemId);

        if (response) {
          showAlert(
            "Code generation has been successfully initiated",
            "success"
          );
          setCurrentIframeUrl(response.iframe);
          setLlmModel(response.llm_model);
          showAlert(response.llm_model);
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.set("task_id", response.task_id);
          updateSessionStorageBackHistory();
          router.push(`${pathname}?${newSearchParams.toString()}`);
          setIsVisible(true);
        } else {
          throw new Error("No response from code generation API");
        }
      } catch (error) {

        if (retryCount < 2) {
          showAlert(
            `Retrying code generation (Attempt ${retryCount + 1})`,
            "info"
          );
          await makeApiCall(retryCount + 1);
        } else {
          showAlert(
            "Code generation initiation failed. Please try again later.",
            "error"
          );
        }
      }
    };

    try {
      await makeApiCall();
    } finally {
      setIsGeneratingCode(false);
      showAlert("Code generation process has been initiated.", "info");
    }
  };

  useEffect(() => {
    if (searchParams.get("task_id")) {
    setCodeGenSetupModal(false);
    setIsGeneratingCode(false);
    }

  },[searchParams])
  // New methods for repository setup flow
  const fetchRepositoryDetails = async (containerId) => {
    try {
      const response = await getRepository(projectId, containerId);
      if (response.repository) {
        setRepository(response.repository);
      } else {
        setRepository(null);
      }
    } catch (err) {

      showAlert('Failed to fetch repository details', 'error');
    }
  };

  const handlePlatformChange = (platformData) => {
    setCurrentPlatform(platformData);
    handlePropertyUpdate("platform", platformData.key);
    if(platformData?.key=="mobile")
      setCurrentFramework(mobileFrameworks[0]);

    if(platformData?.key=="web")
      setCurrentFramework(frameworks[0]);

    if(platformData?.key=="backend")
      setCurrentFramework(backendFrameworks[0]);
  }
   const handlePropertyUpdate = async (key, value) => {
      try {
        if (key === 'platform' || key === 'framework') {
          showAlert("Please wait...", "info");
        }
        if (!selectedItemId) {
          throw new Error('Design node ID not found');
        }

        const response = await updateNodeByPriority(selectedItemId, key, value);

        if (response === "success") {
          if (key == "platform") {
            showAlert("Platform updated successfully", "success");
          }
          else {
            showAlert("Content updated successfully", "success");
          }
        } else {
          throw new Error('Update failed');
        }
      } catch (error) {

        showAlert("Failed to update content", "error");
      }
    };


  const handleFrameworkChange = (newFramework) => {
    try {
      handlePropertyUpdate("framework", newFramework.key);
      setCurrentFramework(newFramework);
      showAlert("Framework updated successfully", "success");
    } catch (error) {

      showAlert("Failed to update framework", "error");
    }
  };

  const handleBranchUpdate = async (newBranch) => {
    setCurrentBranch(newBranch);
  };

  const handleConfigureRepoClick = () => {
    setShowRepoDetails(true);
  };

  const handleRepoDetailsClose = () => {
    setShowRepoDetails(false);
  };

  const handleCodeGeneration = () => {
    setArchitectureId(selectedItemId);
    setIsGeneratingCode(true);
  };

  const BranchSelection = () => {
    const containerId = lowLevelDesignData.find(item => item.id === selectedItemId)?.container?.id;

    return (
      <BranchSelector
        projectId={projectId}
        containerId={containerId}
        currentBranch={currentBranch}
        onUpdate={handleBranchUpdate}
        tooltip={"Select branch"}
        className="w-full"
      />
    );
  };

  const openCodeGenSetupModal = (itemId) => {
    setSelectedItemId(itemId);
    const item = lowLevelDesignData.find(item => item.id === itemId);
    if (item?.container?.id) {
      fetchRepositoryDetails(item.container.id);
    }
    setCodeGenSetupModal(true);
  };

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    try {
      const result = await getPastCodeGenerationTasks(
        projectId,
        null,
        currentLimit,
        currentSkip
      );
      setPastTasks(result.tasks);
      setTotalCount(result.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {

      showAlert("Failed to fetch past code generation tasks", "error");
    }
  };

  const handleViewPastDiscussion = async () => {
    await fetchPastTasks();
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_code_discussion", "true");
    newSearchParams.set("discussion_type", "code_generation");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
    setIsPastTasksModalOpen(true);
  };

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const viewPastDiscussions = params.get('view_past_code_discussion')
    const discussionType = params.get('discussion_type');

    if (viewPastDiscussions === 'true' && discussionType === 'code_generation') {
      setIsPastTasksModalOpen(true);
      fetchPastTasks(); // Load past tasks when modal is opened by query param
    }
  }, []);

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };
  const handleCloseModal = () => {
    setIsPastTasksModalOpen(false);

    // Remove the `view_pastDiscussion` from the URL when the modal is closed
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete("view_past_code_discussion");
    newSearchParams.delete("discussion_type");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleRowClick = (itemId) => {
   router.push(buildProjectUrl(projectId, `architecture/design/${itemId}`));
  };

  if (isLoading)
    return <CardGroupSkeletonLoder />

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Design"
        message={en.UnableToLoadDesign}
        onRetry={() => getLowLevelDesignList()}
        panelType='main'
      />
    );
  }

  const handleBack = () => {
    if (sessionStorage.getItem("querySet")) {
      const backTabs = Number(sessionStorage.getItem("querySet")); //number of tabs we need to go back to avoid the modal being repopped
      sessionStorage.removeItem("querySet");
      window.history.go(backTabs); //will go back required number of steps to avoid popping the discussion channel
    }
    else {
      router.back();
    }
  };

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col  border border-gray-200">
        <div className="relative px-2 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {"Design"}
                </h2>
              </div>
            </div>
            {lowLevelDesignData.length > 0 && (

              <button
              onClick={handleViewPastDiscussion}
              className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                      rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                      focus:ring-offset-2 typography-body-sm"
          >
              <BookOpen size={14} />
              History
          </button>

            )}
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  if (lowLevelDesignData.length === 0) {
    return (
      <EmptyStateView type="designtab" />
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* {isGeneratingCode && <FullScreenLoader />} */}
      <LowLevelDesignTable
        data={lowLevelDesignData}
        onRowClick={handleRowClick}
        onGenerateCode={(itemId) => {
          openCodeGenSetupModal(itemId);
        }}
        showBaner={showBaner}
        reconfigCount={reconfigCount}
        isModal={isModal}
      />
      {lowLevelDesignData.length === 0 && (
        <div className="design-tab-data-not-found">
          <EmptyStateView type="designtab" />
        </div>
      )}
      {architectureId && (
        <CodeGenerationHandler
          projectId={projectId}
          itemId={architectureId}
          onComplete={() => {
            setArchitectureId(null);
          }}
        />
      )}
      {isVisible && <CodeGenerationModal />}
      <PastTasksModal
        isOpen={isPastTasksModalOpen}
        onClose={handleCloseModal}
        tasks={pastTasks}
        totalCount={totalCount}
        limit={limit}
        skip={skip}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />
      {codeGenSetupModal && (
        <CodeGenerationSetupModal
          isGeneratingCode={isGeneratingCode}
          onClose={() => setCodeGenSetupModal(false)}
          BranchSelection={BranchSelection}
          currentPlatform={currentPlatform}
          onPlatformChange={handlePlatformChange}
          onConfirm={handleCodeGeneration}
          repository={repository}
          currentBranch={currentBranch}
          currentFramework={currentFramework}
          onFrameworkChange={handleFrameworkChange}
          isModal={isModal}
          projectId={projectId}
          containerId={lowLevelDesignData.find(item => item.id === selectedItemId)?.container?.id}
          handleRepoChange={(repo) => {
            setRepository(repo);
            showAlert('Repository configured successfully', 'success');
            // Close the repository modal if it's open
            setShowRepoDetails && setShowRepoDetails(false);
          }}
        />
      )}
      {showRepoDetails && (
        <RepositoryDetailsModal
          open={true}
          projectId={projectId}
          containerId={lowLevelDesignData.find(item => item.id === selectedItemId)?.container?.id}
          onClose={handleRepoDetailsClose}
          onSuccess={() => fetchRepositoryDetails(lowLevelDesignData.find(item => item.id === selectedItemId)?.container?.id)}
          handleRepoChange={setRepository}
        />
      )}
    </div>
  );
};

export default LowLevelDesign;
