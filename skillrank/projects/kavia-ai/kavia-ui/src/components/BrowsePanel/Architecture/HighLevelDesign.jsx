"use client";
import React, { useState, useEffect, useContext } from "react";
import Image from "next/image";
import dynamic from "next/dynamic";
import {
  useRouter,
  useParams,
  usePathname,
  useSearchParams,
} from "next/navigation";
import { <PERSON>aC<PERSON>s, FaEllipsisV } from "react-icons/fa";
import {
  Hash,
  Plus,
  CircleChevronDown,
  CircleChevronRight,
} from "lucide-react";
import EllipsisDropdown from "@/components/ElipsisDropdown/ElipsisDropDown";
import Accordion from "./Accordian";
import {
  fetchRootArchitecture,
  fetchChildArchitecture,
  getArchitectureInterfacesStatus,
  fetchArchitectureRelationships,
  updateNodeByPriority
} from "@/utils/api";
import { ArchitectureContext } from "../../Context/ArchitectureContext";
import graphicon from "../../../../public/images/graph_icon.svg";
import tableicon from "../../../../public/images/table_icon.svg";
import checboxicon from "../../../../public/images/checkBox_icon.svg";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import Button from "@/components/Buttons";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import en from "../../../en.json";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

import { Loading2 } from "@/components/Loaders/Loading";
import EnParser from '@/utils/enParser';
import ArchitectureRelationshipsTable from "./ArchitectureRelationshipsTable";
import Badge from "@/components/UIComponents/Badge/Badge"
const NoSSR = dynamic(() => import("../../Chart/MermaidChart"), { ssr: false });

const ToggleButton = ({ isToggled, toggle }) => (
  <button
    onClick={toggle}
    className={`flex items-center rounded-md border border-[#3e83ee] ${
      isToggled ? "m-1.5" : " "
    }`}
  >
    <div className="flex items-center space-x-2">
      <div
        className={`p-2 ${
          !isToggled ? "bg-[#dfeafc] text-[#3e83ee] rounded-sm" : ""
        }`}
      >
        <BootstrapTooltip title="Graph View" placement="bottom">
          <Image
            src={graphicon}
            alt="Left Icon"
            width={20}
            height={20}
            className={`${isToggled ? "opacity-50" : "opacity-100"}`}
          />
        </BootstrapTooltip>
      </div>
      <div
        className={`p-2 ${
          isToggled ? "bg-[#dfeafc] text-[#3e83ee] rounded-sm" : ""
        }`}
      >
        <BootstrapTooltip title="Table View" placement="bottom">
          <Image
            src={tableicon}
            alt="Table Icon"
            width={20}
            height={20}
            className={`${isToggled ? "opacity-100" : "opacity-50"}`}
          />
        </BootstrapTooltip>
      </div>
    </div>
  </button>
);

const HighLevelDesign = () => {
  const {
    updateArchitectures,
    setArchitectureRootId,
  } = useContext(ArchitectureContext);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const { showAlert } = useContext(AlertContext);

  const [data, setData] = useState({});
  const [rootData, setRootData] = useState(null);
  const [childData, setChildData] = useState([]);
  const [chartDefinition, setChartDefinition] = useState("");
  const [isToggled, setIsToggled] = useState(true);
  const [expandedRows, setExpandedRows] = useState([]);
  const [childLoading, setChildLoading] = useState(null);
  const [loading, setLoading] = useState(true);
  const [checkall, setCheckAll] = useState(false);
  const [sortConfig, setSortConfig] = useState({
    key: "id",
    direction: "ascending",
  });
  const [hoveredRow, setHoveredRow] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchTermInterface, setSearchTermInterface] = useState("");
  const [interfaceData, setInterfaceData] = useState([]);
  const [architectureRelationships, setArchitectureRelationships] = useState(
    []
  );
  const [relationshipsLoading, setRelationshipsLoading] = useState(true);

  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await fetchRootArchitecture(params.projectId);
        setData(data);
        setRootData(data.root || null);
        if (data.root?.id) {
          setArchitectureRootId(data.root.id);
        }
        if (data.children) {
          setChildData(data.children);
          updateArchitectures(data.children);
        }
        if (data.root?.properties?.MermaidChart) {
          setChartDefinition(data.root.properties.MermaidChart);
        }
        setInterfaceData(
          await getArchitectureInterfacesStatus(pathname.split("/")[3])
        );

        
      } catch (error) {
        
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.projectId]);

  useEffect(() => {
    const fetchRelationships = async () => {
      setRelationshipsLoading(true);
      try {
        const relationships = await fetchArchitectureRelationships(
          params.projectId,
          "implements"
        );
        if (relationships && relationships.implementations) {
          setArchitectureRelationships(relationships.implementations);
        } else {
          
          setArchitectureRelationships([]);
        }
      } catch (error) {
        
        setArchitectureRelationships([]);
      } finally {
        setRelationshipsLoading(false);
      }
    };

    fetchRelationships();
  }, [params.projectId]);

  const toggle = () => {
    setIsToggled(!isToggled);
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      if (!rootData?.id) {
        throw new Error('Root architecture ID not found');
      }

      const response = await updateNodeByPriority(rootData.id, key, value);
      
      if (response === "success") {
        // Update local state
        setRootData(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));
        
        // Also update the main data state if needed
        setData(prev => ({
          ...prev,
          root: {
            ...prev.root,
            properties: {
              ...prev.root.properties,
              [key]: value
            }
          }
        }));

        showAlert("Content updated successfully", "success");
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      
      showAlert("Failed to update content", "error");
    }
  };

  const handleExpandRow = async (itemId, level) => {
    const isExpanded = expandedRows.some(
      (row) => row.id === itemId && row.level === level
    );
    if (isExpanded) {
      setExpandedRows(
        expandedRows.filter(
          (row) => !(row.id === itemId && row.level === level)
        )
      );
    } else {
      setExpandedRows([...expandedRows, { id: itemId, level }]);
      setChildLoading(itemId);
      try {
        const childArchitecture = await fetchChildArchitecture(itemId);

        const addChildData = (items) => {
          return items.map((item) => {
            if (item.id == itemId) {
              return {
                ...item,
                subItems: childArchitecture.length
                  ? childArchitecture
                  : [
                      {
                        id: null,
                        title: "No child architecture components available",
                        status: "",
                        assignee_name: "",
                        subItems: [],
                      },
                    ],
              };
            }
            if (item.subItems) {
              return { ...item, subItems: addChildData(item.subItems) };
            }
            return item;
          });
        };
        setChildData(addChildData(childData));
      } catch (error) {
        
      } finally {
        setChildLoading(null);
      }
    }
  };

  const handleItemClick = (id) => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    router.push(buildProjectUrl(params.projectId, `architecture/high-level/${id}`));
  };

  const renderRows = (items, level = 0) => {
    return items.map((item) => (
      <React.Fragment key={item.id || `no-child-${level}`}>
        <tr
          className={`cursor-pointer relative table-val{ ${
            hoveredRow === item.id ? "bg-gray-100" : ""
          }`}
          onMouseEnter={() => setHoveredRow(item.id)}
          onMouseLeave={() => setHoveredRow(null)}
        >
          <td className="py-2 px-4 border-b text-center">
            {item.id && (
              <Image src={checboxicon} alt="Check box" className="w-4 h-4" />
            )}
          </td>
          <td
            className="py-2 px-4 border-b text-center"
            style={{ paddingLeft: `${level * 40}px` }}
          >
            {item.id !== null && item.has_child ? (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleExpandRow(item.id, level);
                }}
              >
                {expandedRows.some(
                  (row) => row.id === item.id && row.level === level
                ) ? (
                  <CircleChevronDown width={18} height={18} />
                ) : (
                  <CircleChevronRight width={18} height={18} />
                )}
              </button>
            ) : (
              <button disabled>
                <CircleChevronRight
                  width={18}
                  height={18}
                  style={{ opacity: 0.5 }}
                />
              </button>
            )}
          </td>
          <td className="py-2 px-2 border-b text-center">{item.id}</td>
          {item.id === null ? (
            <td className="py-2 px-4 border-b text-center">
              <EnParser content={en.ChildArchitectureNotAvailable} />
            </td>
          ) : (
            <>
              <td
                className="py-2 px-4 border-b underline-on-hover"
                style={{ cursor: "pointer" }}
                onClick={() => handleItemClick(item.id)}
              >
                <div className="truncate" title="Click to view the details">
                  {item.properties?.Title}
                </div>
              </td>
              <td className="py-2 px-4 border-b">
                <span className="bg-gray-200 rounded-xl px-2 py-0.5 typography-body-sm">
                  {item.properties?.Type || "N/A"}
                </span>
              </td>
              <td className="py-2 px-4 border-b ">
                <span className="px-3 py-1 ">
                  {item.properties?.Description.length > 30
                    ? `${item.properties.Description.slice(0, 30)}...`
                    : item.properties?.Description || "TODO"}
                </span>
                {hoveredRow === item.id && item.has_child && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <FaEllipsisV
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpandRow(item.id, level);
                      }}
                    />
                  </div>
                )}
              </td>
            </>
          )}
        </tr>
        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          childLoading === item.id && (
            <tr key={`loading-${item.id}`}>
              <td colSpan="7" className="py-2 px-4 border-b text-center">
                Loading...
              </td>
            </tr>
          )}
        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          item.subItems &&
          renderRows(item.subItems, level + 1)}
      </React.Fragment>
    ));
  };

  const filteredData = childData.filter((item) => {
    const idIncludesSearchTerm =
      item.id && item.id.toString().includes(searchTerm);
    const titleIncludesSearchTerm =
      item.properties?.Title &&
      item.properties.Title.toLowerCase().includes(searchTerm.toLowerCase());
    const typeIncludesSearchTerm =
      item.type && item.type.toLowerCase().includes(searchTerm.toLowerCase());

    return (
      idIncludesSearchTerm || titleIncludesSearchTerm || typeIncludesSearchTerm
    );
  });

  const filteredDataInterface = React.useMemo(() => {
    return interfaceData.filter((item) => {
      const searchLower = searchTermInterface.toLowerCase();
      return (
        item.id.toString().toLowerCase().includes(searchLower) ||
        (item.title && item.title.toLowerCase().includes(searchLower)) ||
        (item.description &&
          item.description.toLowerCase().includes(searchLower)) ||
        (item.source_node &&
          item.source_node.title.toLowerCase().includes(searchLower)) ||
        (item.target_node &&
          item.target_node.title.toLowerCase().includes(searchLower))
      );
    });
  }, [interfaceData, searchTermInterface]);

  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  const interface_columns = [
    { key: "id", label: "ID", sortable: true },
    { key: "title", label: "TITLE", sortable: true },
    { key: "type", label: "TYPE", sortable: true },
    {
      key: "description",
      label: "DESCRIPTION",
      render: (text) =>
        text.length > 64 ? text.substring(0, 64) + "..." : text,
    },
    {
      key: "source_node",
      label: "SOURCE NODE",
      render: (node) => (node ? node.title : "N/A"),
    },
    {
      key: "target_node",
      label: "TARGET NODE",
      render: (node) => (node ? node.title : "N/A"),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        const architectureId = pathname.split("/")[3];
        const data = await getArchitectureInterfacesStatus(architectureId);
        
      } catch (e) {
        
      }
    };

    fetchData();
  }, [pathname]); // Dependency array includes `pathname`

  if (loading) {
    return (
      <Loading2 />
    );
  }

  const handleUpdateClick = (e, component = false) => {
    if (!childData.length > 0) return;
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", rootData.id);
    newSearchParams.set("node_type", "Architecture");
    if (component) newSearchParams.set("discussion_node_type", "component");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };
  const updateProps = {
    onUpdateClick: handleUpdateClick,
    buttonText: "Update System Architecture",
  };
  const configureProps = {
    isConfigurable: true,
    nodeId: rootData?.id,
    nodeType: "ArchitectureRoot",
    setLoadingAutoConfigure: setLoadingAutoConfigure,
    onSubmitSuccess: () => {
      // You can add a success message or any other logic here
      showAlert("Auto configure initiated", "success");
    },
    buttonText: "Auto Configure",
  };
  const handleViewPastDiscussion = async (component = false) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", rootData.id);
    const discussionType = component ? "component" : "architecture";
    newSearchParams.set("discussion_type", discussionType);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleBtn = () => {
    showAlert("Functionality was not implemented", "info");
  };

  const ComponentTable = () => {
    return (
      <div>
        <table className="w-full  text-center  table-auto">
          <thead className=" uppercase h-10 bg-gray-100 table-header ">
            <tr>
              <th className="px-4 py-2">
                <input
                  id="default-checkbox"
                  type="checkbox"
                  className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-0"
                  onClick={() => setCheckAll(!checkall)}
                />
              </th>
              <th className="px-6 py-1"></th>
              <th
                className="px-2 py-2 text-center cursor-pointer"
                onClick={() => requestSort("id")}
              >
                <div className="flex">
                  <Hash width={15} height={15} className="mt-1" />
                  <div className="inline-block">
                    <span
                      style={{ marginBottom: "-4px" }}
                      className={`block ${
                        sortConfig.key === "id" &&
                        sortConfig.direction === "ascending"
                          ? "text-gray-800"
                          : "text-gray-400"
                      }`}
                    >
                      ▲
                    </span>
                    <span
                      className={`block ${
                        sortConfig.key === "id" &&
                        sortConfig.direction === "descending"
                          ? "text-gray-800"
                          : "text-gray-400"
                      }`}
                    >
                      ▼
                    </span>
                  </div>
                </div>
              </th>
              <th className="table-heading px-6 py-1">NAME</th>
              <th className="table-heading px-6 py-1">TYPE</th>
              <th className="table-heading px-6 py-1">DESCRIPTION</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y">
            {renderRows(filteredData)}
          </tbody>
        </table>
      </div>
    );
  };
  return (
    <div className="container py-2 px-1">
      {!rootData ? (
        <div className="flex justify-center h-96 items-center text-center">
          <EnParser content={en.HighlevelDesignNotFound} />
        </div>
      ) : (
        <>
          <div className="p-1 border-2 rounded-lg mb-5">
            <div
              className="flex sticky top-0 justify-between  items-center border-t border-r border-l pl-2 rounded-t"
              style={{ height: "80px" }}
            >
              <div className="flex items-center flex-grow max-h-auto flex-wrap">
                <h2
                  className="project-panel-heading whitespace-normal ml-2"
                  style={{
                    maxWidth: "400px",
                    overflowWrap: "break-word",
                  }}
                >
                  {rootData.properties?.Title}
                </h2>

                  {/* This div will display the value after the title */}
                   {rootData?.properties?.Type ? (
                <div className="py-1 ml-2">
                  <Badge type ={rootData.properties.Type}/>
                </div>
              ) : (
                ""
              )}
                </div>

              <div className="flex items-center justify-right flex-shrink-0 mr-2">
                <ConfigureButtons
                  updateProps={updateProps}
                  configureProps={configureProps}
                />
                <EllipsisDropdown
                  options={["Edit", "Delete", "view past discussion"]}
                  onOptionClick={(option) => {
                    if (option === "view past discussion") {
                      handleViewPastDiscussion();
                    } else {
                      handleBtn();
                    }
                  }}
                  buttonClassName="architecture-btn"
                />
              </div>
            </div>
            <div className="p-2 -mt-1">
              {/* {renderProperties(rootData.properties, data.ui_metadata)} */}
              <PropertiesRenderer
                properties={rootData.properties}
                metadata={data.ui_metadata}
                to_skip={["Title", "Type"]}
                projectId={params.projectId}
                architectureId={rootData?.id}
                onUpdate={handlePropertyUpdate} 
              />
            </div>
          
          </div>

          <div className="border-2 rounded-lg mb-5">
            <div
              className="flex sticky top-0 justify-between  items-center border-t border-r border-l pl-2 rounded-t"
              style={{ height: "80px", zIndex: 1 }}
            >
              <div className="flex items-center flex-grow max-h-auto flex-wrap">
                <h2 className="project-panel-heading whitespace-normal ml-2">
                  Architecture Relationships
                </h2>
              </div>
            </div>
            {relationshipsLoading ? (
              <div className="w-full flex justify-center">
                <Loading2 />
              </div>
            ) : architectureRelationships.length > 0 ? (
              <div className="overflow-x-auto bg-white">
                <ArchitectureRelationshipsTable
                  implementations={architectureRelationships}
                  projectId={params.projectId}
                />
              </div>
            ) : (
              <div className="m-3 flex items-center justify-center">
                <EnParser content={en.ArchitectureRelationshipsNotAvailable} />
              </div>
            )}
          </div>

          <div className=" border-2 rounded-lg mb-5">
            <div
              className="flex sticky top-0 justify-between  items-center border-t border-r border-l pl-2 rounded-t "
              style={{ height: "80px", zIndex: 1 }}
            >
              <div className="flex items-center flex-grow max-h-auto flex-wrap">
                <h2 className="project-panel-heading whitespace-normal ml-2">
                  Components
                </h2>
              </div>
              <div className="flex items-center justify-right flex-shrink-0">
                {filteredData.length > 0 ? (
                  <>
                    <Button
                      className="p-2 mr-4 flex items-center"
                      size="md"
                      onClick={(e) => {
                        handleUpdateClick(e, true);
                      }}
                    >
                      <FaCogs size={18} className="mr-2" />
                      {"Update Components"}
                    </Button>
                    <EllipsisDropdown
                      options={["Edit", "Delete", "view past discussion"]}
                      onOptionClick={(option) => {
                        if (option === "view past discussion") {
                          handleViewPastDiscussion(true);
                        } else {
                          handleBtn();
                        }
                      }}
                      buttonClassName="architecture-btn mr-3"
                    />
                  </>
                ) : (
                  <>
                    <Button
                      className="p-2 h-9 mr-3 bg-primary text-white rounded-md typography-body-sm font-weight-medium"
                      onClick={(e) => {
                        handleUpdateClick(e, true);
                      }}
                    >
                      <Plus size={18} className="mr-2" />
                      {"Create Components"}
                    </Button>
                  </>
                )}
                <div className="ml-2">
                  <ToggleButton isToggled={isToggled} toggle={toggle} />
                </div>
              </div>
            </div>
            {isToggled ? (
              <>
                {filteredData.length > 0 ? (
                  <div className="overflow-x-auto  bg-white  ">
                    <ComponentTable />
                  </div>
                ) : (
                  <>
                    <div className="m-3 flex items-center justify-center">
                      <EnParser content={en.ComponentsNotAvailable} />
                    </div>
                  </>
                )}
              </>
            ) : (
              <>
                <Accordion
                  isopen={true}
                  title={
                    rootData.properties.Title + " Diagram" ||
                    "Architecture Diagram"
                  }
                  defaultOpen={true}
                >
                  {rootData.properties.MermaidChart ? (
                    <NoSSR chartDefinition={rootData.properties.MermaidChart} />
                  ) : (
                    <div className="flex items-center justify-center">
                     <EnParser content= {en.MermaidChartNotAvailable} />
                    </div>
                  )}
                </Accordion>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default HighLevelDesign;
