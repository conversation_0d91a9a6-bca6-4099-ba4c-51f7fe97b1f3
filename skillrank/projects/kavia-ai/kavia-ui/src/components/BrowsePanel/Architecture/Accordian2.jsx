// components/Accordion.js
"use client"
import React, { useState } from "react";
import { ChevronDown, ChevronUp } from 'lucide-react';

const Accordion = ({ title,isopen=false, children }) => {
  const [isOpen, setIsOpen] = useState(isopen);
  const toggleAccordion = () => setIsOpen(!isOpen);

  return (
    <div className="">
      <div
        className="flex justify-between items-center p-3 cursor-pointer hover:bg-primary-50 transition-colors"
        onClick={toggleAccordion}
      >
        <div className="accordion-title truncate">{title}</div>
        <div className="text-primary">
          {isOpen ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </div>
      </div>
      {isOpen && (
        <div className="p-4 bg-gradient-orange-light bg-opacity-10 backdrop-blur-sm">
          {children}
        </div>
      )}
    </div>
  );
};

export default Accordion;
