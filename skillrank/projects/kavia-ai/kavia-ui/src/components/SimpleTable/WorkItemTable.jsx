
import React, { useRef, useState, useMemo } from 'react';
import { CircleChevronDown, CircleChevronRight, MoreVertical, Square, Clock, User, ChevronUp, ChevronDown, CheckCircle, Eye, ListTodo, AlertTriangle, Flame, Activity, LeafyGreen, Siren, } from 'lucide-react';
import Pagination from '../UIComponents/Paginations/Pagination';

const TableHeader = ({ checkAll, handleSelectAll, requestSort, sortConfig }) => {
  const renderColumn = (field) => {
    const commonProps = {
      requestSort: requestSort,
      sortConfig: sortConfig
    };

    switch (field) {
      case 'expand':
        return <th scope="col" className="px-2 py-1.5 w-8"></th>;
      case 'checkbox':
        return (
          <th scope="col" className="px-2 py-1.5 w-8">
            <input
              type="checkbox"
              checked={checkAll}
              onChange={handleSelectAll}
              className="w-3.5 h-3.5 text-primary bg-white border-semantic-gray-300 rounded focus:ring-primary focus:ring-1"
            />
          </th>
        );
      case 'id':
        return <SortableHeader title="#" align="center" field="id" {...commonProps} />;
      case 'title':
        return <SortableHeader title="TITLE" field="title" {...commonProps} />;
      case 'type':
        return <SortableHeader title="TYPE" align="center" field="type" {...commonProps} />;
      case 'status':
        return <SortableHeader title="STATUS" align="center" field="status" {...commonProps} />;
      case 'assignee':
        return <SortableHeader title="ASSIGNEE" align="center" field="assignee_name" {...commonProps} />;
      case 'priority':
        return <SortableHeader title="PRIORITY" align="center" field="priority" {...commonProps} />;
      case 'actions':
        return <th scope="col" className="px-2 py-1.5 w-16 text-center text-xs font-semibold text-semantic-gray-600 uppercase tracking-wide">ACTIONS</th>;
      default:
        return null;
    }
  };

  const columns = ['expand', 'checkbox', 'id', 'title', 'type', 'priority', 'actions'];

  return (
    <thead className="bg-semantic-gray-50 border-b border-semantic-gray-200" style={{ position: 'sticky', top: 0, zIndex: 1 }}>
      <tr>
        {columns.map((column) => (
          <React.Fragment key={column}>
            {renderColumn(column)}
          </React.Fragment>
        ))}
      </tr>
    </thead>
  );
};

const SortableHeader = ({ title, field, requestSort, sortConfig, icon, align }) => (
  <th scope="col" className={`px-2 py-1.5 cursor-pointer hover:bg-semantic-gray-100 transition-colors ${align === 'center' ? 'text-center' : 'text-left'}`} onClick={() => requestSort(field)}>
    <div className={`flex items-center gap-1 whitespace-nowrap ${align === 'center' ? 'justify-center' : ''}`}>
      {icon}
      <span className="text-xs font-semibold text-semantic-gray-600 uppercase tracking-wide">{title}</span>
      <div className="inline-block ml-1">
        <ChevronUp
          className={`h-3 w-3 transition-colors ${sortConfig.key === field && sortConfig.direction === 'ascending'
            ? 'text-primary'
            : 'text-semantic-gray-400'
            }`}
        />
        <ChevronDown
          className={`h-3 w-3 transition-colors ${sortConfig.key === field && sortConfig.direction === 'descending'
            ? 'text-primary'
            : 'text-semantic-gray-400'
            }`}
        />
      </div>
    </div>
  </th>
);

const StatusBadge = ({ status }) => {
  const statusConfig = {
    'DONE': {
      icon: CheckCircle,
      styles: 'bg-semantic-green-100 text-semantic-green-800 border-semantic-green-200',
      iconColor: 'text-semantic-green-600'
    },
    'IN PROGRESS': {
      icon: Clock,
      styles: 'bg-semantic-yellow-100 text-semantic-yellow-800 border-semantic-yellow-200',
      iconColor: 'text-semantic-yellow-600'
    },
    'IN REVIEW': {
      icon: Eye,
      styles: 'bg-primary-100 text-primary-800 border-primary-200',
      iconColor: 'text-primary-600'
    },
    'TODO': {
      icon: ListTodo,
      styles: 'bg-semantic-gray-100 text-semantic-gray-800 border-semantic-gray-200',
      iconColor: 'text-semantic-gray-600'
    },
    'N/A': {
      icon: AlertTriangle,
      styles: 'bg-semantic-gray-100 text-semantic-gray-600 border-semantic-gray-200',
      iconColor: 'text-semantic-gray-500'
    }
  };

  const config = statusConfig[status || 'N/A'];
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center px-1 py-0.5 text-xs font-medium rounded border min-w-0 max-w-full ${config.styles}`}>
      <Icon className={`w-2 h-2 mr-0.5 flex-shrink-0 ${config.iconColor}`} />
      <span className="truncate">{status || 'N/A'}</span>
    </span>
  );
};

const PriorityBadge = ({ priority }) => {
  const normalizedPriority = (() => {
    if (typeof priority === "number") return 'Low';
    if (priority === 'High' || priority === 'Medium' || priority === 'Low' || priority === 'Critical') return priority;
    return 'default';
  })();

  const priorityConfig = {
    'Critical': {
      icon: Siren,
      styles: 'bg-semantic-red-100 text-semantic-red-900 border-semantic-red-300 shadow-md ring-2 ring-semantic-red-400 ring-opacity-50',
      iconColor: 'text-semantic-red-700',
      animation: 'animate-pulse'
    },
    'High': {
      icon: Flame,
      styles: 'bg-semantic-red-100 text-semantic-red-800 border-semantic-red-200 shadow-sm',
      iconColor: 'text-semantic-red-600',
      pulse: true
    },
    'Medium': {
      icon: Activity,
      styles: 'bg-semantic-yellow-100 text-semantic-yellow-800 border-semantic-yellow-200 shadow-sm',
      iconColor: 'text-semantic-yellow-600',
      animation: 'hover:scale-105 transition-transform duration-200'
    },
    'Low': {
      icon: LeafyGreen,
      styles: 'bg-semantic-green-100 text-semantic-green-800 border-semantic-green-200 shadow-sm',
      iconColor: 'text-semantic-green-600'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-semantic-gray-100 text-semantic-gray-600 border-semantic-gray-200 shadow-sm',
      iconColor: 'text-semantic-gray-500'
    }
  };

  const config = priorityConfig[normalizedPriority];
  const Icon = config.icon;

  return (
    <span
      className={`
        inline-flex items-center px-1 py-0.5 text-xs font-medium
        rounded-full border whitespace-nowrap ${config.styles} ${config.pulse ? 'animate-pulse' : ''}
        ${config.animation || ''} cursor-default
      `}
    >
      <Icon className={`w-2 h-2 mr-0.5 ${config.iconColor}`} />
      {normalizedPriority || 'Not Set'}
    </span>
  );
};

const NameAvatar = ({ assigneeName }) => {

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="flex items-center gap-1">
      <div className="relative inline-flex items-center justify-center w-5 h-5 overflow-hidden rounded border border-semantic-gray-300 bg-primary-50">
        {
          assigneeName ? (
            <span className="text-xs font-medium text-primary">
              {getInitials(assigneeName)}
            </span>
          )
            :
            (
              <User className='w-2.5 h-2.5 text-primary' />
            )
        }
      </div>
      <span className="text-semantic-gray-600 text-xs font-medium">{assigneeName || "Unassigned"}</span>
    </div>
  );

}

const TableRow = ({ item, level, handleExpandRow, expandedRows, handleItemClick, handleCheckboxChange, selectedItems, typeBadgeColors, isOpenDropdownOn, setIsOpenDropdownOn, confirmAndDelete, handleBtn, handleViewPastDiscussion }) => {
  const dropdownRef = useRef(null);
  const [dropdownStyle, setDropdownStyle] = useState('');
  const dropdownRefs = useRef([]);

  const indentationWidth = 24;

  const renderCell = (type) => {
    switch (type) {
      case 'expand':
        return (
          <td className="px-2 py-1.5 text-center"
            style={{ paddingLeft: level > 0 ? `${(level) * indentationWidth}px` : '' }}
          >
            {item.id != null && (
              <div className='flex items-center justify-center'>
                <button
                  className={`text-semantic-gray-400 hover:text-primary transition-colors ${item.has_child ? "text-semantic-gray-600" : "opacity-50 cursor-not-allowed"}`}
                  disabled={!item.has_child}
                  onClick={() => handleExpandRow(item.id, level)}
                >
                  {expandedRows.some(row => row.id === item.id && row.level === level) ? (
                    <CircleChevronDown className="w-4 h-4" />
                  ) : (
                    <CircleChevronRight className="w-4 h-4" />
                  )}
                </button>
              </div>
            )}
          </td>
        );
      case 'checkbox':
        return (
          <td className="px-2 py-1.5 text-center">
            <input
              type="checkbox"
              checked={selectedItems.includes(item.id)}
              onChange={() => handleCheckboxChange(item.id)}
              className="w-3.5 h-3.5 text-primary bg-white border-semantic-gray-300 rounded focus:ring-primary focus:ring-1"
            />
          </td>
        );
      case 'id':
        return <td className="px-2 py-1.5 text-semantic-gray-600 text-center text-sm font-medium">{item.id}</td>;
      case 'title':
        return (
          <td className="px-2 py-1.5">
            <div
              className="text-semantic-gray-900 hover:text-primary cursor-pointer custom-table-text-ellipsis text-sm font-medium transition-colors"
              onClick={() => item.id != null && handleItemClick(item.type, item.id, true)}
              style={{ minWidth: '180px' }}
            >
              {item.title}
            </div>
          </td>
        );
      case 'type':
        return (
          <td className="px-2 py-1.5 text-center">
            <span className={`inline-flex items-center gap-0.5 px-1.5 py-0.5 text-xs font-medium rounded ${typeBadgeColors[item.type] || typeBadgeColors.default}`}>
              <Square fill="currentColor" className="w-1.5 h-1.5" />
              {item.type}
            </span>
          </td>
        );
      case 'status':
        return (
          <td className="px-2 py-1.5 text-center">
            <StatusBadge status={item.status} />
          </td>
        );
      case 'assignee':
        return (
          <td className="px-2 py-1.5 text-center">
            <NameAvatar assigneeName={item.assignee_name} />
          </td>
        );
      case 'priority':
        return (
          <td className="px-2 py-1.5 text-center">
            <PriorityBadge priority={item.priority} />
          </td>
        );
      case 'actions':
        return (
          <td className="px-3 py-3 relative text-center">
            <button
              ref={(el) => (dropdownRefs.current[parseInt(`${item.id}${level}`)] = el)}
              onClick={(e) => {
                e.stopPropagation();
                setIsOpenDropdownOn(isOpenDropdownOn === `${item.id}-${level}` ? null : `${item.id}-${level}`);
                setDropdownStyle(getPositionStyle(item.id, level));
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <MoreVertical className="w-5 h-5" />
            </button>
            {isOpenDropdownOn === `${item.id}-${level}` && (
              <div className={`absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border ${dropdownStyle}`}>
                <div className="py-1">
                  <button onClick={handleBtn} className="block px-4 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                    Edit
                  </button>
                  <button onClick={(e) => confirmAndDelete(item.id, item.type, e)} className="block px-4 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                    Delete
                  </button>
                </div>
              </div>
            )}
          </td>
        );
      default:
        return null;
    }
  };

  const getPositionStyle = (index, level) => {
    const dropdown = dropdownRefs.current[parseInt(`${index}${level}`)];
    if (!dropdown) return '';
    const rect = dropdown.getBoundingClientRect();
    const isBottomVisible = (window.innerHeight - rect.bottom) < 400;
    return isBottomVisible ? 'bottom-0.5' : 'top-3';
  };

  const columns = ['expand', 'checkbox', 'id', 'title', 'type', 'priority', 'actions'];

  return (
<tr className="border-b hover:bg-gray-50">
  {columns.map((column) => (
    <React.Fragment key={column}>{renderCell(column)}</React.Fragment>
  ))}
</tr>

  );
};

const WorkItemTable = ({ data, checkAll, pageSize, totalItems, currentPage, onPageChange, onPageSizeChange, handleSelectAll, requestSort, sortConfig, handleExpandRow, expandedRows, handleItemClick, handleCheckboxChange, selectedItems, typeBadgeColors, isOpenDropdownOn, setIsOpenDropdownOn, confirmAndDelete, handleBtn, handleViewPastDiscussion, StoryLevel = 0 }) => {
  const pageCount = Math.ceil(totalItems / pageSize);


  const handlePageSizeChange = (newSize) => {
    if (typeof onPageSizeChange === 'function') {
      onPageSizeChange(Number(newSize));
    }
  };
  const handlePaginationPageChange = (page) => {
    if (typeof onPageChange === 'function') {
      onPageChange(page);
    }
  };

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return data.slice(startIndex, startIndex + pageSize);
  }, [data, currentPage, pageSize]);


  const renderRows = (items, level) => {
    return items.map((item) => (
      <React.Fragment key={item.id || `no-child-${level}`}>
        <TableRow
          item={item}
          level={level}
          handleExpandRow={handleExpandRow}
          expandedRows={expandedRows}
          handleItemClick={handleItemClick}
          handleCheckboxChange={handleCheckboxChange}
          selectedItems={selectedItems}
          typeBadgeColors={typeBadgeColors}
          isOpenDropdownOn={isOpenDropdownOn}
          setIsOpenDropdownOn={setIsOpenDropdownOn}
          confirmAndDelete={confirmAndDelete}
          handleBtn={handleBtn}
          handleViewPastDiscussion={handleViewPastDiscussion}
        />
        {expandedRows.some(row => row.id === item.id && row.level === level) &&
          item.subItems &&
          renderRows(item.subItems, level + 1)}
      </React.Fragment>
    ));
  };

  return (
    <div className="w-full bg-white border border-semantic-gray-200 rounded-lg shadow-custom-card">
      <div className="overflow-x-auto w-full">
        <table className="w-full border-collapse">
          <TableHeader
            checkAll={checkAll}
            handleSelectAll={handleSelectAll}
            requestSort={requestSort}
            sortConfig={sortConfig}
          />
          <tbody className="bg-white divide-y divide-semantic-gray-100">
            {renderRows(paginatedData, StoryLevel)}
          </tbody>
        </table>
      </div>
      <Pagination
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePaginationPageChange}
        onPageSizeChange={handlePageSizeChange}
        pageSizeOptions={[10, 20, 50, 100]}
      />
    </div>
  );
};

export default WorkItemTable;