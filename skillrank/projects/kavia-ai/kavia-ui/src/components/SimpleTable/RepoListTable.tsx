import React, { useState, useRef, useEffect, useContext } from 'react';
import { ChevronDown, Loader2  } from 'lucide-react';
import { getCookie } from '@/utils/auth';
import { getHeadersRaw } from '@/utils/api';
import ProjectAssetPagination from '../UIComponents/Paginations/ProjectAssetPagination';
import EmptyStateView from '../Modal/EmptyStateModal';
import { AlertContext } from '../NotificationAlertService/AlertList';

interface Branch {
  name: string;
  isDefault: boolean;
}

interface Repository {
  id: number;
  name: string;
  description: string | null;
  languages: string[];
  branch: string | null;
  branches: Branch[];
  selected: boolean;
  lastUpdated: string;
  clone_url: string;
  path?: string;
  repo_type?: 'public' | 'private';
}

interface QueryBranch {
  buildId: string;
  kg_creation_status: Number;
  lastUpdated: string;
  name: string;
  pr_creation_details: string | undefined | null;
  pr_details: string | undefined | null;
  status: string;
  type: string;
  upstream: boolean;
}

interface RepoInQuery {
  id: string;
  name: string;
  gitUrl: string;
  repoType: 'public' | 'private';
  branches: QueryBranch[];
}

interface RepositoryTableProps {
  repoWithKnowledge: RepoInQuery[] | null;
  repositories: Repository[];
  onToggleRepository: (index: number) => void;
  onUpdateRepository?: (index: number, updates: Partial<Repository>) => void;
  activeTabVal?: string;
  organization_name?: string;
}

interface BranchSelectProps {
  repo: Repository;
  index: number;
  onBranchSelect: (branch: string) => void;
  isSelectable?: boolean;
  onUpdateRepository: (index: number, updates: Partial<Repository>) => void;
  isActive: boolean;
  onDropdownToggle: (index: number) => void;
  onDropdownClose: () => void;
  tableRef: React.RefObject<HTMLElement | null>
  activeTabVal?: string;
  organization_name?: string;
}

const BranchSelect: React.FC<BranchSelectProps> = ({ 
  repo, 
  index, 
  onBranchSelect, 
  isSelectable=true,
  onUpdateRepository,
  isActive,
  onDropdownClose,
  onDropdownToggle,
  tableRef,
  activeTabVal,
  organization_name
}) => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<string | null>(repo.branch);
  const dropdownButtonRef = useRef<HTMLDivElement | null>(null);
  const branchDropdownRef = useRef<HTMLDivElement | null>(null);
  const [menuPosition, setMenuPosition] = useState({top: 0, left: 0});
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredBranches, setFilteredBranches] = useState<Branch[]>([]);

  useEffect(() => {
    const updateDropdownPosition = () => {
      const rect = dropdownButtonRef?.current?.getBoundingClientRect();
      if(rect && branchDropdownRef.current) {
        setMenuPosition({top: rect.bottom + window.scrollY, left: rect.left - 100 + window.scrollX})
      }
    }
    
    const closeMenu = (e: MouseEvent | Event) => {
      if(dropdownButtonRef.current && branchDropdownRef.current && e.target instanceof HTMLElement){
        if(!dropdownButtonRef.current.contains(e.target) && !branchDropdownRef.current.contains(e.target)){
          onDropdownClose();
        }
      }
    }
    const tableConatainer = tableRef?.current;

    if(isActive){
      updateDropdownPosition();
      tableConatainer?.addEventListener("scroll", closeMenu);
      window.addEventListener("resize", updateDropdownPosition);
      document.addEventListener("mousedown", closeMenu);
    }

    return(() => {
      tableConatainer?.removeEventListener("scroll", closeMenu);
      window.removeEventListener("resize", updateDropdownPosition);
      document.removeEventListener("mousedown", closeMenu);
    })
  }, [isActive])

  useEffect(() => {
    setFilteredBranches(
      branches.filter((branch) => 
        branch.name.includes(searchTerm)
      )
    )
  }, [searchTerm, branches])

  const fetchBranches = async () => {

    // Extract owner from path safely
    const pathParts = repo.path?.split('/') || [];
    const owner = pathParts[3]; // GitHub URLs typically follow pattern: https://github.com/owner/repo

    setIsLoading(true);
    setError(null);
    try {

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/oauth/github/repo-branches`, {
        method: 'POST',
        headers: getHeadersRaw(),
        body: JSON.stringify({
          user_id: await getCookie('userId'),
          owner: owner,
          repo_name: repo.name,
          org: activeTabVal === 'organization' ? organization_name : false
        }),
      });

      if (!response.ok) throw new Error('Failed to fetch branches');
      
      const data: Branch[] = await response.json();
      if (data.length === 0) {
        setError('No branches found');
        return;
      }
      setBranches(data);

      // If no branch is selected, choose the default branch
      if (!selectedBranch) {
        const defaultBranch = data.find(branch => branch.isDefault);
        if (defaultBranch) {
          handleBranchSelect(defaultBranch.name);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // In RepositoryTable.tsx, update the BranchSelect component's handleBranchSelect function:

  const handleBranchSelect = (branchName: string) => {
    setSelectedBranch(branchName);
    onBranchSelect(branchName);
    onUpdateRepository(index, { 
        branch: branchName,
        clone_url: repo.path,
        description: repo.description,
        name: repo.name
    });
    };

  const handleButtonClick = () => {
    if (!isActive) {
      fetchBranches();
    }
    onDropdownToggle(index);
  };

  return (
    <div className="w-[200px]" ref = {dropdownButtonRef}>
      <button
        onClick={handleButtonClick}
        disabled={!isSelectable}
        className={`w-full flex items-center justify-between px-3 py-1.5 border rounded-md
          ${!isSelectable 
            ? 'bg-gray-100 cursor-not-allowed border-gray-200' 
            : 'bg-white border-gray-300 hover:bg-gray-50'
          }`}
      >
        <span className={`typography-body-sm truncate max-w-[150px] ${!isSelectable ? 'text-gray-400' : 'text-gray-700'}`}>
          {selectedBranch || 'Select branch'}
        </span>
        <ChevronDown
          className={`w-4 h-4 transition-transform flex-shrink-0
            ${isActive ? 'transform rotate-180' : ''} 
            ${!isSelectable ? 'text-gray-400' : 'text-gray-500'}`}
        />
      </button>

      {isActive && (
        <div ref = {branchDropdownRef} 
          className={`fixed z-10 w-[300px] mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto`}
          style={{ top: `${menuPosition.top}px`, left: `${menuPosition.left}px` }}
        >
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="w-5 h-5 text-primary animate-spin" />
              <span className="ml-2 text-gray-600">Loading branches...</span>
            </div>
          ) : error ? (
            <div className="p-4 text-black-500 text-center">{error}</div>
          ) : (
            <>
              {branches.length >= 20 && (
                <div className='sticky w-full p-2 bg-white'>
                  <input 
                    className='rounded-md border-2 border-gray-200 w-full' 
                    value={searchTerm}
                    placeholder='Search Branch...'
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              )}
              {filteredBranches.length==0 ? (
                <div className="p-4 text-black-500 text-center">No branches found</div>
              ) : 
              filteredBranches.map((branch, idx) => (
                <button
                  key={idx}
                  onClick={() => {
                    if (isSelectable) {
                      handleBranchSelect(branch.name);
                    }
                    onDropdownToggle(index);
                  }}
                  className={`w-full px-4 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100
                    ${selectedBranch === branch.name ? 'bg-primary-50' : ''}`}
                >
                  <div className="flex items-center justify-between">
                    <span className="typography-body-sm text-gray-900">{branch.name}</span>
                    {branch.isDefault && (
                      <span className="typography-caption text-primary bg-primary-50 px-2 py-0.5 rounded">
                        default
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </>
          )}
        </div>
      )}
    </div>
  );
};

const RepositoryListTable: React.FC<RepositoryTableProps> = ({ 
  repoWithKnowledge,
  repositories, 
  onToggleRepository,
  onUpdateRepository = () => {} ,
  activeTabVal,
  organization_name
}) => {
    const allowedLanguages = [
      'JavaScript', 'TypeScript', 'HTML', 'CSS', 'SCSS', 
      'JSON', 'YAML', 'Markdown', 'Python', 'C', 'C++', 
      'COBOL', 'Prompt', 'Jinja2'
    ];

    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(20);
    const [searchQuery, setSearchQuery] = useState('');
    const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
    const tableRef = useRef<HTMLDivElement | null>(null);
    const {showAlert} = useContext(AlertContext);

    const checkIfKnowledgeExists = (repository: Repository) => {
      const existingRepo = repoWithKnowledge?.filter((repo) => repo.id == repository.id.toString())
      if(existingRepo){
        if(existingRepo[0]?.branches.find((branch) => branch.name === repository.branch)){
          return true;
        }
        else{
          return false;
        }
      }
      else{
        return false;
      }
    }

    

  const filteredRepositories = repositories.filter((repo) =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getCurrentPageItems = () => {
    const items =  filteredRepositories;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return items.slice(startIndex, endIndex);
  };
  const handleToggleRepository = (pageIndex: number) => {
    const actualIndex = (currentPage - 1) * pageSize + pageIndex;
    const originalRepo = filteredRepositories[actualIndex];
    const trueOriginalIndex = repositories.findIndex(
      (repo) => repo === originalRepo
    );
    onToggleRepository(trueOriginalIndex);
  };
  return (
    <div className='flex flex-col h-full'>
      <div className='relative mt-2 ml-2 mr-2 flex-grow-0'>
        <input
          type="text"
          placeholder="Search repositories..."
          className="w-full border border-gray-300 rounded-lg px-6 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent "
          value={searchQuery}
          onChange={(e) => {
            setCurrentPage(1);
            setSearchQuery(e.target.value);
          }} 
        />
      </div>
      <div className="flex-grow relative mt-3">
        <div ref={tableRef} className=" flex-grow overflow-hidden flex flex-col max-h-[42vh] overflow-y-auto">
          <div  className="flex-grow   border border-[#e9edf5] rounded-[5px]">
            {repositories.length === 0 ? (
                <EmptyStateView
                type="noRepoFound"
              />
              ) : getCurrentPageItems().length === 0 ? (
                <EmptyStateView
                    type="noSearchResult"
                    onClick={() => setSearchQuery("")}
                  />
              ) : (
              <table className=" w-full border-collapse">
                <thead className="bg-[#f7f8fc] sticky top-0 z-10">
                  <tr>
                    <th className="w-12 py-3 px-4"></th>
                    <th className="text-left py-3 px-4 w-1/4">
                      <span className="text-[#687082] typography-caption font-weight-medium uppercase">Repository</span>
                    </th>
                    <th className="text-left py-3 px-4 w-1/3">
                      <span className="text-[#687082] typography-caption font-weight-medium uppercase">Description</span>
                    </th>
                    <th className="text-left py-3 px-4 w-[200px]">
                      <span className="text-[#687082] typography-caption font-weight-medium uppercase">Branch</span>
                    </th>
                    
                  </tr>
                </thead>
                <tbody>
                  {getCurrentPageItems().map((repo, index) => {
                    const knowledgeDoesNotExist = !checkIfKnowledgeExists(repo);
                    return (
                      <tr
                        key={repo.id}
                        className={`border-t border-[#e9edf5] ${knowledgeDoesNotExist ? 'hover:bg-gray-50' : 'bg-gray-100'}`}
                      >
                        <td className="py-4 px-4">
                          <input
                            type="checkbox"
                            checked={repo.selected}
                            onChange={() =>  knowledgeDoesNotExist && handleToggleRepository(index)}
                            onClick = {() => {
                                if (!knowledgeDoesNotExist) {showAlert("This branch has already been built. Please select a different branch.", "info")}
                            }}
                            className="w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                          />
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            <a
                              href={repo.path}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`font-weight-medium ${ knowledgeDoesNotExist ? 'text-primary hover:text-primary-800' : 'text-gray-400'}`}
                            >
                              {repo.name}
                            </a>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <p className={`typography-body-sm ${knowledgeDoesNotExist ? 'text-gray-600' : 'text-gray-400'} line-clamp-2`}>
                            {repo.description || 'No description available'}
                          </p>
                        </td>
                        <td className="py-4 px-4">
                          <BranchSelect 
                            repo={repo} 
                            index={index}
                            //isSelectable={isSelectable}
                            onBranchSelect={(branchName) => {
                                onUpdateRepository(index, { branch: branchName });
                            }}
                            onUpdateRepository={onUpdateRepository}
                            isActive={activeDropdown === index}
                            onDropdownToggle={(idx) => {
                              setActiveDropdown(activeDropdown === idx ? null : idx);
                            }}
                            onDropdownClose={() => {
                              setActiveDropdown(null);
                            }}
                            tableRef={tableRef}
                            activeTabVal={activeTabVal? activeTabVal : ''}
                            organization_name={organization_name}
                          />
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table> 
            )}
          </div>
        </div>
        <div className="absolute w-full bottom-0  bg-white border-t">
          <ProjectAssetPagination
            currentPage={currentPage}
            pageCount={Math.ceil((filteredRepositories.length) / pageSize)}
            pageSize={pageSize}
            totalItems={filteredRepositories.length}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
            pageSizeOptions={[5, 10, 15, 20]}
          />
        </div>
      </div>
    </div>
  );
};

export default RepositoryListTable;