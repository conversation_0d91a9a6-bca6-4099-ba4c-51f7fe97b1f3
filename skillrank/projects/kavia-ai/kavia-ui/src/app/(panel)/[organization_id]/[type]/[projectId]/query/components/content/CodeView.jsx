import React from 'react';

const CodeView = ({ code = DUMMY_CODE, language = 'typescript' }) => {
  return (
    <div className="p-6">
      <div className="bg-semantic-gray-100 p-4 rounded-t-lg border-b border-custom-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="typography-body-sm font-weight-medium text-semantic-gray-700">userController.ts</span>
            <span className="typography-caption text-semantic-gray-500">• TypeScript</span>
          </div>
          <div className="flex items-center space-x-2">
            <button className="typography-caption bg-custom-bg-primary px-3 py-1 rounded border border-custom-border hover:bg-semantic-gray-50">
              Copy
            </button>
            {/* <button className="typography-caption bg-custom-bg-primary px-3 py-1 rounded border border-custom-border hover:bg-semantic-gray-50">
              Download
            </button> */}
          </div>
        </div>
      </div>
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-x-auto">
        <code className={`language-${language}`}>
          {code}
        </code>
      </pre>
    </div>
  );
};

const DUMMY_CODE = `import { Request, Response } from 'express';
import { AuthService } from '../services/AuthService';
import { UserModel } from '../models/UserModel';

export class UserController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  public async authenticate(req: Request, res: Response): Promise<void> {
    try {
      const { email, password } = req.body;
      const token = await this.authService.authenticate(email, password);
      res.json({ token });
    } catch (error) {
      res.status(401).json({ error: 'Authentication failed' });
    }
  }

  public async getUserProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user.id;
      const user = await UserModel.findById(userId);
      res.json(user);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch user profile' });
    }
  }
}`;

export default CodeView;