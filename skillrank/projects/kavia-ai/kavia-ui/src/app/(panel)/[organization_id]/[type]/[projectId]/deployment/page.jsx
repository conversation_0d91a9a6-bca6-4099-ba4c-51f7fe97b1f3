"use client";

import React, { useEffect, useState, useContext } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { fetchSystemContextWithContainers ,getComponentDeployments} from "@/utils/api";
import { buildProjectUrl } from "@/utils/navigationHelpers";
import en from "@/en.json";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { Loading2 } from "@/components/Loaders/Loading";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from "@/components/UIComponents/Buttons/IconButton";
import { ArrowLeft, Box, Layers, Database, Wifi } from "lucide-react";
import { useDeployment } from "@/components/Context/DeploymentContext";

import "@/styles/tabs/architecture/container.css";

const ContainerCard = ({ container, onClick }) => {
  const getIcon = (type) => {
    switch (type.toLowerCase()) {
      case "api":
        return <Wifi className="w-4 h-4" />;
      case "database":
        return <Database className="w-4 h-4" />;
      case "service":
        return <Layers className="w-4 h-4" />;
      default:
        return <Box className="w-4 h-4" />;
    }
  };

  return (
    <div
      onClick={() => onClick(container.id)}
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary-50 rounded-lg">
            {getIcon(container.type)}
          </div>
          <div>
            <h3 className="project-panel-heading">{container.title}</h3>
            <span className="typography-body-sm text-gray-500">ID: {container.id}</span>
          </div>
        </div>
        <Badge type={container.type} />
      </div>
      <p className="mt-3 text-gray-600 typography-body-sm line-clamp-2">
        {container.description}
      </p>
    </div>
  );
};

const ContainerGrid = ({ containers, onContainerClick }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      {containers.map((container) => (
        <ContainerCard
          key={container.id}
          container={container}
          onClick={onContainerClick}
        />
      ))}
    </div>
  );
};

const ContainerList = () => {
  const [loading, setLoading] = useState(true);
  const [containerList, setContainerList] = useState([]);
  const { setContainerData } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const pathParts = pathname.split("/");
  const organizationId = pathParts[1];
  const type = pathParts[2];
  const projectId = pathParts[3];
  const router = useRouter();
  const [error, setError] = useState(null);
  const [containerId,setContainerId] = useState(null)
  const { setResponse } = useDeployment();


  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchSystemContextWithContainers(projectId);
      sessionStorage.setItem(
        "project name",
        data?.data?.systemContext?.properties?.Title
      );
      setContainerList(data);
      setContainerData(data?.data?.containers);
    } catch (error) {
      
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const handleRowClick = (Id) => {
    router.push(buildProjectUrl(projectId, `deployment/${Id}`, type, organizationId));
    setContainerId(Id)
  };

  useEffect(()=>{
    const fetchComponentDeployment= async ()=>{ 
      try {
        const response = await getComponentDeployments(projectId,containerId)
        setResponse(response)
      }catch (error) {
        
        
      }
    }
    if (projectId && containerId) {
      fetchComponentDeployment();
    }

  },[projectId,containerId])

  const handleBack = () => {
    router.back();
  };

  const HeaderSection = () => (
    <div
      className="flex flex-col w-full space-y-4 top-1 sticky bg-white "
      style={{ zIndex: 5 }}
    >
      <div className="flex flex-col border border-gray-200 ">
        <div className="relative px-2.5 py-1 space-y-2">
          <div className="flex items-center justify-between ">
            <div className="flex items-center gap-3 py-2">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="project-panel-heading">
                  Infrastructure Resources
                </h2>
                <Badge type="Containers List" />
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <Loading2 />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Container List"
        message={en.UnableToLoadContainer}
        onRetry={() => fetchData()}
        panelType="main"
      />
    );
  }

  const transformedContainers =
    containerList?.data?.containers?.map((container) => ({
      id: container.id,
      title: container.properties.Title,
      type: container.properties.Type,
      description: container.properties.Description,
    })) || [];

  return (
    <div className="flex flex-col h-full">
      <HeaderSection />
      <div className="flex-1 overflow-auto">
        {transformedContainers.length > 0 ? (
          <ContainerGrid
            containers={transformedContainers}
            onContainerClick={handleRowClick}
          />
        ) : (
          <div className="flex justify-center items-center h-full">
            <EmptyStateView type="containers" />
          </div>
        )}
      </div>
    </div>
  );
};

export default ContainerList;


// "use client";
// import React from "react";
// // import TabbedInterface from "./Main/main";
// import EmptyStateView from "@/components/Modal/EmptyStateModal";

// const Page = () => {
//   return (
//     <>
//       <div className="text-center flex justify-center h-96 items-center ">
//         <EmptyStateView type="comingSoon" />
//       </div>
//     </>
//   );
// };

// export default Page;