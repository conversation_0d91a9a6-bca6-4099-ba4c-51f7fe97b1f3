"use client";
import React from "react";
import "@/styles/tabs/requirements.css"
import { useState, useEffect, useContext, useRef } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import {
  getRequirementById,
  getAvailablePriorities,
  updateNodeByPriority,
  getTopLevelRequirements,
  listAllUsers
} from "@/utils/api";

import { getRelatedNodes } from "@/utils/nodeRoute";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { TabContext } from "@/components/Context/TabContext";
import ConfigureModal from "@/components/Modal/ConfigureModel";
import CreateTaskModal from "@/components/Modal/CreateTaskModel";
import BreadCrumbModel from "@/components/Modal/BreadCrumbModel";
import ErrorView from "@/components/Modal/ErrorViewModal";
import en from "@/en.json"

import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import CustomDropdown from '@/components/UIComponents/Dropdowns/CustomDropdown';
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import {
  BookOpen,
  Plus,
  Settings,
  ArrowLeft,
  ArrowRight,
  X,
} from "lucide-react";
import ChildWorkItemTable from "@/components/BrowsePanel/ChildWorkItemTable";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import RequirementCard from '@/components/UIComponents/DetailCards/DetailCard';
import { CardLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import TestCaseTable from "@/components/TestCaseTable/TestCaseTable";
import TestCaseDetailModal from "@/components/TestCaseTable/TestCaseDetailModal";

const Page = ({ params }) => {
  const [nodeLoading, setNodeLoading] = useState(true);
  const [parents, setParents] = useState([])
  const [nodeDetails, setNodeDetails] = useState({
    title: "",
    description: "",
    type: "",
    assignee_email: "",
    assignee_name: "",
    assignee_id: "",
    assigned_at: "",
  });

  const [isShowTab, setIsShowTab] = useContext(TabContext);
  const { showAlert } = useContext(AlertContext);
  const [refresh, setRefresh] = useState(false);
  const [showUsersToAssign, setShowUsersToAssign] = useState(false);
  const [_listAllUsers, _setListAllUsers] = useState(false);
  const pathname = usePathname();
  const type = params.requirementType;
  const id = params.requirementId;
  const projectId = pathname.split("/")[3];
  const [priorityMap, setPriorityMap] = useState({});
  const [isDropdown, setIsDropdown] = useState(false);
  const [priority, setPriority] = useState([]);
  const [isNodeType, setNodeType] = useState(null);
  const [configureNodeId, setConfigureNodeId] = useState(null);
  const [configureModel, setConfigureModel] = useState(false);
  const [isKebabDropdownOpen, setIsKebabDropdownOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [requirementId, setIsRequirementId] = useState(null);
  const [requirementRootId, setRequirementRootId] = useState(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [topLevelData, setTopLevelData] = useState([]);
  const [uniqueData, setUniqueData] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(null);
  const [currentId, setCurrentId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState("");
  const [childRequirements, setChildRequirements] = useState([])
  const [error, setError] = useState(null);
  const [testCases, setTestCases] = useState([]);
  const [testCasesLoading, setTestCasesLoading] = useState(false);
  const [selectedTestCase, setSelectedTestCase] = useState(null);
  const [testCasePage, setTestCasePage] = useState(1);
  const [testCasePageSize, setTestCasePageSize] = useState(5);
  const openModal = () => setIsModalOpen(true);
  const closeModalFun = () => setIsModalOpen(false);

  const modalRef = useRef(null);
  useEffect(() => {
    const fetchAllData = async () => {
      const list = await listAllUsers();
      _setListAllUsers(list);
      gettop();
      handleItemClick(type, id);
      if (type === "UserStory") {
        fetchTestCases(id);
      }
    };

    fetchAllData();
  }, []);

  const gettop = async () => {
    const data = await getTopLevelRequirements(projectId, type);


    const uniqueData = removeDuplicatesById(data);
    setTopLevelData(data);
    setUniqueData(uniqueData);
    const numId = parseInt(id, 10);
    const index = searchCurrentIndex(numId, uniqueData);
    setCurrentIndex(index);
  };

  useEffect(() => {
    if (currentIndex !== null && uniqueData.length > 0) {
      const idFromIndex = getIdFromIndex(currentIndex, uniqueData);
      setCurrentId(idFromIndex);
    }
  }, [currentIndex, uniqueData]);

  useEffect(() => {

  }, [uniqueData]);



  const fetchRequirementOnUpdate = async () => {
    if (nodeDetails && nodeDetails.id) {
      try {
        const response = await getRequirementById(nodeDetails.id, projectId);
        setNodeDetails(response); // Update the requirement state
      } catch (error) {

        setError(error)
        navigate('/error-page');
      }
    }
  };



  useEffect(() => {
    fetchRequirementOnUpdate();
  }, [projectId])

  useEffect(() => {


  }, [currentIndex, currentId]);

  const removeDuplicatesById = (data) => {
    const uniqueData = [];
    const idSet = new Set();

    data.forEach((item) => {
      if (!idSet.has(item.id)) {
        idSet.add(item.id);
        uniqueData.push(item);
      }
    });

    return uniqueData;
  };




  const searchCurrentIndex = (id, uniqueData) => {
    return uniqueData.findIndex((item) => item.id === id);
  };

  const getIdFromIndex = (currentIndex, uniqueData) => {
    if (currentIndex >= 0 && currentIndex < uniqueData.length) {
      return uniqueData[currentIndex].id;
    } else {
      return null;
    }
  };

  const handleItemClick = async (type, id) => {
    setSelectedItem({ type, id });
    setNodeLoading(true);
    try {
      const response = await getRequirementById(id, projectId);


      setNodeDetails(response);
      setIsRequirementId(response.id);
      setParents(response.parents);
    } catch (error) {

    } finally {
      setNodeLoading(false);
    }
  };

  const chevronHandler = (currentIndex, uniqueData) => {
    const cId = getIdFromIndex(currentIndex, uniqueData);

    if (!cId || cId == null) {
      return;
    } else {
      const pathSegments = pathname.split("/");

      const basePathSegments = pathSegments.slice(0, 5);
      const basePath = basePathSegments.join("/");
      router.replace(`${basePath}/${type}/${cId}`);
    }
  };




  //  to fetch assigned priority
  useEffect(() => {
    const fetchPriority = async () => {
      const response = await getAvailablePriorities();
      setPriority(response);
    };

    fetchPriority();
  }, []);

  // to update Priority

  const updatePriority = async (priority, nodeId, e) => {
    e.stopPropagation();
    const propertyName = "Priority";
    try {
      const response = await updateNodeByPriority(
        nodeId,
        propertyName,
        priority
      );
      setNodeDetails((prevDetails) => {

        // Update the 'Priority' field within 'properties'
        const updatedDetails = {
          ...prevDetails,
          properties: {
            ...prevDetails.properties,
            Priority: priority,
          },
        };
        return updatedDetails;
      });
      setPriorityMap((prevPriorityMap) => ({
        ...prevPriorityMap,
        [nodeId]: priority,
      }));
      setSelectedPriority(priority)
      setIsDropdown(false);
      showAlert("Priority Updated Successfully", "success");
    } catch (error) {

      showAlert("Updating Priority Failed", "danger");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setIsDropdown]);


  const handleBackClick = () => {

    setSelectedItem(null);
    // setNodeDetails(null);
    router.replace(`/project/${projectId}/requirements/`);
  };

  const handleConfigureClick = (id, type) => {
    if (type === "RequirementRoot") {
      setNodeType("RequirementRoot");
      setConfigureNodeId(requirementRootId);
      setConfigureModel(true);
    } else {

      setNodeType(type);
      setConfigureNodeId(id);
      setConfigureModel(true);
    }
  };
  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleUpdateWorkitem = (id, type) => {
    if (!id) {
      return null;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", id);
    newSearchParams.set("node_type", type);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };
  const handleChevronDownClick = (e) => {
    e.stopPropagation();
    setIsDropdown(!isDropdown);
  };

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('view_past_discussions', 'true')
    newSearchParams.set("node_id", id);
    newSearchParams.set('discussion_type', `${nodeDetails.type}`);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  // Add this handler near your other handlers
  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(nodeDetails.id, key, value);

      if (response === "success") {
        // Update local state
        setNodeDetails(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));
        showAlert("Content updated successfully", "success");
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {

      showAlert("Failed to update content", "error");
    }
  };


  const updateNodeDetails = (newDetails) => {
    setNodeDetails((prevDetails) => {
      const updatedDetails = {
        ...prevDetails,
        ...newDetails,
      };

      return updatedDetails;
    });
  };

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Requirements"
        message={en.UnableToLoadRequirements}
        onRetry={() => fetchRequirementOnUpdate()}
        panelType='main'
      />
    );
  }

  const updateChildRequirements = (newTopLevelRequirements) => {
    setChildRequirements(newTopLevelRequirements)

  }
  // To get the appropriate type
  let taskType = ""
  if (type === "Epic") {
    taskType = "UserStory";
  } else if (type === "UserStory") {
    taskType = "Task";
  }

  const menuOptions = [
    {
      label: "History",
      icon: BookOpen,
      onClick: handleViewPastDiscussion,
      tooltip: TOOLTIP_CONTENT.WorkItems.viewPastDiscussion,

    },
  ]

  // Add a function to fetch test cases
  const fetchTestCases = async (nodeId) => {
    setTestCasesLoading(true);
    try {
      const response = await getRelatedNodes(nodeId, "UserStory", "VERIFIES");
      setTestCases(response || []);
    } catch (error) {

      showAlert("Failed to load test cases", "danger");
    } finally {
      setTestCasesLoading(false);
    }
  };

  // Function to handle clicking on a test case
  const handleTestCaseClick = (testCase) => {
    setSelectedTestCase(testCase);
  };

  // Function to close the test case detail modal
  const closeTestCaseModal = () => {
    setSelectedTestCase(null);
  };

  useEffect(() => {
    setIsShowTab(!selectedItem);
  }, [selectedItem]);


  return (
    <div>
      <div
        onMouseLeave={() => setIsKebabDropdownOpen(false)}
        className="requirementDetailsTabHeaderDiv"
      >
        <div className="arrowIconsDiv">
          <IconButton
            icon={<X strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip="Go to requirements table"
            onClick={handleBackClick}
            className="hover:bg-gray-100"
            variant="small"
          />
          <div className="w-px bg-gray-100 mx-2" />
          <IconButton
            icon={<ArrowLeft strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip={`Go to previous ${type}`}
            onClick={() => {
              chevronHandler(currentIndex - 1, uniqueData);
            }}
            className="hover:bg-gray-100"
            variant="small"
          />
          <IconButton
            icon={<ArrowRight strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip={`Go to next ${type}`}
            onClick={() => {
              chevronHandler(currentIndex + 1, uniqueData);
            }}
            className="hover:bg-gray-100"
            variant="small"
          />
        </div>
        <div className="headerTabButtons">
          {nodeDetails.type !== "Task" && nodeDetails.type !== null && (
            <DynamicButton
              variant="square"
              size="sqDefault"
              tooltip={`Create ${taskType}`}
              placement="bottom-end"
              icon={Plus}
              onClick={openModal}
            />
          )}
            {pathname.split('/')[5] === "UserStory" && (
            <DynamicButton
            variant="primary"
            icon={Settings}
            text={`Create Test Case`}
            onClick={() => {
              const newSearchParams = new URLSearchParams(searchParams);
              newSearchParams.set("discussion", "new");
              newSearchParams.set("node_id", nodeDetails.id);
              newSearchParams.set("node_type", "UserStory");
              newSearchParams.set("discussion_type", "testcase_generation");
              router.push(`${pathname}?${newSearchParams.toString()}`);
            }}
          />
          )}
          <DynamicButton
            variant="primary"
            icon={Settings}
            text={`Update ${pathname.split('/')[5]}`}
            onClick={() => {
              handleUpdateWorkitem(
                nodeDetails.id,
                nodeDetails.type || "Requirement"
              );
            }}
          />
          {nodeDetails.type !== "Task" && (
            <DynamicButton
              variant="primary"
              icon={Plus}
              text="Auto Configure"
              isLoading={loadingAutoConfigure}
              onClick={() => handleConfigureClick(nodeDetails.id, nodeDetails.type)}
            />
          )}
          <CustomDropdown options={menuOptions} align="right" />

        </div>
      </div>

      {nodeLoading ? (
        <CardLoadingSkeleton />
      ) : (
        <div>
          <div className="pl-3 mt-3 ml-4">
            <BreadCrumbModel projectId={projectId} parents={parents} titleWidth={5} />
          </div>
          <div className="mainContentDetailsWrapper pr-4">
            <div className="space-y-4">
              <RequirementCard
                nodeDetails={nodeDetails}
                onAssignUser={() => setShowUsersToAssign(true)}
                updatePriority={updatePriority}
                listAllUsers={_listAllUsers}
                refresh={refresh}
                setRefresh={setRefresh}
                updateNodeDetails={updateNodeDetails}
                onKeyUpdate={handlePropertyUpdate}
              />

              <div className="">
                {nodeDetails?.ui_metadata && (
                  <PropertiesRenderer
                    properties={nodeDetails.properties}
                    metadata={nodeDetails.ui_metadata}
                    to_skip={['Assignee', 'Priority', 'Type', 'Title', 'Description', 'DueDate', 'StoryPoints', 'UserStoryType', 'AssignedTo']}
                    onUpdate={handlePropertyUpdate}
                  />
                )}
              </div>

              {/* Test Cases Section */}
              {pathname.split('/')[5] === "UserStory" && (
                <div className="mt-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="typography-body-lg font-weight-medium text-gray-800">Test Cases</h3>
                    {testCasesLoading ? (
                      <div className="px-4 py-2 typography-body-sm text-gray-600">Loading...</div>
                    ) : testCases.length > 0 ? (
                      <div className="px-4 py-2 typography-body-sm rounded-md bg-primary-50 text-primary-700">
                        {testCases.length} test cases found
                      </div>
                    ) : null}
                  </div>

                  {testCasesLoading ? (
                    <div className="h-40 flex items-center justify-center">
                      <CardLoadingSkeleton />
                    </div>
                  ) : testCases.length > 0 ? (
                    <TestCaseTable
                      testCases={testCases}
                      onItemClick={handleTestCaseClick}
                      currentPage={testCasePage}
                      pageSize={testCasePageSize}
                      onPageChange={setTestCasePage}
                      onPageSizeChange={setTestCasePageSize}
                    />
                  ) : (
                    <div className="border rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50">
                      <p className="text-gray-500 mb-4">No test cases are associated with this user story</p>
                      <DynamicButton
                        variant="primary"
                        icon={Plus}
                        text="Create Test Case"
                        onClick={() => {
                          const newSearchParams = new URLSearchParams(searchParams);
                          newSearchParams.set("discussion", "new");
                          newSearchParams.set("node_id", nodeDetails.id);
                          newSearchParams.set("node_type", "UserStory");
                          newSearchParams.set("discussion_type", "testcase_generation");
                          router.push(`${pathname}?${newSearchParams.toString()}`);
                        }}
                      />
                    </div>
                  )}
                </div>
              )}

              <ChildWorkItemTable childRequirements={childRequirements} />
            </div>
          </div>
        </div>
      )}

      {selectedTestCase && (
        <TestCaseDetailModal
          testCase={selectedTestCase}
          onClose={closeTestCaseModal}
        />
      )}

      {configureModel && (
        <ConfigureModal
          id={id}
          type={pathname.split('/')[4].toLowerCase()}
          isNodeType={isNodeType}
          requirementRootId={requirementRootId ? requirementRootId : undefined}
          requirementId={configureNodeId}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            const successMessage =
              isNodeType === "RequirementRoot"
                ? "RequirementRoot Configured Successfully"
                : "Node Configured Successfully";
            showAlert(successMessage, "success");
          }}
        />
      )}

      <CreateTaskModal
        isOpen={isModalOpen}
        onClose={closeModalFun}
        id={id}
        projectId={projectId}
        updateChildRequirements={updateChildRequirements}
        type={nodeDetails?.type}
      />
    </div>
  );
};

export default Page;
