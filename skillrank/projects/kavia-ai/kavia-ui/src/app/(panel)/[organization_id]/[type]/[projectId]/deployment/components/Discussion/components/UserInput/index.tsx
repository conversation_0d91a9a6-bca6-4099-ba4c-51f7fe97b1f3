
// deployment/components/Discussion/components/UserInput/index.tsx
//@ts-nocheck
'use client';

import React, { useState } from 'react';

interface FormSchema {
  basicInfo: {
    [key: string]: {
      type: string;
      label: string;
      required: boolean;
      placeholder: string;
      validation?: {
        minLength?: number;
        maxLength?: number;
      }
    }
  }
}

const formSchema: FormSchema = {
  basicInfo: {
    projectName: {
      type: 'string',
      label: 'Project Name',
      required: true,
      placeholder: 'Enter project name',
      validation: {
        minLength: 3,
        maxLength: 50
      }
    }
  }
};

const UserInputPanel = () => {
  const [formData, setFormData] = useState<{ [key: string]: string }>({
    projectName: ''
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateField = (name: string, value: string) => {
    const field = formSchema.basicInfo[name];
    if (field.required && !value) {
      return `${field.label} is required`;
    }
    if (field.validation?.minLength && value.length < field.validation.minLength) {
      return `${field.label} must be at least ${field.validation.minLength} characters`;
    }
    if (field.validation?.maxLength && value.length > field.validation.maxLength) {
      return `${field.label} must be less than ${field.validation.maxLength} characters`;
    }
    return '';
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors: { [key: string]: string } = {};
    Object.keys(formSchema.basicInfo).forEach(fieldName => {
      const error = validateField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // If validation passes, log the form data
    
  };

  return (
    <div className="p-6 flex justify-center"> {/* Added flex and justify-center */}
      <div className="w-1/2"> {/* Added container with 50% width */}
        <h2 className="typography-body-lg font-weight-semibold text-[#2A3439] mb-4">
          Infrastructure Configuration
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {Object.entries(formSchema.basicInfo).map(([name, field]) => (
            <div key={name} className="space-y-2">
              <label 
                htmlFor={name}
                className="block typography-body-sm font-weight-medium text-gray-700"
              >
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
              
              <input
                type={field.type}
                id={name}
                name={name}
                value={formData[name]}
                onChange={handleChange}
                placeholder={field.placeholder}
                className={`w-full px-3 py-2 border rounded-md shadow-sm
                  ${errors[name] ? 'border-red-500' : 'border-gray-300'}
                  focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent`}
              />
              
              {errors[name] && (
                <p className="text-red-500 typography-caption mt-1">{errors[name]}</p>
              )}
            </div>
          ))}

         
        </form>
      </div>
    </div>
  );
};

export default UserInputPanel;