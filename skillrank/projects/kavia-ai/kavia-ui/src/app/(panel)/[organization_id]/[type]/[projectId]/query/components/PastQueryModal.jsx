import React, { useState, useContext, useEffect } from "react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { FaTimes } from "react-icons/fa";
import { updateNodeByPriority } from '@/utils/api';
import PastDeepAnalysis from "./PastDeepAnalysis";

import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import EditableCell from "@/components/SimpleTable/EditableCell";

const Spinner = () => (
  <div className="flex justify-center items-center h-64">
    <div className="relative">
      <div className="w-12 h-12 rounded-full border-4 border-gray-200"></div>
      <div className="w-12 h-12 rounded-full border-4 border-t-primary animate-spin absolute top-0 left-0"></div>
    </div>
  </div>
);
const PastQueryModal = ({
  queries,
  onClose,
  handleRowClick,
  onPageChange,
  onLimitChange,
  totalCount,
  isLoading,
  currentPage,
  pastDeepAnalysis,
}) => {
  const [currentTab, setCurrentTab] = useState("code_query");
  const [data, setData] = useState(queries);
  const { showAlert } = useContext(AlertContext);

  const handleUpdate = async (rowId, field, value) => {
    try {
      await updateNodeByPriority(rowId, field, value);
      const updatedData = data.map((item) =>
        item.discussionId === rowId ? { ...item, [field]: value } : item
      );
      setData(updatedData);
      showAlert("Update successful", "success");
    } catch (error) {
      showAlert("Failed to update", "error");
    }
  };

  useEffect(() => {
    setData(queries);
  }, [queries]);

  const headers = [
    {
      key: "session_name",
      label: "Session Name",
      render: (value, row) => (
        <EditableCell
          value={value || "Untitled"}
          field="session_name"
          rowId={row.discussionId}
          onUpdate={handleUpdate}
        />
      ),
    },
    {
      key: "username",
      label: "created by",
    },
    { key: "timestamp", label: "Last Updated" },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-[70vw]  flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-100 flex justify-between items-center bg-gray-50 rounded-t-lg">
          <h2 className="typography-body-lg font-weight-semibold">Past Queries</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
            <FaTimes size={18} />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex px-4 pt-2 border-b bg-gray-50">
          {["code_query", "deep_analysis"].map((tab) => (
            <div
              key={tab}
              className={`px-4 py-2 cursor-pointer rounded-t-md typography-body-sm font-weight-medium ${
                currentTab === tab
                  ? "border-x border-t bg-white border-gray-100"
                  : "hover:bg-gray-100 text-gray-500"
              }`}
              onClick={() => setCurrentTab(tab)}
            >
              {tab === "code_query" ? "Code Query" : "Deep Analysis"}
            </div>
          ))}
        </div>

        {/* Tab Content */}
        <div className="flex-grow overflow-y-auto p-4">
          {currentTab === "code_query" ? (
            <div className="max-h-[70vh] overflow-auto">
              <TableComponent
                totalCount={totalCount}
                data={data}
                headers={headers}
                sortableColumns={{ id: true, title: true, timestamp: true }}
                onRowClick={handleRowClick}
                onPageChange={onPageChange}
                onPageSizeChange={onLimitChange}
                isLoading={isLoading}
                customPage={currentPage}
                component="query"
                emptyStateType="query_sessions"
              />
            </div>
          ) : (
            <div className="max-h-[70vh] overflow-auto">
              <PastDeepAnalysis
                onClose={onClose}
                pastAnalysis={pastDeepAnalysis.pastAnalysis}
                onPageChange={pastDeepAnalysis.onPageChange}
                onLimitChange={onLimitChange}
                isLoading={pastDeepAnalysis.isLoading}
                setPastAnalysis={pastDeepAnalysis.setPastAnalysis}
                pageSize={pastDeepAnalysis.limit}
                totalCount={pastDeepAnalysis.totalCount}
                customPage={pastDeepAnalysis.currentPage}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PastQueryModal;
