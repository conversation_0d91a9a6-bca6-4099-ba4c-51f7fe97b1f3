"use client";

import React, { useContext, useEffect, useState } from "react";
import "@/styles/tabs/architecture/architectureRequirement.css"
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { getArchitecturalRequirementWithChildren } from "@/utils/api";
import en from "@/en.json";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import TableComponent from "@/components/SimpleTable/ArchitectureTable"
import { BookOpen } from 'lucide-react';
import Badge from "@/components/UIComponents/Badge/Badge"
import ErrorView from "@/components/Modal/ErrorViewModal";
import { updateNodeByPriority,getReconfigNodeStatus } from "@/utils/api";
import NavigationTree from "@/components/Modal/NavigationTreeComponent"
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup"
import { useResponsiveDimensions } from "@/utils/responsiveness";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";

const ArchitectureRequirementsPage = () => {
  const { updateSelectedArchitecturalRequirement } = useContext(ArchitectureContext);
  const [requirementData, setRequirementData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null)
  const [showBaner,setShowBaner] = useState(false)
  const [reconfigCount,setReconfigCount] = useState(0)
  const { showAlert } = useContext(AlertContext);
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { mainContentWidth, contentHeight, calculateDimensions } = useResponsiveDimensions();

  const projectId = pathname.split("/")[3];


  const fetchData = async () => {
    setLoading(true);
    try {
      const data = await getArchitecturalRequirementWithChildren(projectId);
      const reconfig = await getReconfigNodeStatus (projectId)
      let showBanner = false;

    if (reconfig) {
      const hasReconfigNeeded =
      reconfig.FunctionalRequirement?.some(item => item.reconfig_needed === true) ||
      reconfig.NonFunctionalRequirement?.some(item => item.reconfig_needed === true);

      const Count =
      (reconfig.FunctionalRequirement?.filter(item => item.reconfig_needed === true)?.length || 0) +
      (reconfig.NonFunctionalRequirement?.filter(item => item.reconfig_needed === true)?.length || 0);
      setReconfigCount(Count)

      showBanner = hasReconfigNeeded;

    }
    setShowBaner(showBanner)

      setRequirementData(data);
    } catch (error) {

      setError(error)
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    fetchData();
  }, [])

  useEffect(() => {
    calculateDimensions()
  }, [calculateDimensions])

  if (loading) {
    return (
      <TwoColumnSkeletonLoader />
    );
  }

  if (error)
    return (
      <ErrorView
        title="Unable to Load architecture requirements"
        message={en.UnableToLoadArchitectureRequirements}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );

  const handleOnUpdate = () => {
    if (!requirementData || !requirementData.id) return;
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", requirementData.id);
    newSearchParams.set("node_type", "ArchitecturalRequirement");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const configureProps = {
    isConfigurable: false,
    nodeId: requirementData?.id,
    nodeType: "ArchitectureRoot",
    setLoadingAutoConfigure: () => { }, // Implement if needed
    onSubmitSuccess: () => {
      showAlert("Auto configure initiated", "success");
    },
    buttonText: "Auto Configure",
  };

  const updateProps = {
    onUpdateClick: handleOnUpdate,
    buttonText: (requirementData && requirementData.childNodes.length > 0) ? TOOLTIP_CONTENT.Architecture.requirements.update : TOOLTIP_CONTENT.Architecture.requirements.create,
    tooltip: (requirementData && requirementData.childNodes.length > 0) ? "Update the existing requirements" : "Create new requirements",
  };

  const headers = [
    { key: 'title', label: 'Title' },
    { key: 'type', label: 'Type' },
    { key: 'description', label: 'Description' },
  ];

  const tableData = requirementData.childNodes.map(data => ({
    id: data.id,
    title: data.properties.Title,
    type: data.properties.Type,
    description: data.properties.Description
  }))


  const handleRowClick = (
    architecturalRequirementId,
    title,
    description,
    type
  ) => {
    const selectedRequirement = {
      id: architecturalRequirementId,
      title,
      description,
      type,
    };
    updateSelectedArchitecturalRequirement(selectedRequirement);
    router.push(
      `/project/${projectId}/architecture/architecture-requirement/${type}/${architecturalRequirementId}`
    );
  };
  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", requirementData.id);
    newSearchParams.set("discussion_type", "architectural_requirement");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      // Since requirementData.id contains the node ID we need to update
      const response = await updateNodeByPriority(requirementData.id, key, value);

      if (response === "success") {
        // Update local state
        setRequirementData(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));
        showAlert("Content updated successfully", "success");
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {

      showAlert("Failed to update content", "error");
    }
  };

  const treeData = [
    ...Object.entries(requirementData?.ui_metadata || {})
      .filter(
        ([key, value]) =>
          !["Title", "Type"].includes(key) &&
          value.hidden !== true &&
          requirementData.properties?.hasOwnProperty(key)
      )
      .map(([key, value]) => {
        const label = value.Label === key ? value.Label : key;
        const id = label.toLowerCase().replace(/_/g, '-');
        const name = label.replace(/_/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
        return {
          id,
          name,
          children: [],
        };
      }),
    ...(requirementData.childNodes?.length > 0
      ? [
        {
          id: "related-nodes",
          name: "Related Nodes",
          children: [],
        },
      ]
      : []),
  ];


  const handleScrollToSection = (id) => {

    const element = document.getElementById(id);

    const mainContent = document.getElementById("main-content")

    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80,
        behavior: "smooth",
      });
    }
  };


  const HeaderSection = () => {
    const title =
      requirementData.properties?.Title?.replace(
        "Architectural requirements for project",
        ""
      ) || "";

    return (
      <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
        {showBaner? (
          <RequirementsBanner value={`One or more architecture requirements `}/>
        ):null}
        <div className="flex flex-col border border-gray-100">
          <div className="relative px-2 py-2 space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                    {title}
                  </h2>
                  {requirementData?.properties?.Type && (
                    <div className="flex items-center gap-1">
                      <Badge type={requirementData.properties.Type} />
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <ConfigureButtons updateProps={updateProps} configureProps={configureProps} />
                <button
                  onClick={handleViewPastDiscussion}
                  className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                             rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                             focus:ring-offset-2 typography-body-sm"
                >
                  <BookOpen size={14} />
                  History
                </button>
              </div>
            </div>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
          </div>
        </div>
      </div>
    );
  };


  return (
    <div className="relative flex max-h-[78vh] overflow-hidden bg-white-50" style={{
      height: contentHeight
    }}>
      {requirementData ? (
        <>
          <div>
            {treeData && (
              <NavigationTree
                treeData={treeData}
                handleScrollToSection={handleScrollToSection}
              />
            )}

          </div>

          {/* Main Content */}
          <main
            id="main-content"
            className={`
              flex-1
              relative
              overflow-y-auto
              overflow-x-hidden
              transition-all
              duration-300
              ease-in-out
            `}
            style={{
              width: mainContentWidth,
            }}
          >
            <div className="w-full pl-3 pr-4">
              <div className="mb-4">
                <HeaderSection />
              </div>
              {requirementData && (
                <PropertiesRenderer
                  properties={requirementData.properties}
                  metadata={requirementData.ui_metadata}
                  to_skip={["configuration_state", "Type", "Title"]}
                  onUpdate={handlePropertyUpdate}
                />
              )}
              <div id="related-nodes" className="architecture-requirement-related-child-nodes w-full">
                <div
                  className={`${requirementData.childNodes.length > 0
                    ? 'border rounded-lg overflow-hidden'
                    : ''
                    }`}
                >
                  {requirementData.childNodes.length > 0 ? (
                    <TableComponent
                      data={tableData}
                      onRowClick={handleRowClick}
                      headers={headers}
                      sortableColumns={{ "title": true }}
                      itemsPerPage={20}
                      component="architecture-requirements"
                      title="Related Nodes"
                    />
                  ) : (
                    <></>
                  )}
                </div>
              </div>
            </div>
          </main>
        </>
      ) : (
        <p className="text-center text-gray-500 mt-10">
          <EmptyStateView type="architectureRequirementsNotFound" />
        </p>
      )}
    </div>
  );
};

export default ArchitectureRequirementsPage;
