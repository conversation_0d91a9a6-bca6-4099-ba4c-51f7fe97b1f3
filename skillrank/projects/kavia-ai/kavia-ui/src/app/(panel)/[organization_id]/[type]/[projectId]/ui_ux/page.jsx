//src/app/(panel)/[organization_id]/[type]/[projectId]/ui_ux/page.jsx
'use client'
import { useState, useEffect, useContext, useRef, useCallback } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import EmptyStateDesign from '@/components/Figma/EmptyState';
import FigmaDesignFetcher from '@/components/Figma/FigmaDesignFetcher';
import { getFigmaFiles, addFigmaFileV2, getFigmaProcessingStatus, deleteFigmaDesign, reloadFigmaDesign, deleteExtImages } from '@/api/figma';
import { addMoreImages, downloadAllFrames, renameExternalImage, uploadExternalImages, deleteExternalImage } from '@/utils/FigmaAPI';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Plus, ArrowLeft, Trash2, XCircle, Group, Table, Download, RefreshCw, Info, AlertCircle, BookOpen, X, Upload, FileIcon, ImageIcon, Grid, List } from "lucide-react";
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import Pagination from '@/components/BrowsePanel/Pagination';
import { TOOLTIP_CONTENT } from '@/utils/constant/tooltip';
import { useUser } from "@/components/Context/UserContext";

import FigmaExtractButton from '@/components/Figma/FigmaExtractButton';
import { FigmaExtractionContext } from '@/components/Context/FigmaExtractionContext';
import { getPastDiscussions } from '@/utils/FigmaAPI';
import PastFigmaDiscussionsModal from '@/components/modals/PastFigmaDiscussionsModal';
import { formatDateTime } from '@/utils/datetime';
import { BootstrapTooltip } from '@/components/UIComponents/ToolTip/Tooltip-material-ui';
import JSZip from 'jszip';

const Modal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 w-[425px] max-w-[90%] relative">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          <XCircle className="h-5 w-5" />
        </button>
        {children}
      </div>
    </div>
  );
};

const TokenErrorAlert = ({ onClose }) => {
  return (
    // Setting z to at least 1500 because 1300 is the z-index of MUI Dialog component
    <div className="fixed inset-0 bg-gray-800 bg-opacity-75 z-[1500] flex justify-center items-center">
      <div className="bg-white p-5 rounded-md flex flex-col gap-5 max-w-lg">
        <div className="flex items-center gap-2">
          <svg
            id="Layer_1"
            data-name="Layer 1"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 122.88 111.54"
            width="24"
            height="24"
          >
            <defs>
              <style>
                {`.cls-1 { fill: #cf1f25; }
                  .cls-2 { fill: #fec901; fill-rule: evenodd; }
                  .cls-3 { fill: #010101; }`}
              </style>
            </defs>
            <title>warning</title>
            <path
              className="cls-1"
              d="M2.35,84.42,45.28,10.2l.17-.27h0A23,23,0,0,1,52.5,2.69,17,17,0,0,1,61.57,0a16.7,16.7,0,0,1,9.11,2.69,22.79,22.79,0,0,1,7,7.26q.19.32.36.63l42.23,73.34.24.44h0a22.48,22.48,0,0,1,2.37,10.19,17.63,17.63,0,0,1-2.17,8.35,15.94,15.94,0,0,1-6.93,6.6c-.19.1-.39.18-.58.26a21.19,21.19,0,0,1-9.11,1.75v0H17.61c-.22,0-.44,0-.65,0a18.07,18.07,0,0,1-6.2-1.15A16.42,16.42,0,0,1,3,104.24a17.53,17.53,0,0,1-3-9.57,23,23,0,0,1,1.57-8.74,7.66,7.66,0,0,1,.77-1.51Z"
            />
            <path
              className="cls-2"
              d="M9,88.75,52.12,14.16c5.24-8.25,13.54-8.46,18.87,0l42.43,73.69c3.39,6.81,1.71,16-9.33,15.77H17.61C10.35,103.8,5.67,97.43,9,88.75Z"
            />
            <path
              className="cls-3"
              d="M57.57,83.78A5.53,5.53,0,0,1,61,82.2a5.6,5.6,0,0,1,2.4.36,5.7,5.7,0,0,1,2,1.3,5.56,5.56,0,0,1,1.54,5,6.23,6.23,0,0,1-.42,1.35,5.57,5.57,0,0,1-5.22,3.26,5.72,5.72,0,0,1-2.27-.53A5.51,5.51,0,0,1,56.28,90a5.18,5.18,0,0,1-.36-1.27,5.83,5.83,0,0,1-.06-1.31h0a6.53,6.53,0,0,1,.57-2,4.7,4.7,0,0,1,1.14-1.56Zm8.15-10.24c-.19,4.79-8.31,4.8,8.49,0-.82-8.21-2.92-29.34-2.86-37.05.07-2.38,2-3.79,4.56-4.33a12.83,12.83,0,0,1,5,0c2.61.56,4.65,2,4.65,4.44v.24L65.72,73.54Z"
            />
          </svg>
          <h1 className="font-weight-semibold typography-heading-4">Forbidden Access to Figma!</h1>
        </div>

        <p className="text-md bg-red-100 text-red-600 p-4 rounded-md">
          Sorry,We couldn't access your Figma design because the provided access token is invalid or has expired. Please update your Figma access token to continue importing designs.
        </p>

        <p className="text-md bg-primary-100 text-primary p-4 rounded-md flex gap-2 items-start">
          <AlertCircle className="w-5 h-5" />
          Organization Admins are able to update the token in Dashboard &gt; Settings
        </p>

        <DynamicButton
          variant="primary"
          onClick={onClose}
          text="Close"
          className="w-[130px] self-center"
        />
      </div>
    </div>
  );
};

export default function Page() {
  const { setShowFigmaExtactionModal, setFigmaDiscussionId, selectedDesignData } = useContext(FigmaExtractionContext);
  const searchParams = useSearchParams()
  
  const router = useRouter()
  const [figmaDesigns, setFigmaDesigns] = useState([]);
  const [ImageDesigns, setImageDesigns] = useState([]);
  const [paginatedFigmaDesigns, setPaginatedFigmaDesigns] = useState([]);
  const [currentFigmaLink, setCurrentFigmaLink] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [designName, setDesignName] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDesign, setSelectedDesign] = useState(null);
  const { showAlert } = useContext(AlertContext);
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [viewMode, setViewMode] = useState('card');
  const [isDownloading, setIsDownloading] = useState(false);
  const [reloadConfirmation, setReloadConfirmation] = useState(null);
  const [isReloading, setIsReloading] = useState(false);
  const [statusModal, setStatusModal] = useState(null);
  const [processingStatus, setProcessingStatus] = useState(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [processingDesigns, setProcessingDesigns] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isTokenError, setIsTokenError] = useState(false);
  const wsRef = useRef(null);
  const maxPaginatedData = 12;
  const statusModalId = useRef(null); //used to live render the progress in progress modal
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [discussions, setDiscussions] = useState([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [historyLimit, setHistoryLimit] = useState(10);
  const [historySkip, setHistorySkip] = useState(0);
  const [uploadMethod, setUploadMethod] = useState(searchParams.get('type') === 'image' ? 'image' : 'figma');
  const [designType, setDesignType] = useState(searchParams.get('type') === 'image' ? 'image' : 'figma');
  const [images, setImages] = useState([]);
  const [viewModeimg, setViewModeimg] = useState('list');
  const { is_admin, tenant_id, fetchUserData } = useUser();
  const [sessionName, setSessionName] = useState("Untitled");
  const [isSessionModal, setIsSessionModal] = useState(false)
  // Add these state variables
  const [paginatedImageDesigns, setPaginatedImageDesigns] = useState([]);
  const [currentImagePage, setCurrentImagePage] = useState(1);
  const [totalImagePages, setTotalImagePages] = useState(1);
  const [renameModal, setRenameModal] = useState(null);
  const [newName, setNewName] = useState('');
  const [isRenaming, setIsRenaming] = useState(false);
  const [modalType, setModalType] = useState('new'); // 'figma' or 'image'
  const [jsonProcessingStatus, setJsonProcessingStatus] = useState({});
  const [wsConnectionState, setWsConnectionState] = useState('disconnected'); // 'connecting', 'connected', 'disconnected'
  const reconnectTimeoutRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  const isComponentMountedRef = useRef(true);
  const params = useParams();
  const projectId = params.projectId;
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set('type', designType);
    router.push(`?${params.toString()}`, { scroll: false });
  }, [designType]);
  // Add handler for image card click
  const handleImageCardClick = (image) => {
    // Similar to handleCardClick but for images
    if (!['completed', 'partially_completed'].includes(image.status)) {
      showAlert('This image is still processing. Please wait until it completes.', 'warning');
      return;
    }

    setSelectedDesign({
      id: image.id,
      name: image.name,
      figmaUrl: ""
    });

    const params = new URLSearchParams(searchParams);
    params.set('selectedDesignId', image.id);
    params.set('type', "image");
    router.push(`?${params.toString()}`, { scroll: false });
  };

  // Add handler for deleting images
  const handleDeleteImage = async (image, e) => {
    e.stopPropagation();
    // You can reuse the deleteConfirmation modal for this
    setDeleteConfirmation(image);
  };

  // Add this effect for image pagination
  useEffect(() => {
    if (ImageDesigns) {
      if ((currentImagePage - 1) * maxPaginatedData >= ImageDesigns.length) {
        setCurrentImagePage(currentImagePage > 1 ? currentImagePage - 1 : 1);
      }
      else {
        const startingIndex = (currentImagePage > 1 ? currentImagePage - 1 : 0) * maxPaginatedData;
        setPaginatedImageDesigns(ImageDesigns.filter((_, index) =>
          index >= startingIndex && index <= startingIndex + maxPaginatedData - 1
        ))
        setTotalImagePages(Math.ceil(ImageDesigns.length / maxPaginatedData));
      }
    }
  }, [ImageDesigns, maxPaginatedData, currentImagePage]);
  useEffect(() => {
  isComponentMountedRef.current = true;
  
  // Initialize WebSocket connection
  connectWebSocket();

  // Cleanup function
  return () => {
    isComponentMountedRef.current = false;
    
    // Clear timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    // Stop heartbeat
    stopHeartbeat();
    
    // Close WebSocket
    if (wsRef.current) {
      wsRef.current.close();
    }
  };
}, [projectId]);

  // Make sure this effect runs when toggle changes
  useEffect(() => {
    if (designType === 'figma') {
      // Reset image pagination when switching to Figma
      setCurrentImagePage(1);
    } else {
      // Reset figma pagination when switching to Images
      setCurrentPage(1);
    }
  }, [designType]);
  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  

  useEffect(() => {
    fetchProjectData();
  }, [projectId]);



  useEffect(() => {

    if (selectedDesign?.figmaUrl)
      setCurrentFigmaLink(selectedDesign.figmaUrl)
    else
      setCurrentFigmaLink('')
    fetchDiscussions();
  }, [selectedDesign]);

  const fetchDiscussions = useCallback(async (currentSkip = 0, currentLimit = historyLimit) => {
    setIsHistoryLoading(true);
    try {
      const response = await getPastDiscussions(projectId, selectedDesign?.id);
      // Debug log

      // Check if the response has the expected structure
      const discussionsList = response.discussions || response;

      if (!Array.isArray(discussionsList)) {
        throw new Error('Invalid response format');
      }

      // Format the discussions for display using the correct field names
      const formattedData = discussionsList.map(discussion => {
        // Debug log for each discussion


        return {
          discussion_name: `Discussion ${discussion.discussion_id.substring(0, 8)}`,
          created_at: formatDateTime(discussion.discussion_created_at, true),
          message_count: discussion.message_count || 0,
          status: discussion.discussion_status?.toUpperCase() || 'PENDING',
          id: discussion.discussion_id, // Make sure this is not undefined
          fullId: discussion.discussion_id // For compatibility with the table component
        };
      });

      // Debug log

      setDiscussions(formattedData);
      setTotalCount(discussionsList.length);
      setHistorySkip(currentSkip);
      setHistoryLimit(currentLimit);
    } catch (error) {

      showAlert('Failed to fetch past discussions', 'error');
      setDiscussions([]);
    } finally {
      setIsHistoryLoading(false);
    }
  }, [projectId, selectedDesign, historyLimit, showAlert, formatDateTime]);
  const handleHistoryPageChange = (newPage) => {
    const newSkip = (newPage - 1) * historyLimit;
    fetchDiscussions(newSkip, historyLimit);
  };

  const handleHistoryLimitChange = (newLimit) => {
    fetchDiscussions(0, newLimit);
  };


  useEffect(() => {
    // Initialize WebSocket connection
    connectWebSocket();

    // Only cleanup when component unmounts, not on every figmaDesigns change
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [projectId]); // Remove figmaDesigns dependency to prevent unnecessary reconnections

const connectWebSocket = useCallback(() => {
  // Clear any existing reconnection timeout
  if (reconnectTimeoutRef.current) {
    clearTimeout(reconnectTimeoutRef.current);
    reconnectTimeoutRef.current = null;
  }

  // Don't connect if component is unmounted
  if (!isComponentMountedRef.current) {
    return;
  }

  // Check if already connected or connecting
  if (wsRef.current?.readyState === WebSocket.OPEN) {
    console.log('WebSocket already connected');
    setWsConnectionState('connected');
    return;
  }

  if (wsRef.current?.readyState === WebSocket.CONNECTING) {
    console.log('WebSocket already connecting');
    return;
  }

  // Close existing connection if it exists
  if (wsRef.current) {
    wsRef.current.close();
  }

  try {
    console.log('Connecting to WebSocket...');
    setWsConnectionState('connecting');
    
    const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/figma-${projectId}`);
    wsRef.current = ws;

    // Connection timeout
    const connectionTimeout = setTimeout(() => {
      if (ws.readyState === WebSocket.CONNECTING) {
        console.log('WebSocket connection timeout');
        ws.close();
        handleReconnection();
      }
    }, 10000); // 10 seconds timeout

    ws.onopen = () => {
      console.log('WebSocket connected successfully');
      clearTimeout(connectionTimeout);
      setWsConnectionState('connected');
      
      // Send initialization message
      ws.send(JSON.stringify({
        type: "client",
        task_id: `figma-${projectId}`
      }));

      // Start heartbeat to keep connection alive
      // startHeartbeat(ws);
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        // Handle heartbeat response
        if (message.type === 'pong') {
          console.log('Received heartbeat pong');
          return;
        }

        if (message.type === 'figma_update') {
          const { figma_id, update_data } = message.data;
          let updatedDesign = {};

          setFigmaDesigns(prevDesigns => {
            return prevDesigns.map(design => {
              if (design.id === figma_id) {
                updatedDesign = {
                  ...design,
                  ...update_data,
                  completed_frames: update_data.completed_frames ?? design.completed_frames,
                  failed_frames: update_data.failed_frames ?? design.failed_frames,
                  status: update_data.status || design.status,
                  time_updated: update_data.time_updated,
                  sizes: update_data.sizes ?? design.sizes ?? {}
                };
                return updatedDesign;
              }
              return design;
            });
          });

          // Dispatch custom event for FigmaDesignFetcher to listen to
          window.dispatchEvent(new CustomEvent('figma_update', {
            detail: { figma_id, update_data }
          }));

          if (statusModalId.current === figma_id) {
            setProcessingStatus({
              completed_frames: updatedDesign.completed_frames ?? design.completed_frames,
              failed_frames: updatedDesign.failed_frames ?? design.failed_frames,
              total_frames: updatedDesign.total_frames,
              status: updatedDesign.status || design.status,
              time_updated: updatedDesign.time_updated,
            });
          }

          // If status is completed or failed, refresh the design list
          if (update_data.status === 'completed' || update_data.status === 'failed') {
            fetchProjectData();
          }
        }
        else if (message.type === 'figma_update_json') {
          const { figma_id, update_data } = message.data;
          
          setJsonProcessingStatus(prev => ({
            ...prev,
            [figma_id]: {
              processed_count: update_data.processed_count,
              total_count: update_data.total_count,
              percentage: update_data.percentage,
              time_updated: update_data.time_updated
            }
          }));
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      clearTimeout(connectionTimeout);
      setWsConnectionState('disconnected');
    };

    ws.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      clearTimeout(connectionTimeout);
      setWsConnectionState('disconnected');
      stopHeartbeat();
      
      // Only attempt to reconnect if the closure wasn't intentional and component is still mounted
      if (isComponentMountedRef.current && event.code !== 1000) {
        handleReconnection();
      }
    };

  } catch (error) {
    console.error('Error creating WebSocket connection:', error);
    setWsConnectionState('disconnected');
    handleReconnection();
  }
}, [projectId, figmaDesigns]);

// Heartbeat mechanism to keep connection alive
// const startHeartbeat = (ws) => {
//   stopHeartbeat(); // Clear any existing heartbeat
  
//   heartbeatIntervalRef.current = setInterval(() => {
//     if (ws.readyState === WebSocket.OPEN) {
//       ws.send(JSON.stringify({ type: 'ping' }));
//     } else {
//       stopHeartbeat();
//     }
//   }, 30000); // Send ping every 30 seconds
// };

const stopHeartbeat = () => {
  if (heartbeatIntervalRef.current) {
    clearInterval(heartbeatIntervalRef.current);
    heartbeatIntervalRef.current = null;
  }
};

// Handle reconnection logic
const handleReconnection = useCallback(() => {
  if (!isComponentMountedRef.current) {
    return;
  }

  // Clear any existing timeout
  if (reconnectTimeoutRef.current) {
    clearTimeout(reconnectTimeoutRef.current);
  }

  // Check if there are pending/processing designs that need updates
  const hasActiveDesigns = figmaDesigns.some(design => 
    ['pending', 'processing', 'processing_wait'].includes(design.status)
  );

  if (hasActiveDesigns) {
    console.log('Scheduling WebSocket reconnection in 3 seconds...');
    reconnectTimeoutRef.current = setTimeout(() => {
      connectWebSocket();
    }, 3000);
  } else {
    console.log('No active designs, skipping reconnection');
  }
}, [figmaDesigns, connectWebSocket]);

// Function to ensure WebSocket is connected (call this before adding designs)
const ensureWebSocketConnection = useCallback(() => {
  return new Promise((resolve) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      resolve(true);
      return;
    }
    else{
    connectWebSocket();
    }
    console.log('WebSocket not connected, establishing connection...');
    
    
    
    // Wait for connection or timeout
    const checkConnection = () => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        console.log('WebSocket connection established');
        resolve(true);
      } else if (wsConnectionState === 'disconnected') {
        console.log('WebSocket connection failed');
        resolve(false);
      } else {
        // Still connecting, check again
        setTimeout(checkConnection, 500);
      }
    };
    
    setTimeout(checkConnection, 500);
  });
}, [connectWebSocket, wsConnectionState]);

  const fetchProjectData = async () => {
    try {
      setIsLoading(true);
      const response = await getFigmaFiles(projectId);
      setFigmaDesigns(response.designs);
      setImageDesigns(response.images);

      // After loading designs, check if we need to select a design from URL
      const selectedDesignId = searchParams.get('selectedDesignId');
      const typeParam = searchParams.get('type') || 'figma';

      if (selectedDesignId && !selectedDesign) {
        let foundDesign = null;

        if (typeParam === 'figma' && response.designs) {
          foundDesign = response.designs.find(design => design.id === selectedDesignId);
          if (foundDesign && foundDesign.status && ['completed', 'partially_completed'].includes(foundDesign.status)) {
            setSelectedDesign({
              id: foundDesign.id,
              name: foundDesign.name,
              figmaUrl: foundDesign.url,
              sizes: foundDesign.sizes
            });
          }
        } else if (typeParam === 'image' && response.images) {
          foundDesign = response.images.find(image => image.figma_ext_id === selectedDesignId);
          if (foundDesign && foundDesign.status && ['completed', 'partially_completed'].includes(foundDesign.status)) {
            setSelectedDesign({
              id: foundDesign.figma_ext_id,
              name: foundDesign.name,
              figmaUrl: ""
            });
          }
        }
      }
    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (figmaDesigns) {
      if ((currentPage - 1) * maxPaginatedData >= figmaDesigns.length) { //checking if current page lies in range after change such as deletion
        setCurrentPage(currentPage > 1 ? currentPage - 1 : 1);
      }
      else {
        const startingIndex = (currentPage > 1 ? currentPage - 1 : 0) * maxPaginatedData;
        setPaginatedFigmaDesigns(figmaDesigns.filter((_, index) =>
          index >= startingIndex && index <= startingIndex + maxPaginatedData - 1
        ))
        setTotalPages(Math.ceil(figmaDesigns.length / maxPaginatedData));
      }
    }
  }, [figmaDesigns, maxPaginatedData, currentPage]);

  const resetForm = () => {
    setDesignName('');
    setCurrentFigmaLink('');
    setImages([]);
    setUploadMethod(designType == 'figma' ? 'figma' : 'image');
    setIsModalOpen(false)
  };
  const removeImage = (id) => {
    setImages(images.filter(image => image.id !== id));
  };


  // Handle multiple image file selection
  const handleImageChange = (e) => {
    const selectedFiles = Array.from(e.target.files);
    processFiles(selectedFiles);
  };

  const totalSize = images.reduce((total, img) => total + (img.file?.size || 0), 0);
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };
  // Process files (used by both input change and drop events)
  const processFiles = (files) => {
    if (files.length > 0) {
      const newImageEntries = Array.from(files).map(file => {
        // Create a preview URL for each selected image
        const fileReader = new FileReader();
        const imageEntry = {
          file,
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          name: file.name,
          size: file.size,
          type: file.type
        };

        fileReader.onload = () => {
          setImages(prevImages =>
            prevImages.map(img =>
              img.id === imageEntry.id ? { ...img, preview: fileReader.result } : img
            )
          );
        };
        fileReader.readAsDataURL(file);

        return imageEntry;
      });

      setImages(prevImages => [...prevImages, ...newImageEntries]);
    }
  };

const handleSubmit = async (e) => {
  e.preventDefault();
  setIsUpdating(true);

  try {
    // Ensure WebSocket connection before submitting
    if (uploadMethod === 'figma' || modalType === 'figma') {
      console.log('Ensuring WebSocket connection before adding Figma design...');
      await ensureWebSocketConnection();
    }

    if (modalType === "new") {
      if (uploadMethod === 'image') {
        // Validate files
        if (images.length === 0) {
          showAlert('Please select at least one image to upload', 'warning');
          setIsUpdating(false);
          return;
        }

        // Extract actual File objects
        const files = images.map(img => img.file);

        // Upload the files
        const result = await uploadExternalImages(
          projectId,
          designName,
          files
        );

        showAlert(`Successfully uploaded ${result.results.length} images`, 'success');
        await fetchProjectData();
        resetForm();
      } else {
        // Figma URL upload code
        await handleUpdateFigmaLink(e);
      }
    } else {
      await addMoreImages(projectId, selectedDesign.id, images.map(img => img.file));
      await fetchProjectData();
      resetForm();
    }
  } catch (error) {
    console.error('Error in handleSubmit:', error);
    showAlert(error.message || 'Failed to upload', 'error');
  } finally {
    setIsUpdating(false);
  }
};

const handleUpdateFigmaLink = async (e) => {
  e.preventDefault();
  setIsUpdating(true);
  
  try {
    // Ensure WebSocket connection before processing
    console.log('Ensuring WebSocket connection for Figma processing...');
    await ensureWebSocketConnection();
    
    const response = await addFigmaFileV2(projectId, designName, currentFigmaLink);

    if (response.status === 'pending') {
      showAlert('Figma file processing started', 'info');
      await fetchProjectData();
      setIsModalOpen(false);
      setDesignName('');
      setCurrentFigmaLink('');
    } else {
      showAlert(response.message || 'Failed to start processing', 'error');
    }
  } catch (error) {
    console.error('Error in handleUpdateFigmaLink:', error);
    if (error.message && error.message.includes('403 Forbidden')) {
      setIsModalOpen(false);
      setIsTokenError(true);
    } else {
      showAlert(error.message || 'Failed to add Figma design', 'error');
    }
  } finally {
    setIsUpdating(false);
  }
};

  const handleCardClick = (design) => {
    // Check if design status is valid for selection
    if (!['completed', 'partially_completed'].includes(design.status)) {
      showAlert('This design is still processing. Please wait until it completes.', 'warning');
      return;
    }

    setSelectedDesign({
      id: design.id,
      name: design.name,
      figmaUrl: design.url,
      sizes: design.sizes
    });

    // Sample design data with frames
    const params = new URLSearchParams(searchParams);
    // Add or update the figmaDiscussion parameter
    params.set('selectedDesignId', design.id);
    params.set('type', 'figma');
    // Update the URL with the new search params
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleDeleteDesign = async (design, e) => {
    e.stopPropagation();
    setDeleteConfirmation(design);
  };

  const handleDeleteImageFrame = (frame) => {
    setDeleteConfirmation({
      ...frame,
      type: 'frame'  // Add type to distinguish between design and frame deletion
    });
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      if (deleteConfirmation.type === 'frame') {
        await deleteExternalImage(selectedDesign.id, deleteConfirmation.id);
        showAlert('Image frame deleted successfully', 'success');
      } else if (designType === 'image') {
        await deleteExtImages(deleteConfirmation.id);
      } else {
        await deleteFigmaDesign(projectId, deleteConfirmation.id);
      }
      await fetchProjectData();
      showAlert('Deleted successfully', 'success');
      setDeleteConfirmation(null);
      if (selectedDesign?.id === deleteConfirmation.id) {
        setSelectedDesign(null);
      }

    } catch (error) {

      showAlert(error.message || 'Failed to delete', 'error');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleUpdateFigmaDesign = async () => {
    setIsUpdating(true);
    try {
      const response = await reloadFigmaDesign(projectId, selectedDesign.id);

      // If we get a successful response, show message
      if (response.status === 'pending') {
        showAlert('Design update started', 'info');
        setIsUpdating(false);  // Reset loading state after initial response

        // Start polling for status
        const checkStatus = async () => {
          try {
            const status = await getFigmaProcessingStatus(projectId, response.id);
            if (status.status === 'completed') {
              await fetchProjectData();
              showAlert('Figma design updated successfully', 'success');
              setSelectedDesign({
                ...selectedDesign,
                timestamp: Date.now()
              });
            } else if (status.status === 'failed') {
              showAlert(status.error_message || 'Failed to update Figma design', 'error');
            } else if (status.status === 'processing') {
              // Continue polling
              setTimeout(checkStatus, 2000);
            }
          } catch (error) {

            showAlert('Failed to check update status', 'error');
          }
        };

        // Start the polling process
        checkStatus();
      } else {
        showAlert(response.message || 'Failed to start update process', 'error');
        setIsUpdating(false);
      }
    } catch (error) {

      showAlert(error.message || 'Failed to update Figma design', 'error');
      setIsUpdating(false);
    }
  };

  const handleDownloadAllFrames = async () => {
    if (!selectedDesign) {
      showAlert('Please select a design before downloading', 'warning');
      return;
    }

    setIsDownloading(true);
    try {
      if (designType === 'image') {
        const images = selectedDesignData?.frames || [];
        if (images.length === 0) {
          throw new Error('No images available to download');
        }

        // Create new zip file
        const zip = new JSZip();

        // Add images to zip
        for (let i = 0; i < images.length; i++) {
          const image = images[i];
          if (image.imageUrl) {
            // Extract base64 data
            const base64Data = image.imageUrl.split(',')[1];
            if (!base64Data) continue;

            // Add file to zip
            const fileName = image.name || `image-${i + 1}.png`;
            zip.file(fileName, base64Data, { base64: true });
          }
        }

        // Generate zip file
        const blob = await zip.generateAsync({ type: 'blob' });
        const zipFileName = `${selectedDesign.name || 'images'}.zip`;

        // Create download link and trigger download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = zipFileName;
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);

        showAlert('Images downloaded successfully', 'success');
      } else {
        if (!currentFigmaLink) {
          throw new Error('No Figma link available');
        }

        const blob = await downloadAllFrames(currentFigmaLink);
        const filename = selectedDesign.name
          ? `${selectedDesign.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_frames.zip`
          : "figma_export_all_frames.zip";

        // Create download link and trigger download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);

        showAlert('All frames downloaded successfully', 'success');
      }
    } catch (error) {

      showAlert(error.message || 'Failed to download files', 'error');
    } finally {
      setIsDownloading(false);
    }
  };

  const getSizeColor = (sizeKb) => {
    return sizeKb <= 500 ? 'text-success' : 'text-destructive';
  };

  const isDesignSizeValid = (sizeKb) => {
    return sizeKb <= 500;
  };

  const handleReloadDesign = async (design, e) => {
    e.stopPropagation();
    setReloadConfirmation(design);
  };

  const confirmReload = async () => {
    setIsReloading(true);
    try {
      const response = await reloadFigmaDesign(projectId, reloadConfirmation.id);

      // If we get a successful response, show message and close modal
      if (response.status === 'pending') {
        showAlert('Reload process started', 'info');
        setReloadConfirmation(null);
        setIsReloading(false);  // Reset loading state after initial response

        // Start polling for status
        const checkStatus = async () => {
          try {
            const status = await getFigmaProcessingStatus(projectId, response.id);
            if (status.status === 'completed') {
              await fetchProjectData();
              showAlert('Figma design reloaded successfully', 'success');
            } else if (status.status === 'failed') {
              showAlert(status.error_message || 'Failed to reload Figma design', 'error');
            } else if (status.status === 'processing') {
              // Continue polling
              setTimeout(checkStatus, 2000);
            }
          } catch (error) {

            showAlert('Failed to check reload status', 'error');
          }
        };

        // Start the polling process
        checkStatus();
      } else {
        showAlert(response.message || 'Failed to start reload process', 'error');
        setReloadConfirmation(null);
        setIsReloading(false);
      }

    } catch (error) {

      showAlert(error.message || 'Failed to reload Figma design', 'error');
      setReloadConfirmation(null);
      setIsReloading(false);
    }
  };

  const handleCheckStatus = async (design, e) => {
    e.stopPropagation();
    statusModalId.current = design.id;
    setStatusModal(design);
    setIsLoadingStatus(true);
    try {
      const status = await getFigmaProcessingStatus(projectId, design.id);
      setProcessingStatus(status);
    } catch (error) {

      showAlert(error.message || 'Failed to fetch status', 'error');
    } finally {
      setIsLoadingStatus(false);
    }
  };

  const handleDiscussionClick = (row) => {
    let discussionId;
    if (typeof row === 'string') {
      discussionId = row;
    } else if (row && row.id) {
      discussionId = row.id;
    } else {

      showAlert("Error: Invalid discussion selected", "error");
      return;
    }



    // Create new URLSearchParams instance
    const newSearchParams = new URLSearchParams(searchParams);

    // Set the required parameters with the valid discussion ID
    newSearchParams.set('figmaDiscussion', 'existing');
    newSearchParams.set('figmaDiscussionId', discussionId);

    // If there's a selected design, maintain that parameter
    if (selectedDesign?.id) {
      newSearchParams.set('selectedDesignId', selectedDesign.id);
    }

    // Update context state for immediate UI feedback
    setFigmaDiscussionId(discussionId);
    setShowFigmaExtactionModal(true);

    // Close the history modal
    setIsHistoryModalOpen(false);

    // Use router.push for navigation
    router.push(`${window.location.pathname}?${newSearchParams.toString()}`, {
      scroll: false
    });
  };

  const handleRename = (row) => {

    setRenameModal(row);
    setNewName(row.name || row.title || '');
  };

  const confirmRename = async () => {
    setIsRenaming(true);
    try {
      // Add API call here to rename the frame
      const response = await renameExternalImage(selectedDesign.id, renameModal.id, newName);

      showAlert('Frame renamed successfully', 'success');
      await fetchProjectData();
    } catch (error) {

      showAlert(error.message || 'Failed to rename frame', 'error');
    } finally {
      setIsRenaming(false);
      setRenameModal(null);
      setNewName('');
    }
  };

  return (
    <div className="flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side */}
            <div className="flex items-center gap-2">
              {selectedDesign ? (
                <>
                  <BootstrapTooltip title="Back to Design" placement="bottom">
                    <button
                      onClick={() => {
                        setSelectedDesign(null)
                        const params = new URLSearchParams(searchParams);
                        params.delete('selectedDesignId');
                        router.push(`?${params.toString()}`, { scroll: false });
                      }}
                      className="flex items-center text-gray-600 hover:text-gray-900"
                    >
                      <ArrowLeft className="w-5 h-5 mr-1" />
                    </button>
                  </BootstrapTooltip>
                  <span className="typography-body-lg text-gray-800">{selectedDesign.name}</span>
                </>
              ) : (
                <h1 className="typography-body-lg font-weight-semibold text-gray-900">
                  Designs
                </h1>
              )}
            </div>

            {/* Right side */}
            <div className="flex items-center gap-3">
              {selectedDesign ? (
                <div className="flex items-center gap-3">
                  {/* View Controls for selected design */}
                  <div className="flex items-center bg-gray-100 rounded-lg p-1">
                    <button
                      onClick={() => setViewMode('card')}
                      className={`p-2 rounded-md transition-all duration-200 ease-in-out flex items-center gap-2
                  ${viewMode === 'card'
                          ? 'bg-white text-primary shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                        }`}
                      aria-label="Grid view"
                    >
                      <Group size={18} />
                      <span className="typography-body-sm font-weight-medium hidden sm:inline">Grid</span>
                    </button>

                    <button
                      onClick={() => setViewMode('table')}
                      className={`p-2 rounded-md transition-all duration-200 ease-in-out flex items-center gap-2
                  ${viewMode === 'table'
                          ? 'bg-white text-primary shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                        }`}
                      aria-label="List view"
                    >
                      <Table size={18} />
                      <span className="typography-body-sm font-weight-medium hidden sm:inline">List</span>
                    </button>
                  </div>

                  {/* Action Buttons */}
                  {designType === 'figma' && (
                    <DynamicButton
                      type="button"
                      size="default"
                      variant="primary"
                      icon={RefreshCw}
                      onClick={handleUpdateFigmaDesign}
                      text={isUpdating ? 'Updating...' : 'Update Design'}
                      isLoading={isUpdating}
                      disabled={isUpdating}
                    />
                  )}

                  <DynamicButton
                    type="submit"
                    size="default"
                    variant="green"
                    icon={Download}
                    onClick={handleDownloadAllFrames}
                    text={isDownloading ? 'Downloading...' : 'Download All'}
                    isLoading={isDownloading}
                    disabled={isDownloading}
                  />
                  {designType === 'image' && (
                    <DynamicButton
                      type="button"
                      size="default"
                      variant="primary"
                      icon={Plus}
                      onClick={() => {
                        setModalType('image');
                        setIsModalOpen(true);
                      }}
                      text="Add More Images"
                    />
                  )}
                  <div className="flex items-center gap-2">
                    <FigmaExtractButton projectId={projectId} selectedDesignId={selectedDesign?.id} />

                    <DynamicButton
                      type="button"
                      size="default"
                      variant="secondary"
                      icon={BookOpen}
                      onClick={() => {
                        setIsHistoryModalOpen(true);
                        fetchDiscussions();
                      }}
                      text="Discussion History"
                      tooltip="View past Figma discussions"
                      className="whitespace-nowrap"
                    />
                  </div>
                </div>
              ) : (
                <>
                  <DynamicButton
                    type="button"
                    size="default"
                    variant="secondary"
                    icon={RefreshCw}
                    onClick={fetchProjectData}
                    text="Reload Designs"
                    isLoading={isLoading}
                    disabled={isLoading}
                    tooltip={TOOLTIP_CONTENT.Figma.Reload_Designs}
                    className='hidden'
                  />

                  {/* Add Toggle Button before Add button */}
                  <div className="flex items-center bg-gray-100 rounded-lg p-1 mr-2">
                    <button
                      onClick={() => {
                        setDesignType('figma')
                        setUploadMethod('figma')
                      }}
                      className={`px-3 py-1.5 rounded-md transition-all duration-200 ease-in-out
                  ${designType === 'figma'
                          ? 'bg-white text-primary shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                        }`}
                    >
                      Figma
                    </button>
                    <button
                      onClick={() => {
                        setDesignType('image')
                        setUploadMethod('image')
                      }}
                      className={`px-3 py-1.5 rounded-md transition-all duration-200 ease-in-out
                  ${designType === 'image'
                          ? 'bg-white text-primary shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                        }`}
                    >
                      Images
                    </button>
                  </div>

                  <DynamicButton
                    type="button"
                    size="default"
                    variant="primary"
                    icon={Plus}
                    onClick={() => {
                      setIsModalOpen(true)
                      setModalType('new')
                    }}
                    text="Add"
                    tooltip={TOOLTIP_CONTENT.Figma.Create_New}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        {isLoading ? (
          // Loading state
          <div className="p-4 pb-48">
            <div className="UIUXLoadingSkeleton">
              <div className='w-full flex items-center justify-between p-4 border-b border-gray-300 mb-4'>
                {/*sticky header */}
                <div className='w-32 animate-pulse h-6 bg-gray-200 '></div>
                <div className='flex animate-pulse gap-6'>
                  <div className='w-32 h-6  bg-gray-200'></div>
                  <div className='w-32 h-6 bg-gray-200'></div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(9)].map((_, index) => (
                  <div key={index} className="bg-white rounded-lg border border-gray-200 p-4 animate-pulse">
                    <div className="space-y-3">
                      {/* Header section */}
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                          <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                        </div>
                      </div>

                      {/* Status section */}
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="h-2 bg-gray-300 rounded-full w-3/4"></div>
                        </div>
                      </div>

                      {/* Footer section */}
                      <div className="flex justify-between items-center pt-2">
                        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : selectedDesign ? (
          <FigmaDesignFetcher
            figmaId={selectedDesign?.id}
            figmaLink={selectedDesign?.figmaUrl}
            viewMode={viewMode}
            setViewMode={setViewMode}
            isUpdating={isUpdating}
            isDownloading={isDownloading}
            handleUpdateFigmaDesign={handleUpdateFigmaDesign}
            handleDownloadAllFrames={handleDownloadAllFrames}
            selectedDesign={selectedDesign}
            designType={designType}
            onRename={handleRename}
            onDelete={handleDeleteImageFrame}
          />
        ) : (
          <div className="p-4 pb-48 ">
            {/* Display either Figma designs or images based on selected toggle */}
            {designType === 'figma' ? (
              // Figma Designs Display
              paginatedFigmaDesigns.length > 0 ? (
                <div className='h-[65vh] overflow-y-scroll'>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ">
                    {paginatedFigmaDesigns.map((design, index) => {
                      const sizeKb = design.sizes?.size_kb || 0;
                      // const isValidSize = isDesignSizeValid(sizeKb);
                      const completionPercentage = design.total_frames > 0
                        ? Math.round((design.completed_frames / design.total_frames) * 100)
                        : 0;

                      const getStatusColor = (status) => {
                        switch (status) {
                          case 'completed': return 'bg-green-100 text-green-800';
                          case 'processing': return 'bg-primary-100 text-primary-800';
                          case 'partially_completed': return 'bg-yellow-100 text-yellow-800';
                          case 'failed': return 'bg-red-100 text-red-800';
                          default: return 'bg-gray-100 text-gray-800';
                        }
                      };

                      return (
                        <div key={index} className="relative">
                          <div
                            className={`bg-white rounded-lg border border-gray-200 p-4 transition-all h-[180px] flex flex-col justify-between
                        ${['completed', 'partially_completed'].includes(design.status)
                                ? 'hover:shadow-md cursor-pointer'
                                : 'opacity-75 cursor-not-allowed'
                              }`}
                            onClick={() => handleCardClick({
                              id: design.id,
                              name: design.name,
                              url: design.url,
                              sizes: design.sizes,
                              status: design.status
                            })}
                          >
                            <div className="space-y-3 relative">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h3 className="typography-body-lg font-weight-semibold text-gray-900">{design.name}</h3>
                                  <p className="typography-body-sm text-gray-600">{`Added by ${design.added_by.name}`}</p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={(e) => handleCheckStatus({
                                      name: design.name,
                                      id: design.id
                                    }, e)}
                                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                                    title="Check processing status"
                                  >
                                    <Info className="h-4 w-4 text-gray-500 hover:text-primary" />
                                  </button>
                                  <button
                                    onClick={(e) => handleReloadDesign({
                                      name: design.name,
                                      id: design.id,
                                      url: design.url
                                    }, e)}
                                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                                    title="Reload design"
                                  >
                                    <RefreshCw className="h-4 w-4 text-gray-500 hover:text-primary" />
                                  </button>
                                  <button
                                    onClick={(e) => handleDeleteDesign({
                                      name: design.name,
                                      id: design.id
                                    }, e)}
                                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                                    title="Delete design"
                                  >
                                    <Trash2 className="h-4 w-4 text-gray-500 hover:text-red-500" />
                                  </button>
                                </div>
                              </div>

                              {/* Status and Progress Section */}
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className={`typography-caption px-2 py-1 rounded-full capitalize ${getStatusColor(design.status)}`}>
                                    {design.status.replace('_', ' ')}
                                    {design.status === 'processing_wait' && jsonProcessingStatus[design.id] && (
                                      <span className="ml-1 text-primary-800">
                                        ({jsonProcessingStatus[design.id].percentage}%)
                                      </span>
                                    )}
                                  </span>
                                  <span className="typography-body-sm text-gray-600">
                                    {design.completed_frames}/{design.total_frames} frames
                                  </span>
                                </div>

                                {/* Progress Bar */}
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full ${
                                      design.status === 'completed' ? 'bg-green-500' :
                                      design.status === 'partially_completed' ? 'bg-yellow-500' :
                                      design.status === 'processing_wait' ? 'bg-purple-500' : 'bg-primary-500'
                                    }`}
                                    style={{ 
                                      width: design.status === 'processing_wait' && jsonProcessingStatus[design.id] 
                                        ? `${jsonProcessingStatus[design.id].percentage}%`
                                        : `${completionPercentage}%`
                                    }}
                                  />
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center justify-between pt-2">
                              {/* <p className={`typography-body-sm ${getSizeColor(sizeKb)} font-weight-medium`}>
                                Size: {sizeKb.toFixed(2)} KB
                                {!isValidSize && (
                                  <span className="ml-2 text-red-600">
                                    (Exceeds 500 KB limit)
                                  </span>
                                )}
                              </p> */}
                              <span className="typography-body-sm text-gray-500">View Design →</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  {totalPages > 1 &&
                    <div className='mt-4'>
                      <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={setCurrentPage}
                      />
                    </div>}
                </div>
              ) : (
                <EmptyStateDesign
                  type="figma"
                  onAddClick={() => {
                    setModalType('new');
                    setUploadMethod('figma');
                    setIsModalOpen(true);
                  }}
                />
              )
            ) : (
              // Images Display
              paginatedImageDesigns.length > 0 ? (
                <div className='h-[65vh] overflow-y-scroll'>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {paginatedImageDesigns.map((image, index) => {
                      const completionPercentage = image.total_frames > 0
                        ? Math.round((image.completed_frames / image.total_frames) * 100)
                        : 0;

                      const getStatusColor = (status) => {
                        switch (status) {
                          case 'completed': return 'bg-green-100 text-green-800';
                          case 'processing': return 'bg-primary-100 text-primary-800';
                          case 'processing_wait': return 'bg-purple-100 text-purple-800';
                          case 'partially_completed': return 'bg-yellow-100 text-yellow-800';
                          case 'failed': return 'bg-red-100 text-red-800';
                          default: return 'bg-gray-100 text-gray-800';
                        }
                      };

                      return (
                        <div key={index} className="relative">
                          <div
                            className={`bg-white rounded-lg border border-gray-200 p-4 transition-all h-[180px] flex flex-col justify-between
                        ${['completed', 'partially_completed'].includes(image.status)
                                ? 'hover:shadow-md cursor-pointer'
                                : 'opacity-75 cursor-not-allowed'
                              }`}
                            onClick={() => handleImageCardClick({
                              id: image.figma_ext_id,
                              name: image.name,
                              status: image.status
                            })}
                          >
                            <div className="space-y-3 relative">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h3 className="typography-body-lg font-weight-semibold text-gray-900">{image.name}</h3>
                                  <p className="typography-body-sm text-gray-600">{`Added by ${image.added_by.name}`}</p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={(e) => handleDeleteImage({
                                      name: image.name,
                                      id: image.figma_ext_id
                                    }, e)}
                                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                                    title="Delete image"
                                  >
                                    <Trash2 className="h-4 w-4 text-gray-500 hover:text-red-500" />
                                  </button>
                                </div>
                              </div>

                              {/* Status and Progress Section */}
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className={`typography-caption px-2 py-1 rounded-full capitalize ${getStatusColor(image.status)}`}>
                                    {image.status.replace('_', ' ')}
                                  </span>
                                  <span className="typography-body-sm text-gray-600">
                                    {image.completed_frames}/{image.total_frames} frames
                                  </span>
                                </div>

                                {/* Progress Bar */}
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full ${image.status === 'completed' ? 'bg-green-500' :
                                      image.status === 'partially_completed' ? 'bg-yellow-500' : 'bg-primary-500'
                                      }`}
                                    style={{ width: `${completionPercentage}%` }}
                                  />
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center justify-between pt-2">
                              <p className="typography-body-sm text-gray-600 font-weight-medium">
                                Created: {new Date(image.time_created).toLocaleDateString()}
                              </p>
                              <span className="typography-body-sm text-gray-500">View Images →</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  {/* Pagination for Images */}
                  {totalImagePages > 1 &&
                    <div className='mt-4'>
                      <Pagination
                        currentPage={currentImagePage}
                        totalPages={totalImagePages}
                        onPageChange={setCurrentImagePage}
                      />
                    </div>
                  }
                </div>
              ) : (
                <EmptyStateDesign
                  type="image"
                  onAddClick={() => {
                    setModalType('new');
                    setUploadMethod('image');
                    setIsModalOpen(true);
                  }}
                />
              )
            )}
          </div>
        )}
      </div>
      {/* Add Design Modal */}
      {isModalOpen && (
        <div
          className='fixed inset-0  bg-black bg-opacity-50 z-50 flex items-center justify-center'
        >
          <div className="bg-white w-[550px] rounded-md shadow-md p-6 max-w-lg mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="typography-heading-4 font-weight-bold">{modalType == 'image' ? "Add Images" : "Add New Design"}</h2>
              <button
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close"
                onClick={resetForm}
              >
                <X size={20} />
              </button>
            </div>
            <form onSubmit={handleSubmit}>
              {modalType == "new" &&(
                <>
                  {/* Design Name */}
                  <div className="mb-4">
                    <label htmlFor="designName" className="block font-weight-medium mb-1">
                      Design Name
                    </label>
                    <input
                      id="designName"
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Enter design name"
                      value={designName}
                      onChange={(e) => setDesignName(e.target.value)}
                      required
                    />
                  </div>
                  {/* Upload Method Selection */}
                  <div className="mb-4">
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="uploadMethod"
                          checked={uploadMethod === 'figma'}
                          onChange={() => setUploadMethod('figma')}
                          className="mr-2"
                        />
                        Figma URL
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="uploadMethod"
                          checked={uploadMethod === 'image'}
                          onChange={() => setUploadMethod('image')}
                          className="mr-2"
                        />
                        Upload Images
                      </label>
                    </div>
                  </div>
                </>
              )
              }
              {/* Conditional form fields based on selection */}
              {uploadMethod === 'figma' && modalType == "new" ? (
                <div className="mb-6">
                  <label htmlFor="figmaUrl" className="block font-weight-medium mb-1">
                    Figma URL
                  </label>
                  <input
                    id="figmaUrl"
                    type="url"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Enter Figma URL"
                    value={currentFigmaLink}
                    onChange={(e) => setCurrentFigmaLink(e.target.value)}
                    required={uploadMethod === 'figma'}
                  />
                </div>
              ) : (
                <div className="mb-6">
                  <label className="block font-weight-medium mb-1">
                    Upload Images
                  </label>
                  {images.length > 0 && (
                    <div className="flex justify-between items-center mb-2">
                      <span className="typography-body-sm text-gray-500">
                        {images.length} file{images.length !== 1 ? 's' : ''} ({formatFileSize(totalSize)})
                      </span>
                      <div className="flex space-x-1">
                        <button
                          type="button"
                          className={`p-1 rounded ${viewModeimg === 'grid' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
                          onClick={() => setViewModeimg('grid')}
                          aria-label="Grid view"
                        >
                          <Grid size={16} />
                        </button>
                        <button
                          type="button"
                          className={`p-1 rounded ${viewModeimg === 'list' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
                          onClick={() => setViewModeimg('list')}
                          aria-label="List view"
                        >
                          <List size={16} />
                        </button>
                      </div>
                    </div>
                  )}
                  {/* Image preview grid */}
                  {images.length > 0 && (
                    viewModeimg === 'grid' ? (
                      <div className="grid grid-cols-2 gap-3 mb-3 max-h-40 overflow-y-auto p-1 border border-gray-200 rounded-md">
                        {images.map((image) => (
                          <div
                            key={image.id}
                            className="relative rounded-md overflow-hidden h-32 border border-gray-200 group"
                          >
                            {image.preview ? (
                              <div className="relative w-full h-full">
                                <img
                                  src={image.preview}
                                  alt="Preview"
                                  className="w-full h-full object-contain bg-gray-50"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-200"></div>
                                <button
                                  type="button"
                                  onClick={() => removeImage(image.id)}
                                  className="absolute top-1 right-1 bg-white rounded-full p-1 shadow-md hover:bg-red-50 transition-colors"
                                  aria-label="Remove image"
                                >
                                  <X size={16} />
                                </button>
                              </div>
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                                <ImageIcon className="text-gray-400" size={24} />
                                <button
                                  type="button"
                                  onClick={() => removeImage(image.id)}
                                  className="absolute top-1 right-1 bg-white rounded-full p-1 shadow-md"
                                >
                                  <X size={16} />
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="mb-3 max-h-40 overflow-y-auto border border-gray-200 rounded-md divide-y">
                        {images.map((image) => (
                          <div
                            key={image.id}
                            className="flex items-center py-2 px-3 hover:bg-gray-50"
                          >
                            <div className="mr-3">
                              <FileIcon size={20} className="text-gray-400" />
                            </div>
                            <div className="flex-grow min-w-0">
                              <div className="truncate typography-body-sm">{image.name}</div>
                              <div className="typography-caption text-gray-500">{formatFileSize(image.size)}</div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeImage(image.id)}
                              className="ml-2 text-gray-400 hover:text-gray-600"
                            >
                              <X size={18} />
                            </button>
                          </div>
                        ))}
                      </div>
                    )
                  )}

                  {/* Upload area */}
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center relative"
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      e.currentTarget.classList.add('border-primary');
                    }}
                    onDragLeave={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      e.currentTarget.classList.remove('border-primary');
                    }}
                    onDrop={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      e.currentTarget.classList.remove('border-primary');
                      const droppedFiles = Array.from(e.dataTransfer.files).filter(
                        file => file.type.startsWith('image/')
                      );
                      processFiles(droppedFiles);
                    }}
                  >
                    <label className="block cursor-pointer">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 typography-body-sm text-gray-500">
                        Drag and drop images, or <span className="text-primary">browse</span>
                      </p>
                      <p className="mt-1 typography-caption text-gray-400">
                        Supports multiple files
                      </p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                        multiple
                        required={uploadMethod === 'image' && images.length === 0}
                      />
                    </label>
                  </div>
                </div>
              )}

              {/* Form Actions */}
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md"
                  disabled={isUpdating}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className={`px-4 py-2 ${isUpdating
                    ? 'bg-primary-300 cursor-not-allowed'
                    : 'bg-primary hover:bg-primary-600'} text-white rounded-md`}
                  disabled={isUpdating}
                >
                  {isUpdating
                    ? "Adding..."
                    : modalType == 'new'
                      ? "Add Design"
                      : "Add Images"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      {isTokenError &&
        <TokenErrorAlert onClose={() => { setIsModalOpen(true); setIsTokenError(false); }} />
      }

      {/* Delete Confirmation Modal */}
      <Modal isOpen={!!deleteConfirmation} onClose={() => !isDeleting && setDeleteConfirmation(null)}>
        <div className="space-y-4">
          <h2 className="typography-heading-4 font-weight-semibold">Confirm Delete</h2>
          <p>Are you sure you want to delete {deleteConfirmation?.type === 'frame' ? 'this image' : `"${deleteConfirmation?.name}"`}?</p>
          <div className="flex justify-end space-x-2 pt-4">
            <DynamicButton
              type="button"
              variant="secondary"
              size="medium"
              onClick={() => setDeleteConfirmation(null)}
              text="Cancel"
              disabled={isDeleting}
            />
            <DynamicButton
              type="button"
              variant="danger"
              size="medium"
              onClick={confirmDelete}
              text={isDeleting ? "Deleting..." : "Delete"}
              isLoading={isDeleting}
              disabled={isDeleting}
            />
          </div>
        </div>
      </Modal>
      {/* Delete Confirmation Modal */}
      {/* <Modal isOpen={isSessionModal} onClose={() => setIsSessionModal(false)}>
        <div className="space-y-4">
          <label htmlFor="sessionName" className="typography-body-lg font-weight-medium">
            Session Name
          </label>
          <input
            id="sessionName"
            type="text"
            value={sessionName}
            onChange={(e) => setSessionName(e.target.value)}
            className="w-full border rounded px-3 py-2"
            placeholder="Enter session name"
            required
          />
          <div className="flex justify-end space-x-2 pt-4">
            <DynamicButton
              type="button"
              variant="secondary"
              size="medium"
              onClick={() =>setIsSessionModal(false)}
              text="Cancel"
            />
            <DynamicButton
              type="button"
              variant="primary"
              size="medium"
              onClick={handleSessionName}
              text={"Save"}
              disabled={!sessionName.trim()}
            />
          </div>
        </div>
      </Modal> */}

      {/* Add Reload Confirmation Modal */}
      <Modal
        isOpen={!!reloadConfirmation}
        onClose={() => !isReloading && setReloadConfirmation(null)}
      >
        <div className="space-y-4">
          <h2 className="typography-heading-4 font-weight-semibold">Confirm Reload</h2>
          <p>This action will reload all frames from the beginning. Do you want to continue?</p>
          <div className="flex justify-end space-x-2 pt-4">
            <DynamicButton
              type="button"
              variant="secondary"
              size="medium"
              onClick={() => setReloadConfirmation(null)}
              text="Cancel"
              disabled={isReloading}
            />
            <DynamicButton
              type="button"
              variant="primary"
              size="medium"
              onClick={confirmReload}
              text={isReloading ? "Reloading..." : "Reload"}
              isLoading={isReloading}
              disabled={isReloading}
            />
          </div>
        </div>
      </Modal>

      {/* Add Status Modal */}
      <Modal isOpen={!!statusModal} onClose={() => {
        setStatusModal(null);
        setProcessingStatus(null);
      }}>
        <div className="space-y-4">
          <h2 className="typography-heading-4 font-weight-semibold">Processing Status</h2>
          <div className="space-y-4">
            {isLoadingStatus ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
              </div>
            ) : processingStatus ? (
              <>
                <div className="space-y-3">
                  {/* Status Badge */}
                  <div className="flex items-center justify-between">
                    <span className="typography-body-sm font-weight-medium">Status:</span>
                    <span className={`px-2 py-1 rounded-full typography-caption font-weight-medium ${processingStatus.status === 'completed' ? 'bg-green-100 text-green-800' :
                      processingStatus.status === 'processing' ? 'bg-primary-100 text-primary-800' :
                        processingStatus.status === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                      }`}>
                      {processingStatus.status.charAt(0).toUpperCase() + processingStatus.status.slice(1)}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between typography-body-sm">
                      <span>Progress</span>
                      <span>{Math.round((processingStatus.completed_frames / processingStatus.total_frames) * 100) || 0}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${processingStatus.status === 'completed' ? 'bg-green-500' :
                          processingStatus.status === 'failed' ? 'bg-gray-300' : 'bg-primary-500'
                          }`}
                        style={{ width: `${(processingStatus.completed_frames / processingStatus.total_frames) * 100 || 0}%` }}
                      />
                    </div>
                  </div>

                  {/* Frame Details */}
                  <div className="space-y-2">
                    <div className="flex justify-between typography-body-sm">
                      <span>Total Frames:</span>
                      <span>{processingStatus.total_frames}</span>
                    </div>
                    <div className="flex justify-between typography-body-sm">
                      <span>Completed Frames:</span>
                      <span>{processingStatus.completed_frames}</span>
                    </div>
                    <div className="flex justify-between typography-body-sm">
                      <span>Failed Frames:</span>
                      <span className={processingStatus.failed_frames > 0 ? 'text-red-600' : 'text-gray-600'}>
                        {processingStatus.failed_frames}
                      </span>
                    </div>
                  </div>

                  {/* Error Message if any */}
                  {processingStatus.error_message && (
                    <div className="typography-body-sm text-red-600 bg-red-50 p-3 rounded">
                      {processingStatus.error_message}
                    </div>
                  )}

                  {/* Last Updated */}
                  <div className="typography-caption text-gray-500">
                    Last updated: {new Date(processingStatus.time_updated).toLocaleString()}
                  </div>
                </div>
              </>
            ) : (
              <p className="text-gray-600">No status information available</p>
            )}
          </div>
          <div className="flex justify-end pt-4">
            <DynamicButton
              type="button"
              variant="secondary"
              size="medium"
              onClick={() => {
                setStatusModal(null);
                setProcessingStatus(null);
              }}
              text="Close"
            />
          </div>
        </div>
      </Modal>

      {/* Rename Modal */}
      <Modal isOpen={!!renameModal} onClose={() => !isRenaming && setRenameModal(null)}>
        <div className="space-y-4">
          <h2 className="typography-heading-4 font-weight-semibold">Rename Image</h2>
          <div>
            <label htmlFor="frameName" className="block typography-body-sm font-weight-medium text-gray-700">
            Image Name
            </label>
            <input
              type="text"
              id="frameName"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              placeholder="Enter new name"
              disabled={isRenaming}
            />
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <DynamicButton
              type="button"
              variant="secondary"
              size="medium"
              onClick={() => setRenameModal(null)}
              text="Cancel"
              disabled={isRenaming}
            />
            <DynamicButton
              type="button"
              variant="primary"
              size="medium"
              onClick={confirmRename}
              text={isRenaming ? "Renaming..." : "Rename"}
              isLoading={isRenaming}
              disabled={isRenaming || !newName.trim()}
            />
          </div>
        </div>
      </Modal>

      {/* Past Discussions Modal */}
      <PastFigmaDiscussionsModal
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        discussions={discussions}
        isLoading={isHistoryLoading}
        onRowClick={handleDiscussionClick}
        totalCount={totalCount}

        onPageChange={handleHistoryPageChange}
        onPageSizeChange={handleHistoryLimitChange}
      />
    </div>
  );
}