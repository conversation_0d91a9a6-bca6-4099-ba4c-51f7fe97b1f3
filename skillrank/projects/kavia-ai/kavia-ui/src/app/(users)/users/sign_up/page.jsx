"use client";

import React, { useState, useContext, useEffect, useCallback } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { signUpUser, getTenantName, validateReferralCode } from "../../../../utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { decryptTenantId, encryptTenantId } from "@/utils/hash";
import LoginSignupContainer from "@/components/LoginSignupContainer";
import { ArrowRightIcon, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { Check, X } from "lucide-react";


// // Add this API function for referral validation
// const validateReferralCode = async (code) => {
//   try {
//     const backend_base_url = process.env.NEXT_PUBLIC_API_URL;
//     const response = await fetch(`${backend_base_url}/auth/referral/validate/${code}`, {
//       method: 'GET',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//     });

//     if (!response.ok) {
//       throw new Error('Validation failed');
//     }

//     return await response.json();
//   } catch (error) {
//     console.error('Error validating referral code:', error);
//     return null;
//   }
// };

const SignupPage = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [name, setName] = useState("");
  const [designation, setDesignation] = useState("");
  const [department, setDepartment] = useState("");
  const [referralCode, setReferralCode] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [condition, setIsCondition] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const { showAlert } = useContext(AlertContext);

  // Referral validation state
  const [referralValidation, setReferralValidation] = useState({
    isValidating: false,
    isValid: false,
    referrerInfo: null,
    error: null
  });

  const router = useRouter();
  const queryParams = useSearchParams();
  const [tenant_id, setTenant_id] = useState(null);
  const [tenant_name, setTenant_name] = useState(null);
  const [isLoadingTenant, setIsLoadingTenant] = useState(false);
  const [unencryptedTenantId, setUnencryptedTenantId] = useState(null);

  const [passwordValidation, setPasswordValidation] = useState({
    hasSymbol: false,
    hasUppercase: false,
    hasNumber: false,
    hasMinLength: false
  });

  const [confirmPasswordValidation, setConfirmPasswordValidation] = useState({
    matches: false,
    hasValue: false
  });

  const validatePassword = useCallback((password) => {
    const validation = {
      // Updated to include more common symbols
      hasSymbol: /[@$!%*?&><.,;:'"~`#^+=\-_|\\\/\[\]{}()]/.test(password),
      hasUppercase: /[A-Z]/.test(password),
      hasNumber: /\d/.test(password),
      hasMinLength: password.length >= 8
    };
    setPasswordValidation(validation);
    return validation;
  }, []);

  const validateConfirmPassword = useCallback((confirmPass, originalPass) => {
    const validation = {
      matches: confirmPass === originalPass && confirmPass.length > 0,
      hasValue: confirmPass.length > 0
    };
    setConfirmPasswordValidation(validation);
    return validation;
  }, []);

  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    validatePassword(newPassword);

    // Re-validate confirm password if it has a value
    if (confirmPassword) {
      validateConfirmPassword(confirmPassword, newPassword);
    }

    // Show/hide conditions based on whether user has started typing
    setIsCondition(newPassword.length > 0);
  };

  const handleConfirmPasswordChange = (e) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);
    validateConfirmPassword(newConfirmPassword, password);
  };

  const ValidationIcon = ({ isValid, show = true }) => {
    if (!show) return null;

    return isValid ? (
      <Check className="w-4 h-4 text-green-500" />
    ) : (
      <X className="w-4 h-4 text-red-500" />
    );
  };

  // Debounced referral code validation
  const validateReferral = useCallback(async (code) => {
    if (!code || code.length < 6) { // Minimum expected length
      setReferralValidation({
        isValidating: false,
        isValid: false,
        referrerInfo: null,
        error: null
      });
      return;
    }

    // Check if code has reached expected length (based on your format: ORG_PREFIX + INITIALS + NUMBER)
    // Minimum 6 characters, but typically 7-10 characters
    if (code.length >= 6) {
      setReferralValidation(prev => ({ ...prev, isValidating: true, error: null }));

      try {
        const result = await validateReferralCode(code);

        if (result && result.valid) {
          setReferralValidation({
            isValidating: false,
            isValid: true,
            referrerInfo: {
              name: result.referrer_name,
              organization: result.referrer_organization
            },
            error: null
          });
        } else {
          setReferralValidation({
            isValidating: false,
            isValid: false,
            referrerInfo: null,
            error: result?.message || 'Invalid referral code'
          });
        }
      } catch (error) {
        setReferralValidation({
          isValidating: false,
          isValid: false,
          referrerInfo: null,
          error: 'Failed to validate referral code'
        });
      }
    }
  }, []);

  // Effect to validate referral code when it changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (referralCode) {
        validateReferral(referralCode.toUpperCase());
      } else {
        setReferralValidation({
          isValidating: false,
          isValid: false,
          referrerInfo: null,
          error: null
        });
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [referralCode, validateReferral]);

  // Handle referral code from URL params
  useEffect(() => {
    const refCode = queryParams.get("ref");
    if (refCode) {
      setReferralCode(refCode.toUpperCase());
    }
  }, [queryParams]);

  useEffect(() => {
    async function fetchTenantName(tenant_id) {
      try {
        setIsLoadingTenant(true);
        const tenant_details = await getTenantName(tenant_id);
        setTenant_name(tenant_details.name);
      } finally {
        setIsLoadingTenant(false);
      }
    }

    if (queryParams.get("tenant_id")) {
      const tenantIdParam = queryParams.get("tenant_id");
      // Check if it's the B2C client ID and remove from URL if it is
      if (tenantIdParam === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
        // Remove tenant_id from URL without reloading the page
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete("tenant_id");
        window.history.replaceState({}, "", newUrl.toString());
      }

      if (decryptTenantId(tenantIdParam)) {
        setTenant_id(tenantIdParam);
        setUnencryptedTenantId(decryptTenantId(tenantIdParam));
      } else {
        setTenant_id(encryptTenantId(tenantIdParam));
        setUnencryptedTenantId(tenantIdParam);
      }
      fetchTenantName(encryptTenantId(tenantIdParam));
    } else {
      // Use default B2C tenant ID directly without checking localStorage
      const b2cTenantId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID || "b2c";

      // Set the tenant ID without adding to URL
      setTenant_id(encryptTenantId(b2cTenantId));
      setUnencryptedTenantId(b2cTenantId);
      setIsLoadingTenant(false);
    }
  }, [queryParams, router]);

  // condition for checking user full name 
  const isValidFullName = (name) => {
    const regex = /^[a-zA-ZÀ-ÿ\s]*$/;
    return regex.test(name);
  };

  const handleReferralCodeChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''); // Only allow alphanumeric
    setReferralCode(value);
  };

  const getReferralInputBorderColor = () => {
    if (referralValidation.isValidating) return 'border-primary-300';
    if (referralValidation.isValid) return 'border-green-500';
    if (referralValidation.error) return 'border-red-500';
    return 'border-gray-300';
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);

    // Basic field validation
    if (!email || !password || !confirmPassword) {
      showAlert("Email, Password, and Confirm Password are mandatory fields.", "danger");
      setIsLoading(false);
      return;
    }

    // Password match validation
    if (!confirmPasswordValidation.matches) {
      showAlert("Passwords do not match.", "danger");
      setIsLoading(false);
      return;
    }

    // Full name validation
    if (!isValidFullName(name)) {
      showAlert("Please Enter valid Full name characters and spaces are acceptable", "danger");
      setIsLoading(false);
      return;
    }

    // Password strength validation
    const { hasSymbol, hasUppercase, hasNumber, hasMinLength } = passwordValidation;
    if (!hasSymbol || !hasUppercase || !hasNumber || !hasMinLength) {
      showAlert("Password must meet all the specified requirements.", "warning");
      setIsCondition(true);
      setIsLoading(false);
      return;
    }

    try {
      const response = await signUpUser(
        email,
        password,
        name,
        "", // Empty string for designation
        "", // Empty string for department
        tenant_id,
        acceptedTerms,
        referralCode // Pass referral code to signup
      );

      showAlert("Successfully signed up!", "success");
      setIsLoading(false);

      // Store the email in localStorage to maintain state across redirects
      localStorage.setItem('signup_email', email);

      // Add tenant_id to URL only if it's not the B2C client ID
      const decodedTenantId = decryptTenantId(tenant_id);
      let confirmSignupUrl = `/users/confirm_signup?username=${encodeURIComponent(email)}${decodedTenantId !== process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID
        ? `&tenant_id=${encodeURIComponent(decodedTenantId)}`
        : ''
        }`;


      // ADD REFERRAL_CODE TO URL IF USER ENTERED ONE
      if (referralCode && referralCode.trim()) {
        confirmSignupUrl += `&referral_code=${encodeURIComponent(referralCode.toUpperCase())}`;
        // Also store in localStorage as backup
        localStorage.setItem('referral_code', referralCode.toUpperCase());
      }
      router.push(confirmSignupUrl);
    } catch (error) {
      if (error.message.includes("400")) {
        showAlert("Username (email) already exists", "danger");
        setIsLoading(false);
      } else {
        showAlert("Signup failed. Please check your details.", "danger");
        setIsLoading(false);
      }
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <LoginSignupContainer>
      <div className="bg-custom-bg-primary rounded-lg shadow-xl p-6 max-w-sm w-full z-10 text-center sm:max-w-md md:max-w-lg lg:max-w-md">
        <h1 className="project-panel-heading mb-2">Create your account</h1>
        {isLoadingTenant ? (
          <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-gradient-to-r from-semantic-purple-100 to-primary-100 border border-primary-200">
            <div className="w-2 h-2 rounded-full bg-primary mr-2 animate-pulse"></div>
            <div className="h-4 w-24 bg-semantic-gray-200 rounded animate-pulse"></div>
          </div>
        ) : (
          <>
            {tenant_name && unencryptedTenantId !== process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID && (
              <div className="flex flex-col items-center mb-4">
                <div className={`inline-flex items-center px-3 py-1 rounded-full ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID
                  ? 'bg-gradient-to-r from-[#9D3ADF] to-[#1F6FEB] border border-[#1F6FEB]'
                  : 'bg-gradient-to-r from-purple-100 to-primary-100 border border-primary-200'
                  }`}>
                  <span className={`w-2 h-2 rounded-full ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID ? 'bg-white' : 'bg-primary'
                    } mr-2`}></span>
                  <span className={`font-weight-medium ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID ? 'text-white' : 'text-gray-800'
                    }`}>{tenant_name}</span>
                </div>
              </div>
            )}
            {unencryptedTenantId === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID && (
              <div className="flex flex-col items-center mb-4">
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-purple-100 to-primary-100 border border-primary-200">
                  <span className="w-2 h-2 rounded-full bg-primary mr-2"></span>
                  <span className="font-weight-medium text-gray-800">Kavia Common</span>
                </div>
              </div>
            )}
          </>
        )}
        <p className="text-font mb-4">
          Welcome! Please fill in the details to get started.
        </p>

        {/* Temporarily commenting out Google authentication
        {decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID &&
          <>
            <div className="mb-6">
              <button
                type="button"
                onClick={async () => {
                  try {
                    const backendUrl = process.env.NEXT_PUBLIC_API_URL;
                    window.location.href = `${backendUrl}/auth/google?action=signup&tenant_id=${encodeURIComponent(tenant_id)}`;
                  } catch (error) {
                    showAlert("Failed to initiate Google authentication", "danger");
                  }
                }}
                className="w-full inline-flex justify-center items-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#F97316"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                <span className="typography-body-sm font-weight-medium text-gray-700">Continue with Google</span>
              </button>
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center typography-body-sm">
                <span className="px-2 bg-white text-gray-500">Or</span>
              </div>
            </div>
          </>}
        */}

        <form onSubmit={handleSubmit} className="flex flex-col items-center mt-6">
          <label
            htmlFor="name"
            className="self-start mb-1 login-form"
          >
            <strong>Full Name</strong> <span className="text-red-500">*</span>
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g. John Doe"
            required
            className="w-full px-3 py-2 mb-3 border border-gray-300 rounded-md"
          />


          {/* Referral Code Input with Improved Validation */}
          <label
            htmlFor="referralCode"
            className="self-start mb-1 login-form"
          >
            <strong>Referral Code</strong>
          </label>
          <div className="relative w-full mb-1">
            <input
              id="referralCode"
              type="text"
              value={referralCode}
              onChange={handleReferralCodeChange}
              placeholder="Enter referral code"
              maxLength={12}
              className={`w-full px-3 py-2 border transition-colors pr-10 rounded-md ${referralValidation.isValidating
                ? 'border-primary-300 bg-primary-50'
                : referralValidation.isValid
                  ? 'border-green-300 bg-green-50'
                  : referralValidation.error
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300'
                }`}
            />
            {referralValidation.isValidating && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Loader2 className="w-4 h-4 text-primary animate-spin" />
              </div>
            )}
            {!referralValidation.isValidating && referralValidation.isValid && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <CheckCircle className="w-4 h-4 text-green-500" />
              </div>
            )}
            {!referralValidation.isValidating && referralValidation.error && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <AlertCircle className="w-4 h-4 text-red-500" />
              </div>
            )}
          </div>

          {/* Compact Referral Status Messages */}
          {referralValidation.isValidating && (
            <div className="w-full mb-3 text-xs text-primary flex items-center">
              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              Checking referral code...
            </div>
          )}

          {referralValidation.isValid && referralValidation.referrerInfo && (
            <div className="w-full mb-3 text-xs text-green-700 flex items-center">
              <CheckCircle className="w-3 h-3 mr-1 text-green-500" />
              <span>
                Referred by <strong>{referralValidation.referrerInfo.name}</strong>
              </span>
            </div>
          )}

          {referralValidation.error && (
            <div className="w-full mb-3 text-xs text-red-600 flex items-center">
              <AlertCircle className="w-3 h-3 mr-1 text-red-500" />
              {referralValidation.error}
            </div>
          )}


          <label
            htmlFor="email"
            className="self-start mb-1 login-form"
          >
            <strong>Email Address</strong> <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="e.g. Work email address"
            required
            className="w-full px-3 py-2 mb-3 border border-gray-300 rounded-md"
          />
          <label htmlFor="password" className="self-start mb-1 login-form">
            <strong>Password</strong> <span className="text-red-500">*</span>
          </label>
          <div className="relative w-full mb-3">
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={handlePasswordChange}
              placeholder="Password"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-2 top-2 text-gray-500"
            >
              {showPassword ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                  />
                </svg>
              )}
            </button>
          </div>
          <label htmlFor="confirmPassword" className="self-start mb-1 login-form">
            <strong>Confirm Password</strong> <span className="text-red-500">*</span>
          </label>
          <div className="relative w-full mb-3">
            <input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={handleConfirmPasswordChange}
              placeholder="Confirm Password"
              required
              className={`w-full px-3 py-2 border rounded-md pr-10 ${confirmPasswordValidation.hasValue
                  ? confirmPasswordValidation.matches
                    ? 'border-green-300 bg-green-50'
                    : 'border-red-300 bg-red-50'
                  : 'border-gray-300'
                }`}
            />
            <button
              type="button"
              onClick={toggleConfirmPasswordVisibility}
              className="absolute right-2 top-2 text-gray-500"
            >
              {showConfirmPassword ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                  />
                </svg>
              )}
            </button>
          </div>

          {confirmPasswordValidation.hasValue && (
            <div className="w-full mb-3 flex items-center space-x-2">
              <ValidationIcon isValid={confirmPasswordValidation.matches} />
              <span className={`text-sm ${
                confirmPasswordValidation.matches ? 'text-green-600' : 'text-red-600'
              }`}>
                {confirmPasswordValidation.matches ? 'Passwords match' : 'Passwords do not match'}
              </span>
            </div>
          )}

          {condition && (
            <div className="mb-3 self-start w-full">
              <h3 className="text-left mb-2 user-panel-info">
                <strong>Your password must contain:</strong>
              </h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <ValidationIcon isValid={passwordValidation.hasSymbol} />
                  <span className={`text-sm ${
                    passwordValidation.hasSymbol ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    a symbol (@$!%*?&)
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <ValidationIcon isValid={passwordValidation.hasUppercase} />
                  <span className={`text-sm ${
                    passwordValidation.hasUppercase ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    an uppercase letter
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <ValidationIcon isValid={passwordValidation.hasNumber} />
                  <span className={`text-sm ${
                    passwordValidation.hasNumber ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    a number
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <ValidationIcon isValid={passwordValidation.hasMinLength} />
                  <span className={`text-sm ${
                    passwordValidation.hasMinLength ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    8 characters minimum
                  </span>
                </div>
              </div>
            </div>
          )}

       
          <div className="w-full mb-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={acceptedTerms}
                onChange={(e) => setAcceptedTerms(e.target.checked)}
                className="rounded border-gray-300 text-[#F26A1B] focus:ring-[#F26A1B]"
              />
              <span className="text-sm text-gray-600">
                I have read and agree to the{" "}
                <Link
                  href="https://kavia.ai/terms"
                  className="text-[#F26A1B] hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  terms of service
                </Link>
              </span>
            </label>
          </div>
          <button
            type="submit"
            className={`w-full flex items-center justify-between bg-primary border ${isLoading || !acceptedTerms ? 'border-semantic-gray-600' : 'border-primary'} border-opacity-50 text-primary-foreground rounded-md mt-4 p-1.5 text-md relative ${isLoading || !acceptedTerms ? 'bg-semantic-gray-400' : 'bg-primary'} text-primary-foreground`}
            disabled={isLoading || !acceptedTerms}
          >
            <div className="flex items-center mx-auto">
              {!isLoading ? (
                <>
                  Continue
                  <ArrowRightIcon className="w-4 h-4 ml-2" />
                </>
              ) : (
                <div>
                  <div className="spinner-empty border-s-primary"></div>
                </div>)}
            </div>
          </button>
        </form>

        <div className="mt-4 border-t typography-body-sm sm:typography-body">
          <p className="mt-3 text-gray-600">
            Have an account?
            <Link
              href={`/users/login${tenant_id && decryptTenantId(tenant_id) !== process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID ? `?tenant_id=${encodeURIComponent(decryptTenantId(tenant_id))}` : ''}`}
              className="text-[#F26A1B] ml-1"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </LoginSignupContainer>
  );
};

export default SignupPage;