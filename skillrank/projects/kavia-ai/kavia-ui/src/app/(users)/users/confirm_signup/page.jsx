"use client";

import React, { useState, useEffect, Suspense, useContext } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { confirmSignUp, resendConfirmationCode } from "../../../../utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { decryptTenantId, encryptTenantId } from "../../../../utils/hash";
import LoginSignupContainer from "@/components/LoginSignupContainer";

const ConfirmSignUpContent = () => {
  const [confirmationCode, setConfirmationCode] = useState([
    "",
    "",
    "",
    "",
    "",
    "",
  ]);
  const [resendTimeout, setResendTimeout] = useState(60);
  const { showAlert } = useContext(AlertContext)
  const router = useRouter();
  const searchParams = useSearchParams();
  const [buttonStatus, setButtonStatus] = useState(false);
  const username = searchParams.get("username");
  const [tenant_id, setTenant_id] = useState(null);
  const [referralCode, setReferralCode] = useState(null);
  useEffect(() => {
    // Handle tenant_id from URL or use default B2C client ID
    if (searchParams.get("tenant_id")) {
      const tenantIdParam = searchParams.get("tenant_id");

      // Check if it's the B2C client ID and remove from URL if it is
      if (tenantIdParam === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
        // Remove tenant_id from URL without reloading the page
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete("tenant_id");
        window.history.replaceState({}, "", newUrl.toString());
      }

      if (decryptTenantId(tenantIdParam)) {
        setTenant_id(tenantIdParam);
      } else {
        setTenant_id(encryptTenantId(tenantIdParam));
      }
    } else {
      // Use default B2C tenant ID directly without checking localStorage
      const b2cTenantId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID || "b2c";
      setTenant_id(encryptTenantId(b2cTenantId));
    }

     const referralCodeParam = searchParams.get("referral_code");
    if (referralCodeParam) {
      setReferralCode(decodeURIComponent(referralCodeParam));
      
    } else {
      // Fallback: check localStorage
      const storedReferralCode = localStorage.getItem("referral_code");
      if (storedReferralCode) {
        setReferralCode(storedReferralCode);
        
      }
    }


   if (!username) {
      const storedEmail = localStorage.getItem('signup_email');
      if (storedEmail) {
        // Only include tenant_id in URL if it's not the B2C client ID
        const tenantParam = tenant_id && decryptTenantId(tenant_id) !== process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID
          ? `&tenant_id=${encodeURIComponent(decryptTenantId(tenant_id))}`
          : '';

        // PRESERVE REFERRAL CODE IN URL WHEN REDIRECTING
        const referralParam = referralCodeParam ? `&referral_code=${encodeURIComponent(referralCodeParam)}` : '';

        router.replace(`/users/confirm_signup?username=${encodeURIComponent(storedEmail)}${tenantParam}${referralParam}`);
      } else {
        showAlert("Email information is missing. Please try signing up again.", "danger");
        setTimeout(() => {
          router.push('/users/sign_up');
        }, 2000);
      }
    }
  }, [username, router, searchParams, tenant_id]);

  const handleChange = (e, index) => {
    // Only allow numeric input
    const value = e.target.value.replace(/[^0-9]/g, '');

    if (value.length <= 1) {
      const newCode = [...confirmationCode];
      newCode[index] = value;
      setConfirmationCode(newCode);

      // Auto-focus next input after entering a digit
      if (value !== "" && index < 5) {
        document.getElementById(`code-${index + 1}`).focus();
      }
    }
  };

  // Handle paste event for verification code
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();

    // If pasted data is a 6-digit code
    if (/^\d{6}$/.test(pastedData)) {
      const newCode = pastedData.split('');
      setConfirmationCode(newCode);

      // Focus on the last input after successful paste
      document.getElementById('code-5').focus();
    }
  };

  // Handle keydown events for navigation between inputs
  const handleKeyDown = (e, index) => {
    // Handle backspace
    if (e.key === 'Backspace') {
      if (index > 0 && confirmationCode[index] === '') {
        // If current field is empty and backspace is pressed, move to previous field
        document.getElementById(`code-${index - 1}`).focus();
      }
    }
    // Handle arrow keys for navigation
    else if (e.key === 'ArrowLeft' && index > 0) {
      document.getElementById(`code-${index - 1}`).focus();
    }
    else if (e.key === 'ArrowRight' && index < 5) {
      document.getElementById(`code-${index + 1}`).focus();
    }
  };

  const handleSubmit = async (event) => {
    setButtonStatus(true);
    event.preventDefault();
    try {
      const code = confirmationCode.join("");
      if (!code) {
        showAlert('Confirmation Code is Required!', "danger")
        setButtonStatus(false);
        return;
      }

      // Don't re-encrypt if tenant_id is already encrypted
      // Just pass the tenant_id directly as it's already properly formatted
      const response = await confirmSignUp(username, code, tenant_id, referralCode);

      showAlert("Sign up confirmed successfully!", "success");
       if (referralCode) {
        localStorage.removeItem("referral_code");
      }
      setButtonStatus(false);
      setResendTimeout(0);
      setTimeout(() => {
        router.push("/users/login");
      }, 1000);
    } catch (error) {
      
      showAlert("Confirmation failed. Please check your code.", "danger");
      setButtonStatus(false);
    }
  };

  const handleResend = async () => {
    setButtonStatus(true);
    try {
      // Don't re-encrypt if tenant_id is already encrypted
      // Just pass the tenant_id directly as it's already properly formatted
      const response = await resendConfirmationCode(username, tenant_id);

      showAlert("Confirmation code resent successfully!", "info");
      setResendTimeout(60)
      setButtonStatus(false);
    } catch (error) {
      
      showAlert("Failed to resend confirmation code.", "danger");
      setButtonStatus(false);
    }
  };

  useEffect(() => {
    if (resendTimeout > 0) {
      const timer = setTimeout(() => setResendTimeout(resendTimeout - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimeout]);

  return (
    <LoginSignupContainer>
      <div className="bg-white rounded-lg shadow-xl p-8 -mt-28 max-w-md w-full z-10 text-center">
        <h1 className="user-panel-sub-head mb-2">Verify account</h1>
        <p className="text-font mb-6">
          Code has been sent to <span className="font-weight-bold">{username}</span>.
          <br />
          Enter the code to verify your account.
        </p>

          {/* ✅ ADD REFERRAL CODE DISPLAY */}
        {referralCode && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-700">
              ✅ <strong>Referral Code:</strong> {referralCode}
            </p>
            <p className="text-xs text-green-600 mt-1">
              Your referral will be tracked once you verify your email.
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="flex flex-col items-center">
          <div className="flex justify-center space-x-2 mb-4">
            {confirmationCode.map((digit, index) => (
              <input
                key={index}
                id={`code-${index}`}
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength="1"
                value={digit}
                onChange={(e) => handleChange(e, index)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                onPaste={index === 0 ? handlePaste : undefined}
                className={`w-12 h-12 text-center typography-body-lg border ${digit ? 'border border-[#8C3E10] border-opacity-50 bg-primary-50' : 'border-gray-300'
                  } rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors`}
                autoComplete="one-time-code"
              />
            ))}
          </div>

          <p className="text-gray-600 mb-4">
            I did not receive the code!{" "}
            <button
              type="button"
              onClick={handleResend}
              className="text-[hsl(var(--primary))] hover:text-primary-700"
              disabled={resendTimeout > 0}
            >
              Resend
            </button>
          </p>

          {resendTimeout > 0 && (
            <p className="text-gray-600 mb-4">
              Resend code in 00:
              {resendTimeout < 10 ? `0${resendTimeout}` : resendTimeout}
            </p>
          )}

          <button
            type="submit"
            className={`w-full bg-[hsl(var(--primary))] border border-[#8C3E10] border-opacity-50 text-white rounded-md px-4 py-2.5 typography-body-lg ${buttonStatus ? 'bg-gray-400' : 'bg-[hsl(var(--primary))]'}`}
            disabled={buttonStatus}
          >
            {!buttonStatus ? (
              <>
                Verify your Account
              </>
            ) : (
              <div
                className=" inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-white"
                role="status"
              >
                <span className="absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                  Loading...
                </span>
              </div>
            )}

          </button>
        </form>

        <p className="mt-6 text-gray-600">
          Have an account?{" "}
          <Link
            href={`/users/login${tenant_id && decryptTenantId(tenant_id) !== process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID ? `?tenant_id=${encodeURIComponent(decryptTenantId(tenant_id))}` : ''}`}
            className="text-[hsl(var(--primary))]"
          >
            Sign in
          </Link>
        </p>
      </div>
    </LoginSignupContainer>
  );
};

const ConfirmSignUpPage = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <ConfirmSignUpContent />
  </Suspense>
);

export default ConfirmSignUpPage;
