"use client";

import React, { useContext, useState } from 'react';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { Banner } from '@/components/Banner/Banner';
import { cn } from "@/lib/utils";

const FlashDemoPage = () => {
  const { showAlert } = useContext(AlertContext);
  const [showBanner, setShowBanner] = useState(false);
  const [bannerType, setBannerType] = useState('announcement');

  const handleShowAlert = (type) => {
    const messages = {
      success: "Operation completed successfully! Your changes have been saved.",
      error: "Something went wrong. Please try again later.",
      warning: "Please review your input before proceeding.",
      info: "New features are now available in your dashboard.",
      danger: "Critical error detected. Please contact support."
    };

    showAlert(messages[type], type);
  };

  const handleShowBanner = (type) => {
    setBannerType(type);
    setShowBanner(true);
  };

  const bannerMessages = {
    alert: {
      message: "System maintenance scheduled for tonight at 2:00 AM EST",
      detailed: "We will be performing critical system updates that may affect service availability. Expected downtime: 30 minutes."
    },
    maintenance: {
      message: "Application under maintenance - some features may be limited",
      detailed: "We are currently upgrading our infrastructure to provide better performance and reliability. Thank you for your patience."
    },
    announcement: {
      message: "🎉 New AI-powered code generation features are now live!",
      detailed: "Experience faster development with our enhanced AI capabilities including smart code completion, automated testing, and intelligent refactoring suggestions."
    }
  };

  return (
    <div className="min-h-screen bg-custom-bg-primary p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-custom-text-primary mb-4">
            Flash Messages Demo
          </h1>
          <p className="text-custom-text-secondary">
            Test the improved floating flash messages and banners with better UI/UX design and theme integration.
          </p>
        </div>

        {/* Alert Messages Section */}
        <div className="bg-custom-bg-secondary rounded-lg p-6 mb-8 border border-custom-border">
          <h2 className="text-xl font-semibold text-custom-text-primary mb-4">
            Alert Messages (Floating Notifications)
          </h2>
          <p className="text-custom-text-secondary mb-6">
            These appear as floating notifications in the top-right corner with improved animations and theme colors.
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {['success', 'error', 'warning', 'info', 'danger'].map((type) => (
              <button
                key={type}
                onClick={() => handleShowAlert(type)}
                className={cn(
                  "px-4 py-2 rounded-md text-sm font-medium transition-all duration-200",
                  "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2",
                  type === 'success' && "bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500",
                  type === 'error' && "bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500",
                  type === 'warning' && "bg-yellow-100 text-yellow-700 hover:bg-yellow-200 focus:ring-yellow-500",
                  type === 'info' && "bg-primary-100 text-primary-700 hover:bg-primary-200 focus:ring-primary-500",
                  type === 'danger' && "bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500"
                )}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Banner Messages Section */}
        <div className="bg-custom-bg-secondary rounded-lg p-6 mb-8 border border-custom-border">
          <h2 className="text-xl font-semibold text-custom-text-primary mb-4">
            Banner Messages (Floating Top Banners)
          </h2>
          <p className="text-custom-text-secondary mb-6">
            These appear as floating banners at the top center of the screen with thin, elegant design.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {['alert', 'maintenance', 'announcement'].map((type) => (
              <button
                key={type}
                onClick={() => handleShowBanner(type)}
                className={cn(
                  "px-4 py-2 rounded-md text-sm font-medium transition-all duration-200",
                  "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2",
                  type === 'alert' && "bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500",
                  type === 'maintenance' && "bg-primary-100 text-primary-700 hover:bg-primary-200 focus:ring-primary-500",
                  type === 'announcement' && "bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500"
                )}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)} Banner
              </button>
            ))}
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-custom-bg-secondary rounded-lg p-6 border border-custom-border">
          <h2 className="text-xl font-semibold text-custom-text-primary mb-4">
            Improvements Made
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-custom-text-primary mb-2">Alert Messages</h3>
              <ul className="text-sm text-custom-text-secondary space-y-1">
                <li>• Floating design (no height impact)</li>
                <li>• Improved animations with smooth transitions</li>
                <li>• Theme-integrated colors</li>
                <li>• Better typography and spacing</li>
                <li>• Enhanced accessibility</li>
                <li>• Responsive design</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-custom-text-primary mb-2">Banner Messages</h3>
              <ul className="text-sm text-custom-text-secondary space-y-1">
                <li>• Converted to floating design</li>
                <li>• Thinner, more elegant appearance</li>
                <li>• Centered positioning</li>
                <li>• Backdrop blur effects</li>
                <li>• Orange theme integration</li>
                <li>• Improved modal styling</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Banner */}
      {showBanner && (
        <Banner
          id="demo-banner"
          type={bannerType}
          message={bannerMessages[bannerType].message}
          detailedMessage={bannerMessages[bannerType].detailed}
          showBanner={true}
          onAcknowledge={() => setShowBanner(false)}
        />
      )}
    </div>
  );
};

export default FlashDemoPage;
