"use client";

import { useState, useContext, useEffect, Suspense } from "react";
import { useUser } from "@/components/Context/UserContext";
import { Eye, EyeOff } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import {
  getSettings,
  updateSettings,
  SettingModel,
  SecureSettingValue,
} from "@/utils/organization/settings";
import { decryptStringClipboard } from "@/utils/hash";

interface SettingSection {
  title: string;
  description: string;
  settings: SettingItem[];
}

interface SettingItem {
  section: string;
  key: string;
  label: string;
  description?: string;
  type: "toggle" | "checkbox" | "text" | "integer" | "dropdown";
  defaultValue: boolean | string | number;
  value: boolean | string | number;
  options?: string[];
}

// Add this skeleton loader component
const SettingsSkeleton = () => {
  return (
    <div className="w-full max-w-[1200px] animate-pulse">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <div className="h-6 w-32 bg-gray-200 rounded"></div>
          <div className="h-4 w-64 bg-gray-200 rounded mt-2"></div>
        </div>
        <div className="h-10 w-32 bg-gray-200 rounded"></div>
      </div>

      <div className="space-y-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="mb-6">
            <div className="h-5 w-48 bg-gray-200 rounded"></div>
            <div className="h-4 w-96 bg-gray-200 rounded mt-2"></div>
          </div>

          <div className="grid grid-cols-2 gap-8">
            {[1, 2].map((i) => (
              <div key={i} className="flex flex-col space-y-2">
                <div className="h-4 w-32 bg-gray-200 rounded"></div>
                <div className="h-3 w-48 bg-gray-200 rounded"></div>
                <div className="h-10 w-full bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Wrap the main content in a separate component
const SettingsContent = (
  {
    /* ... props if needed ... */
  }
) => {
  const { is_admin } = useUser();
  const [formState, setFormState] = useState<SettingModel | null>(null);
  const [showToken, setShowToken] = useState(false);
  const [loading, setLoading] = useState(false);
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settings = await getSettings();
        setFormState(settings);
      } catch (error) {
        showAlert("Failed to load settings", "error");
      }
    };
    fetchSettings();
  }, []);

  const getFigmaApiKey = () => {
    const figmaSettings = formState?.integrations?.figma;
    return figmaSettings?.find((s) => s.name === "figma_api_key")?.value || "";
  };

  const handleInputChange =
    (section: string, key: string) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormState((prev) => ({
        ...prev,
        integrations: {
          ...prev?.integrations,
          figma: [
            {
              name: "figma_api_key",
              value: e.target.value,
              secure: true,
            },
          ],
        },
      }));
    };

  const handleSave = async () => {
    setLoading(true);
    try {
      if (formState) {
        await updateSettings(formState);
        showAlert("Settings saved successfully", "success");
      } else {
        showAlert("No settings to save", "error");
      }
    } catch (error) {
      showAlert("Failed to save settings", "error");
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (setting: SecureSettingValue) => {
    const value = setting.secure
      ? decryptStringClipboard(setting.value)
      : setting.value;
    navigator.clipboard.writeText(value);
    showAlert("Token copied to clipboard", "success");
  };

  const sections: SettingSection[] = [
    {
      title: "Integration Settings",
      description: "Manage your external service connections",
      settings: [
        {
          section: "integrations",
          key: "figma_api_key",
          label: "Figma Access Token",
          description:
            "Enter your Figma access token to enable design integrations",
          type: "text",
          defaultValue: "",
          value: getFigmaApiKey(),
        },
      ],
    },
  ];

  const renderSettingInput = (setting: SettingItem) => {
    switch (setting.type) {
      case "text":
        return (
          <div className="relative">
            <input
              type={showToken ? "text" : "password"}
              value={String(
                showToken ? getSettingValue(setting) : setting.value
              )}
              onChange={handleInputChange(setting.section, setting.key)}
              placeholder={`Enter your ${setting.label}`}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:typography-body-sm pr-20"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-2">
              <button
                type="button"
                onClick={() => setShowToken(!showToken)}
                className="text-gray-400 hover:text-gray-600"
              >
                {showToken ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
              {setting.section === "integrations" && getFigmaApiKey() && (
                <button
                  type="button"
                  onClick={() =>
                    copyToClipboard({
                      name: setting.key,
                      value: setting.value as string,
                      secure: setting.section === "integrations",
                    })
                  }
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const getSettingValue = (setting: SettingItem): string => {
    const value = String(setting.value);
    return setting.section === "integrations"
      ? decryptStringClipboard(value)
      : value;
  };

  if (!formState) {
    return <SettingsSkeleton />;
  }

  return (
    <div className="w-full max-w-6xl p-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="typography-heading-4 font-weight-semibold text-gray-900 -mt-2 mb-2">
            General Settings
          </h1>
          <p className="text-gray-500 mt-2">
            Manage your general settings and preferences
          </p>
        </div>
        <DynamicButton
          text="Save Changes"
          onClick={handleSave}
          loading={loading}
        />
      </div>

      <div className="space-y-6 overflow-auto">
        {sections.map((section) => (
          <div
            key={section.title}
            className="bg-white rounded-lg border border-gray-200 p-6"
          >
            <div className="mb-6">
              <h2 className="text-primary font-weight-medium">{section.title}</h2>
              <p className="typography-body-sm text-gray-600 mt-1">
                {section.description}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-8">
              {section.settings.map((setting) => (
                <div key={setting.key} className="flex flex-col space-y-2">
                  <label className="typography-body-sm font-weight-medium text-gray-700">
                    {setting.label}
                  </label>
                  {setting.description && (
                    <p className="typography-caption text-gray-500">
                      {setting.description}
                    </p>
                  )}
                  {renderSettingInput(setting)}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Main component with lazy loading
export default function SettingsPage() {
  return (
    <Suspense
      fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      }
    >
      <SettingsContent />
    </Suspense>
  );
}
