"use client";

import { useState, useEffect, useMemo } from "react";
import TableComponent from "@/components/SimpleTable/table";
import { Search, Users, UserCheck, Clock, User, RefreshCw, Eye, ArrowLeft, Copy, Check , Filter} from 'lucide-react';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { formatDateTime } from "@/utils/datetime";
import { getReferredUsers } from "@/utils/api";

const ReferredUsersTable = () => {
    const [allReferrals, setAllReferrals] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [detailedSearchTerm, setDetailedSearchTerm] = useState("");
    const [individualSearchTerm, setIndividualSearchTerm] = useState("");
    const [copied, setCopied] = useState(false);
    const [selectedOrganization, setSelectedOrganization] = useState("");


    const individualHeaders = [
        { key: 'referred_by', label: 'Referred By' },
        { key: 'referral_code', label: 'Referral Code' },
        { key: 'organization_id', label: 'Organization ID' },
        { key: 'organization_name', label: 'Organization Name' },
        { key: 'total_referrals', label: 'Total Referrals' },
        { key: 'actions', label: 'Actions' }
    ];

    const [selectedUser, setSelectedUser] = useState(null);


    const detailedReferralsHeaders = [
        { key: 'user_name', label: 'Referred User' },
        { key: 'status', label: 'Status' },
        { key: 'referred_at', label: 'Referred At' },
        { key: 'verified_at', label: 'Verified At' },
    ];

    const fetchReferredUsers = async () => {
        try {
            setLoading(true);
            setError(null);

            const data = await getReferredUsers("b2c");
            if (!data || !Array.isArray(data)) {
                throw new Error("Invalid response format");
            }

            const referrals = [];
            data.forEach(user => {
                const {
                    name,
                    referral_code,
                    organization_id,
                    organization,
                    referral_stats: { referral_history = [] } = {},
                } = user;

                referral_history.forEach(ref => {
                    referrals.push({
                        user_name: ref.user_name || "N/A",
                        referred_by: name || "N/A",
                        referral_code: referral_code || "N/A",
                        organization_id: organization_id || "N/A",
                        organization_name: organization?.name || "N/A",
                        status: ref.status || "pending",
                        referred_at: formatDateTime(ref.referred_at, true),
                        verified_at: ref.verified_at ? formatDateTime(ref.verified_at, true) : "--",
                    });
                });
            });

            setAllReferrals(referrals);
        } catch (err) {
            setError(err.message || "Failed to fetch referred users");
        } finally {
            setLoading(false);
        }
    };


    const handleCopy = () => {
        navigator.clipboard.writeText(selectedUser.referral_code);
        setCopied(true);
        setTimeout(() => setCopied(false), 5000);
    };


    const individualData = useMemo(() => {
        const userMap = new Map();

        allReferrals.forEach(referral => {
            const key = `${referral.referred_by}-${referral.referral_code}`;
            if (!userMap.has(key)) {
                userMap.set(key, {
                    referred_by: referral.referred_by,
                    referral_code: referral.referral_code,
                    organization_id: referral.organization_id,
                    organization_name: referral.organization_name,
                    total_referrals: 0,
                    referral_details: []
                });
            }

            const userData = userMap.get(key);
            userData.total_referrals += 1;
            userData.referral_details.push({
                user_name: referral.user_name,
                status: referral.status,
                referred_at: referral.referred_at,
                verified_at: referral.verified_at
            });
        });

        return Array.from(userMap.values()).map(user => ({
            ...user,
            actions: (
                <button
                    onClick={() => handleViewDetails(user)}
                    className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-700 text-sm font-medium rounded-md hover:bg-purple-200 transition-colors"
                >
                    <Eye className="w-4 h-4 mr-1" />
                    View Details
                </button>
            )
        }));
    }, [allReferrals]);

     const uniqueOrganizations = useMemo(() => {
        const orgSet = new Set();
        allReferrals.forEach(referral => {
            if (referral.organization_id && referral.organization_id !== "N/A") {
                orgSet.add(JSON.stringify({
                    id: referral.organization_id,
                    name: referral.organization_name
                }));
            }
        });
        return Array.from(orgSet).map(org => JSON.parse(org)).sort((a, b) => a.name.localeCompare(b.name));
    }, [allReferrals]);

    const handleViewDetails = (user) => {
        setSelectedUser(user);
        setDetailedSearchTerm("");
    };

    const handleBackToList = () => {
        setSelectedUser(null);
        setDetailedSearchTerm("");
    };

    useEffect(() => {
        fetchReferredUsers();
    }, []);

    // Filter logic for individual data
     const filteredIndividualData = useMemo(() => {
        return individualData.filter(row => {
            // Filter by organization if selected
            if (selectedOrganization && row.organization_id !== selectedOrganization) {
                return false;
            }
            
            // Filter by search term
            if (individualSearchTerm) {
                return Object.entries(row).some(([key, val]) => {
                    if (key === 'actions' || key === 'referral_details') return false;
                    return String(val).toLowerCase().includes(individualSearchTerm.toLowerCase());
                });
            }
            
            return true;
        });
    }, [individualData, individualSearchTerm, selectedOrganization])
    const filteredDetailedData = useMemo(() => {
        if (!selectedUser) return [];

        return selectedUser.referral_details.filter(row =>
            Object.entries(row).some(([key, val]) => {
                return String(val).toLowerCase().includes(detailedSearchTerm.toLowerCase());
            })
        );
    }, [selectedUser, detailedSearchTerm]);

    const stats = {
        total: allReferrals.length,
        verified: allReferrals.filter(r => r.status === 'verified').length,
        pending: allReferrals.filter(r => r.status !== 'verified').length,
        uniqueReferrers: individualData.length
    };

    const getSortableColumns = () => {
        return {
            referred_by: true,
            referral_code: true,
            organization_id: true,
            organization_name :true,
            total_referrals: true,
        };
    };

    const SkeletonLoader = () => {
        return (
            <div className="animate-pulse">
                <header className="bg-white shadow-sm border-b border-gray-200">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between h-16">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                                </div>
                                <div className="ml-3">
                                    <div className="h-6 bg-gray-300 rounded w-32 mb-1"></div>
                                    <div className="h-4 bg-gray-200 rounded w-40"></div>
                                </div>
                            </div>
                            <div className="h-10 bg-gray-300 rounded-lg w-24"></div>
                        </div>
                    </div>
                </header>

                <div className="bg-gray-50 min-h-screen">
                    <div className="flex justify-center pt-8 pb-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            {Array.from({ length: 4 }, (_, i) => (
                                <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse min-w-[250px]">
                                    <div className="flex items-center justify-between flex-row-reverse">
                                        <div className="w-8 h-8 bg-gray-200 rounded-full" />
                                        <div className="mr-4 flex-1">
                                            <div className="h-4 bg-gray-200 rounded mb-2 w-24" />
                                            <div className="h-6 bg-gray-200 rounded w-16" />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="flex justify-center px-6 pb-8">
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 w-full max-w-7xl p-6">
                            <div className="flex justify-start mb-5">
                                <div className="flex gap-3 flex-wrap">
                                    <div className="relative">
                                        <div className="h-10 bg-gray-300 rounded-md w-64"></div>
                                    </div>
                                </div>
                            </div>

                            <div className="overflow-hidden">
                                <div className="bg-gray-50 border-b border-gray-200">
                                    <div className="grid grid-cols-6 gap-4 px-6 py-3">
                                        {[...Array(6)].map((_, index) => (
                                            <div key={index} className="h-4 bg-gray-300 rounded"></div>
                                        ))}
                                    </div>
                                </div>

                                {[...Array(8)].map((_, rowIndex) => (
                                    <div key={rowIndex} className="border-b border-gray-100">
                                        <div className="grid grid-cols-6 gap-4 px-6 py-4">
                                            {[...Array(6)].map((_, colIndex) => (
                                                <div key={colIndex} className="h-4 bg-gray-200 rounded"></div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="flex items-center justify-between mt-6">
                                <div className="h-4 bg-gray-300 rounded w-32"></div>
                                <div className="flex space-x-2">
                                    {[...Array(5)].map((_, index) => (
                                        <div key={index} className="w-8 h-8 bg-gray-300 rounded"></div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const ErrorState = () => (
        <div className="flex flex-col justify-center items-center h-64">
            <div className="text-red-500 text-lg mb-2">Error loading data</div>
            <div className="text-gray-500 mb-4">{error}</div>
            <button
                onClick={fetchReferredUsers}
                className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
                Retry
            </button>
        </div>
    );

    if (loading) return <SkeletonLoader />;
    if (error) return <ErrorState />;

    return (
        <div className="p-6">
            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200 mb-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        <div className="flex items-center">
                            {selectedUser && (
                                <button
                                    onClick={handleBackToList}
                                    className="mr-3 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                                >
                                    <ArrowLeft className="w-5 h-5" />
                                </button>
                            )}
                            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                                <Users className="w-5 h-5 text-white" />
                            </div>
                            <div className="ml-3">
                                <h1 className="text-xl font-semibold text-gray-900">
                                    {selectedUser ? `${selectedUser.referred_by}'s Referrals` : 'Referred Users Dashboard'}
                                </h1>
                                <p className="text-sm text-gray-500">
                                    {selectedUser
                                        ? `Viewing detailed referrals for ${selectedUser.referred_by}`
                                        : 'Track referral user activities, view detailed user insights.'
                                    }
                                </p>
                            </div>
                        </div>
                        <button
                            onClick={fetchReferredUsers}
                            className="flex items-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors disabled:opacity-50"
                            disabled={loading}
                        >
                            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                            <span>Refresh</span>
                        </button>
                    </div>
                </div>
            </header>

            {/* Stats Cards */}
            {!selectedUser && (
                <div className="flex justify-center pb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {/* Total Referrals */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                                    <p className="text-2xl font-bold text-primary">{stats.total}</p>
                                    <p className="text-sm mt-1 flex items-center">📊 Sum of all referrals</p>
                                </div>
                                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                    <Users className="w-6 h-6 text-primary" />
                                </div>
                            </div>
                        </div>

                        {/* Verified Referrals */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Verified</p>
                                    <p className="text-2xl font-bold text-green-600">{stats.verified}</p>
                                    <p className="text-sm mt-1 flex items-center">🟢 Confirmed users</p>
                                </div>
                                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <UserCheck className="w-6 h-6 text-green-600" />
                                </div>
                            </div>
                        </div>

                        {/* Pending Referrals */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Pending</p>
                                    <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
                                    <p className="text-sm mt-1 flex items-center">🟡 Awaiting verification</p>
                                </div>
                                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <Clock className="w-6 h-6 text-yellow-600" />
                                </div>
                            </div>
                        </div>

                        {/* Unique Referrers */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Referrers</p>
                                    <p className="text-2xl font-bold text-purple-600">{stats.uniqueReferrers}</p>
                                    <p className="text-sm mt-1 flex items-center">👥 Active referrers</p>
                                </div>
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <User className="w-6 h-6 text-purple-600" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Main Content */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                {!selectedUser ? (
                    <>
                        {/* Search Bar */}
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                <input
                                    type="text"
                                    placeholder="Search referrers..."
                                    className="border border-gray-300 rounded-md p-2 pl-10 w-80"
                                    value={individualSearchTerm}
                                    onChange={(e) => setIndividualSearchTerm(e.target.value)}
                                />
                            </div>
                              <div className="relative">
                                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                <select
                                    value={selectedOrganization}
                                    onChange={(e) => setSelectedOrganization(e.target.value)}
                                    className="border border-gray-300 rounded-md py-2 pl-10 pr-8 min-w-[200px] focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none bg-white"
                                >
                                    <option value="">All Organizations</option>
                                    {uniqueOrganizations.map((org) => (
                                        <option key={org.id} value={org.id}>
                                            {org.name}
                                        </option>
                                    ))}
                                </select>
                               
                            </div>
                        </div>

                      

                        {/* Table Content */}
                        {filteredIndividualData.length > 0 ? (
                            <TableComponent
                                data={filteredIndividualData}
                                onRowClick={() => { }}
                                headers={individualHeaders}
                                sortableColumns={getSortableColumns()}
                                itemsPerPage={20}
                                onAction={() => { }}
                            />
                        ) : (
                            <EmptyStateView
                                type="noSearchResult"
                                message={individualSearchTerm ? "No referrers match your search" : "No referrers found"}
                            />
                        )}
                    </>
                ) : (
                    /* Detailed User Referrals View */
                    <>
                        <div className="mb-6">
                            <div className="bg-gray-50 rounded-lg p-4 mb-4">
                                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Referrer:</span>
                                        <p className="text-lg font-semibold text-gray-900">{selectedUser.referred_by}</p>
                                    </div>
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Referral Code:</span>
                                        <div className="flex items-center space-x-2">
                                            <p className="text-lg font-semibold text-gray-900">{selectedUser.referral_code}</p>
                                            <button
                                                onClick={handleCopy}
                                                className={`transition-all duration-300 rounded-full p-1 ${copied ? "bg-green-100 text-green-600" : "bg-gray-200 hover:bg-gray-300 text-gray-800"
                                                    }`}
                                                title={copied ? "Copied!" : "Copy Referral Code"}
                                            >
                                                {copied ? <Check size={18} /> : <Copy size={18} />}
                                            </button>
                                        </div>
                                    </div>
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Total Referrals:</span>
                                        <p className="text-lg font-semibold text-purple-600">{selectedUser.total_referrals}</p>
                                    </div>
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Organization id:</span>
                                        <p className="text-lg font-semibold text-purple-600">{selectedUser.organization_id}</p>
                                    </div>
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Organization Name:</span>
                                        <p className="text-lg font-semibold text-purple-600">{selectedUser.organization_name}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-between items-center mb-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                <input
                                    type="text"
                                    placeholder="Search referred users..."
                                    className="border border-gray-300 rounded-md p-2 pl-10 w-80"
                                    value={detailedSearchTerm}
                                    onChange={(e) => setDetailedSearchTerm(e.target.value)}
                                />
                            </div>
                        </div>

                        {filteredDetailedData ? (
                            <TableComponent
                                data={filteredDetailedData}
                                onRowClick={() => { }}
                                headers={detailedReferralsHeaders}
                                sortableColumns={{
                                    user_name: true,
                                    status: true,
                                    referred_at: true,
                                    verified_at: true,
                                }}
                                itemsPerPage={20}
                                onAction={() => { }}
                            />
                        ) : (
                            <EmptyStateView
                                type="noSearchResult"
                                message={detailedSearchTerm ? "No referred users match your search" : "No referred users found"}
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default ReferredUsersTable;