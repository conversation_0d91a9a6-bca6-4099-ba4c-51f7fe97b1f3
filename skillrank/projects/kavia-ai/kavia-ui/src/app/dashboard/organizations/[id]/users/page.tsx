 
/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import React, {
  useMemo,
  useState,
  useEffect,
  useContext,
  useRef,
  useCallback,
} from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import TableComponent from "@/components/SimpleTable/table";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import {
  Plus,
  Filter,
  Download,
  InfoIcon,
  Trash2,
  X,
  MoreVertical,
  MinusCircle,
  FileText,
  AlertCircle,
  CheckCircle,
  Users,
  Info,
  User,
  Loader2,
} from "lucide-react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Loader from "@/components/UserOnboarding/ui/Loader";
import AddAdminUser from "@/components/UserOnboarding/Modal/AddAdminUser";
import {
  fetchOrganizationUsers,
  addAdminUserToOrganization,
  promoteUserToAdmin,
  demoteUserFromAdmin,
  deleteAdminUserFromOrganization,
  getB2cUserCostPlan,
  addBulkUsers,
  createReferralCodeForUser,
  getDetailedReferralStats,
  getReferralUsageHistory,
  upgradeUserPlan,
} from "@/utils/api";
import { updateUserStatusById } from "@/utils/superApi";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { UserCredModal } from "@/components/UIComponents/Modals/UserCredModal";
import { useUser } from "@/components/Context/UserContext";
import DeleteConfirmationModal from "@/components/Modal/DeleteConfirmationModal";
import UpgradePlanModal from "@/components/UserOnboarding/Modal/UpgradePlanModal";
import xlsx from "json-as-xlsx";


interface User {
  id: string;
  name: string;
  email: string;
  contact_number: string;
  is_admin: boolean;
  status: string;
  cost: string;
  current_plan_name: string;
  remainingCredits: string;
  allocatedCredits: string;
  referral_code?: string;
  referral_stats?: {
    total_referrals: number;
    verified_referrals: number;
    active_referrals: number;
    last_referral_date: string | null;
  };
}
interface EnhancedReferralModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string | null;
  userName: string | null;
  userEmail: string | null;
}

interface SuccessModalState {
  show: boolean;
  inviteUrl: string;
  loginUrl: string;
}

interface FilterState {
  status: string;
  role: string;
}

interface UserImport {
  email: string;
  name: string;
  firstName: string;
  designation: string;
  department: string;
  raw: string;
  isValid: boolean;
}

interface ValidationError {
  line: number;
  messages: string[];
  content: string;
}

const EnhancedReferralModal: React.FC<EnhancedReferralModalProps> = ({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
}) => {
  const [stats, setStats] = useState<any>(null);
  const [history, setHistory] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    if (isOpen && userId) {
      fetchReferralData();
    }
  }, [isOpen, userId]);

  const fetchReferralData = async () => {
    try {
      setIsLoading(true);

      // Fetch both stats and history
      const [statsResponse, historyResponse] = await Promise.all([
        getDetailedReferralStats(userId!),
        getReferralUsageHistory(userId!, 20, 0),
      ]);

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      if (historyResponse.success) {
        setHistory(historyResponse.data.referral_history);
      }
    } catch (error) {
      console.error("Error fetching referral data:", error);
      showAlert("Failed to fetch referral data", "danger");
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Referral Analytics
              </h2>
              <p className="text-sm text-gray-600">
                {userName} ({userEmail})
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        {/* <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'overview'
                  ? 'border-b-2 border-primary text-primary'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Overview
            </button>
          
          </nav>
        </div> */}

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              {activeTab === "overview" && stats && (
                <div className="space-y-6">
                  {/* Stats Cards */}

                  {/* Referral Code Info */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">
                      Referral Code
                    </h3>
                    <div className="flex items-center space-x-4">
                      <code className="px-3 py-2 bg-white border rounded font-mono text-lg">
                        {stats.referral_code}
                      </code>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(stats.referral_code);
                          showAlert("Referral code copied!", "success");
                        }}
                        className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
                      >
                        Copy Code
                      </button>
                    </div>

                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const UsersPage = () => {
  const params = useParams();
  const { tenant_id } = useUser();

  // States
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isB2cOrg, setIsB2cOrg] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    status: "",
    role: "",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const { showAlert } = useContext(AlertContext);
  const [showBulkUserModel, setShowBulkUserModel] = useState(false);
  const [usersList, setUsersList] = useState("");
  const [selectedPlan, setSelectedPlan] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [parsedUsers, setParsedUsers] = useState<UserImport[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>(
    []
  );
  const [submissionResult, setSubmissionResult] = useState<{
    success: boolean;
    message: string;
    details?: string;
  } | null>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const lastCursorPosition = useRef(0);
  // Upgrade plan states
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [isUpgradePlanModalOpen, setIsUpgradePlanModalOpen] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);

  const [referralModalState, setReferralModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    userEmail: string | null;
    referralCode: string | null;
    stats: any;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    userEmail: null,
    referralCode: null,
    stats: null,
    isLoading: false,
  });

  const { id } = useParams();
  const router = useRouter();

  const [successModal, setSuccessModal] = useState<SuccessModalState>({
    show: false,
    inviteUrl: "",
    loginUrl: "",
  });

  const [deleteModalState, setDeleteModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    isLoading: false,
  });

  const [actionModalState, setActionModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userEmail: string | null;
    userName: string | null;
    isAdmin: boolean;
    position?: { top: number; left: number };
  }>({
    isOpen: false,
    userId: null,
    userEmail: null,
    userName: null,
    isAdmin: false,
  });

  const [promoteModalState, setPromoteModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    isLoading: false,
  });

  const [demoteModalState, setDemoteModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    isLoading: false,
  });

  const [statusModalState, setStatusModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userEmail: string | null;
    userName: string | null;
    currentStatus: string | null;
    newStatus: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userEmail: null,
    userName: null,
    currentStatus: null,
    newStatus: "",
    isLoading: false,
  });

  const plans = [
    { id: "price_1RWBTrCI2zbViAE2WZFApvc8", name: "Premium Starter" },
    { id: "price_1RIosGCI2zbViAE25Ny8rOkc", name: "Premium Advanced" },
    { id: "price_1RIozwCI2zbViAE2beuA7CDk", name: "Premium Pro" },
  ];

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const orgId = params.id as string;
      setIsB2cOrg(orgId === "b2c");
      const data = await fetchOrganizationUsers(orgId);
      let usersList = Array.isArray(data) ? data : [];
      if (orgId === "b2c") {
        const costPlan = await getB2cUserCostPlan();
        const costPlanArray = Array.isArray(costPlan?.users)
          ? costPlan?.users
          : [];

        usersList = (Array.isArray(data) ? data : []).map((user) => {
          const costInfo = costPlanArray.find(
            (plan: any) => plan.user_id === user.id
          );

          const parsedCost =
            costInfo?.cost !== undefined && costInfo?.cost !== null
              ? parseFloat(String(costInfo?.cost).replace("$", ""))
              : 0;

          return {
            ...user,
            cost: `$${parsedCost.toFixed(3)}`,
            current_plan_name:
              costInfo?.current_plan_name === "No Plan"
                ? "No current plan"
                : costInfo?.current_plan_name || "No current plan ",
            remainingCredits:
              costInfo?.credits - parsedCost * 20000 > 0
                ? Math.round(costInfo?.credits - parsedCost * 20000)
                : 0,
            allocatedCredits: costInfo?.credits || "No allocated credits ",
          };
        });
      }
      setUsers(usersList);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to fetch users"));
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [params.id]);

  const handleTextareaChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      lastCursorPosition.current = e.target.selectionStart;
      setUsersList(e.target.value);
    },
    []
  );

  // Add useEffect to restore focus and cursor position
  useEffect(() => {
    if (textareaRef.current && document.activeElement !== textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.setSelectionRange(
        lastCursorPosition.current,
        lastCursorPosition.current
      );
    }
  }, [parsedUsers, validationErrors]);

  // Email validation regex - comprehensive but reasonable
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // Process user input whenever it changes
  useEffect(() => {
    if (!usersList.trim()) {
      setParsedUsers([]);
      setValidationErrors([]);
      return;
    }

    const parseInput = () => {
      // Split by newlines to get individual user entries
      const lines = usersList.split("\n").filter((line) => line.trim());
      const errors: ValidationError[] = [];
      const users: UserImport[] = [];
      const emails = new Set(); // Track emails to detect duplicates

      lines.forEach((line, index) => {
        // Skip empty lines
        if (!line.trim()) return;

        // Try different delimiters: comma, tab, semicolon
        let parts;
        if (line.includes(",")) {
          parts = line.split(",").map((part) => part.trim());
        } else if (line.includes("\t")) {
          parts = line.split("\t").map((part) => part.trim());
        } else if (line.includes(";")) {
          parts = line.split(";").map((part) => part.trim());
        } else {
          // Assume space delimiter or single value (email only)
          parts = line.split(/\s+/).map((part) => part.trim());
        }

        // Extract information
        const email = parts[0]?.toLowerCase();
        const firstName = parts.length > 1 ? parts[1] : "";
        const designation = parts.length > 2 ? parts[2] : "User";
        const department = parts.length > 3 ? parts[3] : "General";

        // Full name construction
        const fullName =
          [firstName].filter(Boolean).join(" ").trim() || email?.split("@")[0];

        // Validation checks
        const validationIssues = [];

        // Check email format
        if (!email || !emailRegex.test(email)) {
          validationIssues.push(
            `Invalid email format: "${email || "missing"}"`
          );
        }

        // Check for duplicate emails
        if (email && emails.has(email)) {
          validationIssues.push(`Duplicate email: "${email}"`);
        } else if (email) {
          emails.add(email);
        }

        // Add to errors if any validation issues
        if (validationIssues.length > 0) {
          errors.push({
            line: index + 1,
            messages: validationIssues,
            content: line,
          });
        }

        users.push({
          email,
          name: fullName,
          firstName,
          designation,
          department,
          raw: line,
          isValid: validationIssues.length === 0,
        });
      });

      setParsedUsers(users);
      setValidationErrors(errors);
    };

    parseInput();
  }, [usersList]);

  const exportToExcel = (users: User[]) => {
    let data = [
      {
        sheet: "Users",
        columns: [
          { label: "User Id", value: "id" },
          { label: "Name", value: "name" },
          { label: "Email", value: "email" },
          {
            label: "Role",
            value: (row: any) => (row.role === true ? "Admin" : "User"),
          },
          { label: "Current Plan", value: "current_plan" },
          { label: "Allocated Credits", value: "allocated_credits" },
          { label: "Remainig Credits", value: "remaining_credits" },
          { label: "Usage Cost", value: "cost" },
          { label: "Status", value: "status" },
        ],
        content: users.map((user) => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.is_admin,
          current_plan: user.current_plan_name,
          allocated_credits: user.allocatedCredits,
          remaining_credits: user.remainingCredits,
          cost: user.cost,
          status: user.status,
        })),
      },
    ];

    let settings = {
      fileName: `Users_Data_${id}`,
    };

    xlsx(data, settings);
  };

  const openBulkModel = () => {
    setShowBulkUserModel(true);
    setUsersList("");
    setParsedUsers([]);
    setValidationErrors([]);
    setSelectedPlan("");
    setSubmissionResult(null);
  };

  const closeBulkModel = () => {
    setShowBulkUserModel(false);
    setUsersList("");
    setParsedUsers([]);
    setValidationErrors([]);
    setSelectedPlan("");
    setSubmissionResult(null);
  };

  const handleSubmitBulkUsers = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      validationErrors.length > 0 ||
      parsedUsers.length === 0 ||
      !selectedPlan
    ) {
      showAlert(
        "Please fix all validation errors and select a plan before submitting",
        "warning"
      );
      return;
    }

    setIsSubmitting(true);
    setSubmissionResult(null);

    try {
      // Filter out only valid users
      const validUsers = parsedUsers.filter((user) => user.isValid);

      // Transform to the expected API format
      const userData = {
        organization_id: params.id as string,
        users: validUsers.map((user) => ({
          email: user.email,
          name: user.name,
          department: user.department,
          designation: user.designation,
        })),
        plan_id: selectedPlan,
      };

      const response = await addBulkUsers({ userData });

      setSubmissionResult({
        success: true,
        message: `Successfully added ${validUsers.length} users`,
        details:
          response?.failed_details[0]?.reason ||
          `${validUsers.length} users were added successfully`,
      });

      // Refresh the user list to include the new users
      fetchUsers();

      // Clear form after successful submission
      setTimeout(() => {
        setUsersList("");
        setParsedUsers([]);
        setValidationErrors([]);
      }, 3000);
    } catch (error: any) {
      setSubmissionResult({
        success: false,
        message: "Failed to add users",
        details: error.message || "An unexpected error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClearForm = () => {
    setUsersList("");
    setParsedUsers([]);
    setValidationErrors([]);
    setSubmissionResult(null);
  };

  const handlePasteFromClipboard = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setUsersList(clipboardText);
    } catch (err) {
      showAlert(
        "Failed to read clipboard. Please check browser permissions.",
        "info"
      );
    }
  };

  const handleCreateReferralCode = async (
    userId: string,
    forceRegenerate = false
  ) => {
    try {
      const result = await createReferralCodeForUser(userId, forceRegenerate);

      if (result) {
        setUsers((prevUsers) =>
          prevUsers.map((user) =>
            user.id === userId
              ? {
                ...user,
                referral_code: result.referral_code,
                referral_stats: result.stats,
              }
              : user
          )
        );

        showAlert(
          `Referral code ${forceRegenerate ? "regenerated" : "created"
          } successfully!`,
          "success"
        );
        return result;
      }
    } catch (error) {
      showAlert("Failed to create referral code", "danger");
      console.error("Error creating referral code:", error);
    }
  };

  // Detect file drops
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];

      if (
        file.type === "text/csv" ||
        file.type === "text/plain" ||
        file.name.endsWith(".csv") ||
        file.name.endsWith(".txt")
      ) {
        const reader = new FileReader();
        reader.onload = (event) => {
          setUsersList(event.target?.result as string);
        };
        reader.readAsText(file);
      } else {
        showAlert("Please upload a CSV or TXT file", "warning");
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const BulkUserModel = () => {
    // Calculate validation stats
    const validCount = parsedUsers.filter((user) => user.isValid).length;
    const invalidCount = parsedUsers.length - validCount;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
        <div className="max-w-2xl w-full bg-white rounded-lg shadow-md overflow-hidden ">
          <div className="px-6 py-4 bg-white text-black flex items-center justify-between border-b">
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary" />
              <h1 className="typography-heading-4 font-weight-bold">
                Bulk User Onboarding
              </h1>
            </div>
            <button
              onClick={closeBulkModel}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          {submissionResult && (
            <div
              className={`mx-6 mt-4 p-3 rounded-lg ${submissionResult.success
                ? "bg-green-50 border border-green-200"
                : "bg-red-50 border border-red-200"
                }`}
            >
              <div className="flex items-start">
                {submissionResult.success ? (
                  <CheckCircle className="w-5 h-5 mr-2 text-green-500 mt-0.5" />
                ) : (
                  <AlertCircle className="w-5 h-5 mr-2 text-red-500 mt-0.5" />
                )}
                <div>
                  <p
                    className={`font-weight-medium ${submissionResult.success
                      ? "text-green-700"
                      : "text-red-700"
                      }`}
                  >
                    {submissionResult.message}
                  </p>
                  {submissionResult.details && (
                    <p className="typography-body-sm mt-1 text-gray-600">
                      {submissionResult.details}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
          <div className="max-h-[60vh] overflow-y-auto">
            <form
              onSubmit={handleSubmitBulkUsers}
              className="px-6 py-6 space-y-6"
            >
              <div className="">
                <div className="flex justify-between items-center mb-2">
                  <label className="block typography-body-sm font-weight-medium text-gray-700">
                    Paste Users List
                  </label>
                  <div className="flex gap-2">
                    <button
                      type="button"
                      onClick={handlePasteFromClipboard}
                      className="typography-caption bg-gray-100 hover:bg-gray-200 text-gray-600 py-1 px-2 rounded"
                    >
                      Paste from clipboard
                    </button>
                    {usersList && (
                      <button
                        type="button"
                        onClick={handleClearForm}
                        className="typography-caption bg-gray-100 hover:bg-gray-200 text-gray-600 py-1 px-2 rounded"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                </div>

                <div
                  className=" rounded-lg  transition-colors"
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                >
                  <textarea
                    ref={textareaRef}
                    value={usersList}
                    onChange={handleTextareaChange}
                    placeholder={`<EMAIL>, First Name, Last Name, Designation, Department\<EMAIL>, First Name, Last Name, Designation, Department`}
                    className="w-full px-3 py-2 text-gray-700 rounded-lg focus:outline-none h-32 resize-none"
                  />
                </div>

                <div className="flex justify-between mt-2 typography-caption text-gray-500">
                  <p>
                    Format: Email, First Name, Last Name, Designation,
                    Department (one user per line)
                  </p>
                  <div className="flex items-center">
                    <FileText className="w-4 h-4 mr-1" />
                    <span>Drop CSV file here</span>
                  </div>
                </div>

                <div className="flex items-center mt-2 typography-caption text-primary">
                  <Info className="w-4 h-4 mr-1" />
                  <p className=" typography-body-sm font-weight-medium ">
                    Supported format only CSV and text format not any JSON, XML
                    ,Excel files (.xlsx, .xls)
                  </p>
                </div>
              </div>

              {parsedUsers.length > 0 && (
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-weight-medium text-gray-700 typography-body-sm">
                      Preview
                    </h3>
                    <div className="flex gap-3">
                      {validCount > 0 && (
                        <span className="flex items-center typography-caption text-green-600">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {validCount} valid
                        </span>
                      )}
                      {invalidCount > 0 && (
                        <span className="flex items-center typography-caption text-red-600">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          {invalidCount} invalid
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="max-h-40 overflow-y-auto border rounded-lg">
                    {parsedUsers.map((user, index) => (
                      <div
                        key={index}
                        className={`flex items-start p-2 typography-body-sm ${index % 2 === 0 ? "bg-gray-50" : "bg-white"
                          } ${!user.isValid ? "border-l-2 border-red-500" : ""}`}
                      >
                        <div className="flex-1">
                          <div className="flex items-center">
                            <span
                              className={`font-weight-medium ${user.isValid ? "text-gray-700" : "text-red-600"
                                }`}
                            >
                              {user.email}
                            </span>
                            {user.isValid ? (
                              <CheckCircle className="w-3 h-3 ml-1 text-green-500" />
                            ) : (
                              <AlertCircle className="w-3 h-3 ml-1 text-red-500" />
                            )}
                          </div>
                          <div className="text-gray-500 typography-caption">
                            {user.name}
                            {user.department ? ` • ${user.department}` : ""}
                            {user.designation ? ` • ${user.designation}` : ""}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {validationErrors.length > 0 && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="font-weight-medium text-red-700 mb-2 flex items-center typography-body-sm">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        Validation Errors
                      </h4>
                      <ul className="typography-caption text-red-600 space-y-1 ml-5 list-disc">
                        {validationErrors.map((error, index) => (
                          <li key={index}>
                            Line {error.line}: {error.messages.join(", ")}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              <div>
                <label className="block typography-body-sm font-weight-medium text-gray-700 mb-2">
                  Select Plan
                </label>
                <select
                  value={selectedPlan}
                  onChange={(e) => setSelectedPlan(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                  required
                >
                  <option value="">Select a plan</option>
                  {plans.map((plan) => (
                    <option key={plan.id} value={plan.id}>
                      {plan.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={
                    validationErrors.length > 0 ||
                    parsedUsers.length === 0 ||
                    !selectedPlan ||
                    isSubmitting
                  }
                  className={`w-full px-4 py-3 text-white font-weight-medium rounded-md transition-colors duration-200 ${validationErrors.length > 0 ||
                    parsedUsers.length === 0 ||
                    !selectedPlan
                    ? "bg-gray-400 cursor-not-allowed"
                    : isSubmitting
                      ? "bg-primary"
                      : "bg-primary-600 hover:bg-primary-700"
                    }`}
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Adding Users...
                    </span>
                  ) : (
                    `Add Users`
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  };
  const handleAddUser = async (userData: {
    name: string;
    email: string;
    role: string;
    contact: string;
  }) => {
    try {
      const orgId = params.id as string;
      const data = {
        name: userData.name,
        email: userData.email,
        contact_number: userData.contact,
        organization_id: orgId,
        department: "Admin",
      };

      const response = await addAdminUserToOrganization(data);

      showAlert("User created successfully!", "success");
      setIsAddModalOpen(false);
      fetchUsers();
    } catch (error: any) {
      // Now this will work because we preserved the status code!
      if (error.status === 409 || error.response?.status === 409) {
        showAlert(
          "User with this email already exists in this organization.",
          "warning"
        );
      } else if (error.status === 400 || error.response?.status === 400) {
        const message =
          error.response?.data?.detail || "Invalid request parameters";
        showAlert(message, "danger");
      } else {
        showAlert("Failed to create user. Please try again.", "danger");
      }
    }
  };

  const getActionButtonItems = (status: string) => {
    switch (status) {
      case "active":
        return [
          { label: "Reinvite", onclick: () => { } },
          { label: "Suspend", onclick: () => { } },
          { label: "Deactivate", onclick: () => { } },
          { label: "Archive", onclick: () => { } },
        ];
      case "inactive":
        return [
          { label: "Activate", onclick: () => { } },
          { label: "Archive", onclick: () => { } },
        ];
      case "suspended":
        return [
          { label: "Activate", onclick: () => { } },
          { label: "Archive", onclick: () => { } },
        ];
      default:
        return [];
    }
  };





  const handleDelete = async (id: string) => {
    try {
      // Set loading state to true before starting the delete operation
      setDeleteModalState((prev) => ({ ...prev, isLoading: true }));

      const response = await deleteAdminUserFromOrganization(id);
      if (response.status === 200) {
        // Remove the user from the local state
        setUsers((prevUsers) =>
          prevUsers.map((user) => {
            if (user.id == id) {
              user.status = "Deleted";
            }
            return user;
          })
        );
        showAlert("User deleted successfully!", "success");
      } else {
        const errorData = await response.json();
        showAlert(errorData.detail || "Failed to delete user", "danger");
        throw new Error(errorData.detail);
      }
    } catch (error: any) {
      showAlert(
        error.message || "An error occurred while deleting the user",
        "danger"
      );
    } finally {
      // Reset modal state after operation completes (success or failure)
      setDeleteModalState({
        isOpen: false,
        userId: null,
        userName: null,
        isLoading: false,
      });
    }
  };




  const handlePromoteToAdmin = async (userId: string) => {
    try {
      setPromoteModalState((prev) => ({ ...prev, isLoading: true }));

      const result = await promoteUserToAdmin(params.id, userId);
      if (result) {
        showAlert("User promoted to admin successfully", "success");
        // Update the users list to reflect the change
        setUsers((prevUsers) =>
          prevUsers.map((user) =>
            user.id === userId ? { ...user, is_admin: true } : user
          )
        );
      }
    } catch (error: any) {
      showAlert(error.message || "Failed to promote user to admin", "danger");
    } finally {
      setPromoteModalState({
        isOpen: false,
        userId: null,
        userName: null,
        isLoading: false,
      });
      setActionModalState({
        isOpen: false,
        userId: null,
        userEmail: null,
        userName: null,
        isAdmin: false,
      });
    }
  };

  const handleDemoteFromAdmin = async (userId: string) => {
    try {
      setDemoteModalState((prev) => ({ ...prev, isLoading: true }));

      const result = await demoteUserFromAdmin(params.id, userId);
      if (result) {
        showAlert("User demoted from admin successfully", "success");
        // Update the users list to reflect the change
        setUsers((prevUsers) =>
          prevUsers.map((user) =>
            user.id === userId ? { ...user, is_admin: false } : user
          )
        );
      }
    } catch (error: any) {
      showAlert(error.message || "Failed to demote user from admin", "danger");
    } finally {
      setDemoteModalState({
        isOpen: false,
        userId: null,
        userName: null,
        isLoading: false,
      });
      setActionModalState({
        isOpen: false,
        userId: null,
        userEmail: null,
        userName: null,
        isAdmin: false,
      });
    }
  };

  const handleUpdateStatus = async (userId: string, newStatus: string) => {
    try {
      setStatusModalState((prev) => ({ ...prev, isLoading: true }));

      const orgId = params.id as string;
      const response = await updateUserStatusById(
        orgId,
        userId,
        newStatus.toLowerCase()
      );

      if (response) {
        showAlert(
          `User ${newStatus.toLowerCase() === "active" ? "activated" : "deactivated"
          } successfully`,
          "success"
        );
        // Instead of updating the UI state directly, fetch the updated user list from the server
        fetchUsers();
      }
    } catch (error: any) {
      showAlert(
        error.message ||
        `Failed to ${newStatus.toLowerCase() === "active" ? "activate" : "deactivate"
        } user`,
        "danger"
      );
    } finally {
      setStatusModalState({
        isOpen: false,
        userId: null,
        userEmail: null,
        userName: null,
        currentStatus: null,
        newStatus: "",
        isLoading: false,
      });
      setActionModalState({
        isOpen: false,
        userId: null,
        userEmail: null,
        userName: null,
        isAdmin: false,
      });
    }
  };

  // Upgrade plan functions
  const handleUserSelection = (userId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedUserId(userId);
    } else {
      setSelectedUserId("");
    }
  };

  const handleUpgradePlan = () => {
    if (!selectedUserId) {
      showAlert("Please select a user to upgrade", "info");
      return;
    }
    setIsUpgradePlanModalOpen(true);
  };

  const handleUpgradePlanSubmit = async (planId: string, planName: string, credits: number) => {
    if (!selectedUserId) return;

    try {
      setIsUpgrading(true);

      // Convert plan ID to the backend format
      let backendPlanName = "";
      switch (planId) {
        case "price_1RWBTrCI2zbViAE2WZFApvc8":
          backendPlanName = "premium_starter";
          break;
        case "price_1RIosGCI2zbViAE25Ny8rOkc":
          backendPlanName = "premium_advanced";
          break;
        case "price_1RIozwCI2zbViAE2beuA7CDk":
          backendPlanName = "premium_pro";
          break;
        default:
          throw new Error("Invalid plan selected");
      }

      await upgradeUserPlan(selectedUserId, backendPlanName, "b2c", credits);

      showAlert("User plan upgraded successfully!", "success");

      // Refresh the users list to show updated plan
      await fetchUsers();

      // Reset selection
      setSelectedUserId("");
      setIsUpgradePlanModalOpen(false);
    } catch (error: any) {
      showAlert(error.message || "Failed to upgrade user plan", "danger");
    } finally {
      setIsUpgrading(false);
    }
  };

  const getSelectedUser = () => {
    return users.find(user => user.id === selectedUserId) || null;
  };

  const handleViewReferralStats = (user: User) => {
    setReferralModalState({
      isOpen: true,
      userId: user.id,
      userName: user.name,
      userEmail: user.email,
      referralCode: null,
      stats: null,
      isLoading: false,
    });

    // Close the action modal
    setActionModalState({
      isOpen: false,
      userId: null,
      userEmail: null,
      userName: null,
      isAdmin: false,
    });
  };

  const filteredData = useMemo(() => {
    if (!Array.isArray(users)) {
      return [];
    }

    const filtered = users.filter((user) => {
      // Exclude deleted users (case insensitive)
      if (user.status?.toLowerCase() === "deleted") {
        return false;
      }

      const matchesSearch = searchTerm
        ? Object.values(user)
          .join(" ")
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
        : true;

      const matchesStatus = filters.status
        ? String(user.status).toLowerCase() === filters.status.toLowerCase()
        : true;

      const matchesRole = filters.role
        ? (user.is_admin && filters.role === "Admin") ||
        (!user.is_admin && filters.role === "User")
        : true;

      return matchesSearch && matchesStatus && matchesRole;
    });

    return filtered.map((user) => ({
      ...user,
      ...(isB2cOrg ? {

        select:true,
      } : {}),
      role: (
        <span
          className={`px-2 py-1 rounded-full typography-body-sm ${user.is_admin
            ? "bg-primary-100 text-primary-800"
            : "bg-gray-100 text-gray-800"
            }`}
        >
          {user.is_admin ? "Admin" : "User"}
        </span>
      ),
      status: String(user.status),
      menuActions: (
        <div className="flex gap-2 relative p-1">
          {/* <DynamicButton
            variant="secondary"
            icon={Download}
            text=""
            onClick={(e) => {
              e.stopPropagation();
              const setPasswordUrl = `${window.location.protocol}//${window.location.host}/users/set_password?tenant_id=${encodeURIComponent(String(params.id))}&email=${encodeURIComponent(user.email)}`;
              const loginUrl = `${window.location.protocol}//${window.location.host}/users/login?tenant_id=${encodeURIComponent(String(params.id))}&email=${encodeURIComponent(user.email)}`;
              setSuccessModal({ show: true, inviteUrl: setPasswordUrl, loginUrl: loginUrl });
            }}
          /> */}
          {/* <UserActionMenu user={user} /> */}
          <button
            className="p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={(e) => {
              e.stopPropagation();
              if (
                actionModalState.isOpen &&
                actionModalState.userId === user.id
              ) {
                setActionModalState({
                  isOpen: false,
                  userId: null,
                  userEmail: null,
                  userName: null,
                  isAdmin: false,
                });
              } else {
                const buttonRect = e.currentTarget.getBoundingClientRect();
                setActionModalState({
                  isOpen: true,
                  userId: user.id,
                  userEmail: user.email,
                  userName: user.name,
                  isAdmin: user.is_admin,
                  position: {
                    top:
                      buttonRect.top +
                      buttonRect.height / 2 +
                      window.scrollY -
                      20,
                    left: buttonRect.left + window.scrollX - 192,
                  },
                });
              }
            }}
          >
            {actionModalState.isOpen && actionModalState.userId === user.id ? (
              <X className="w-5 h-5 text-gray-500" />
            ) : (
              <MoreVertical className="w-5 h-5 text-gray-500" />
            )}
          </button>
        </div>
      ),
    }));
  }, [users, searchTerm, filters, actionModalState]);

  const routeToUserCost = (
    e: React.MouseEvent<HTMLAnchorElement>,
    userId: string
  ) => {
    e.stopPropagation();
    router.push(`/dashboard/organizations/${id}/users/${userId}/costs`);
  };

  const renderContent = () => {
    if (isLoading) {
      return <Loader type="table" />;
    }

    if (error) {
      return (
        <div className="text-center py-8 text-red-600">
          Error loading users: {error.message}
        </div>
      );
    }

    if (filteredData.length === 0) {
      if (searchTerm) {
        return (
          <EmptyStateView
            type="noSearchResults"
            onClick={() => setSearchTerm("")}
          />
        );
      }
      if (filters.status || filters.role) {
        return (
          <EmptyStateView
            type="noFilterResults"
            onClick={() => setFilters({ status: "", role: "" })}
          />
        );
      }
      return (
        <EmptyStateView
          type="noUsers"
          onClick={() => setIsAddModalOpen(true)}
        />
      );
    }

    const headers = [
      ...(isB2cOrg ? [{ key: "select", label: "" }] : []),
      { key: "name", label: "NAME" },
      { key: "email", label: "EMAIL" },
      { key: "role", label: "ROLE" },
      // { key: "contact_number", label: "CONTACT" },
      ...(isB2cOrg
        ? [
          { key: "current_plan_name", label: "CURRENT PLAN" },
          { key: "allocatedCredits", label: "ALLOCATED CREDITS" },
          { key: "remainingCredits", label: "REMAINING CREDITS" },
          { key: "cost", label: "USAGE COST", onClick: routeToUserCost },
        ]
        : []),
      { key: "status", label: "STATUS" },
      { key: "menuActions", label: "", actionLabel: "" },
    ];

    return (
      <TableComponent
        headers={headers}
        data={filteredData}
        onRowClick={(id) => { }}
        onCheckboxChange={(id, checked) => handleUserSelection(id, checked)}

        sortableColumns={{
          ...(isB2cOrg ? { select: false } : {}),
          name: true,
          email: true,
          role: true,
          contact_number: true,
          current_plan_name: true,
          allocatedCredits: true,
          remainingCredits: true,
          cost: true,
          status: true,
        }}
        itemsPerPage={10}
      />
    );
  };

  return (
    <div className="bg-custom-bg-primary rounded-lg shadow -mx-3">
      <div className="py-3 border-b border-custom-border flex justify-between items-center">
        <Search searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
        <div className="flex gap-3">
          {isB2cOrg ? (
            <div className="flex gap-3">
              <DynamicButton
                variant={selectedUserId ? "primary" : "secondary"}
                icon={User}
                text="Upgrade Plan"
                onClick={handleUpgradePlan}
                disabled={!selectedUserId}
              />
              <DynamicButton
                variant="primary"
                icon={FileText}
                text="Export to excel"
                onClick={() =>
                  exportToExcel(
                    users.filter((user) => user.status != "deleted")
                  )
                }
                disabled={users ? users.length == 0 : true}
              />
              <DynamicButton
                variant="primary"
                icon={Plus}
                text="Add Bulk User"
                onClick={openBulkModel}
              />
            </div>
          ) : null}
          <DynamicButton
            variant="primary"
            icon={Plus}
            text="Add User"
            onClick={() => setIsAddModalOpen(true)}
          />
          <div className="relative">
            <DynamicButton
              variant="secondary"
              icon={Filter}
              text="Filter"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            />
            {isFilterOpen && (
              <FilterDropdown
                filters={filters}
                setFilters={setFilters}
                onClose={() => setIsFilterOpen(false)}
              />
            )}
          </div>
        </div>
      </div>

      <EnhancedReferralModal
        isOpen={referralModalState.isOpen}
        onClose={() =>
          setReferralModalState({
            isOpen: false,
            userId: null,
            userName: null,
            userEmail: null,
            referralCode: null,
            stats: null,
            isLoading: false,
          })
        }
        userId={referralModalState.userId}
        userName={referralModalState.userName}
        userEmail={referralModalState.userEmail}
      />

      {renderContent()}
      <AddAdminUser
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddUser}
      />
      <UserCredModal
        isOpen={successModal.show}
        title="User Credentials"
        message=""
        icon={InfoIcon}
        inviteUrl={successModal.inviteUrl}
        loginUrl={successModal.loginUrl}
        onClose={() =>
          setSuccessModal({ show: false, inviteUrl: "", loginUrl: "" })
        }
      />
      <DeleteConfirmationModal
        isOpen={deleteModalState.isOpen}
        onClose={() =>
          setDeleteModalState({
            isOpen: false,
            userId: null,
            userName: null,
            isLoading: false,
          })
        }
        onConfirm={() =>
          deleteModalState.userId && handleDelete(deleteModalState.userId)
        }
        title="Delete Admin User"
        message={`Are you sure you want to delete ${deleteModalState.userName}? This action cannot be undone.`}
        isLoading={deleteModalState.isLoading}
      />
      {actionModalState.isOpen && (
        <div
          className="fixed z-50 bg-white rounded-lg shadow-lg w-56"
          style={{
            top: actionModalState.position?.top,
            left: actionModalState.position?.left,
          }}
        >
          <button
            className="absolute -top-2 -right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
            onClick={() =>
              setActionModalState({
                isOpen: false,
                userId: null,
                userEmail: null,
                userName: null,
                isAdmin: false,
              })
            }
          >
            <X className="w-3 h-3 text-gray-500" />
          </button>
          <div className="p-2 space-y-1">
            {(() => {
              const currentUser = users.find(u => u.id === actionModalState.userId);
              const hasReferralCode = Boolean(currentUser?.referral_code);

              return (
                <>
                  {/* Create Referral Code Button - Only show if user doesn't have referral code */}
                  {!hasReferralCode && (
                    <button
                      className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      onClick={async () => {
                        try {
                          if (actionModalState.userId) {
                            const result = await handleCreateReferralCode(
                              actionModalState.userId,
                              false
                            );
                            if (result) {
                              showAlert(
                                `Referral code created successfully! Code: ${result.referral_code}`,
                                "success"
                              );
                            }
                          }
                          setActionModalState({
                            isOpen: false,
                            userId: null,
                            userEmail: null,
                            userName: null,
                            isAdmin: false,
                          });
                        } catch (error: any) {
                          showAlert(
                            error.message || "Failed to create referral code",
                            "danger"
                          );
                        }
                      }}
                    >
                      <svg
                        className="w-4 h-4 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                      <span>Create Referral Code</span>
                    </button>
                  )}

                  {/* View Referral Code Button - Only show if user has referral code */}
                  {hasReferralCode && (
                    <button
                      className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      onClick={() => {
                        setReferralModalState({
                          isOpen: true,
                          userId: actionModalState.userId,
                          userName: actionModalState.userName,
                          userEmail: actionModalState.userEmail,
                          referralCode: null,
                          stats: null,
                          isLoading: false,
                        });

                        setActionModalState({
                          isOpen: false,
                          userId: null,
                          userEmail: null,
                          userName: null,
                          isAdmin: false,
                        });
                      }}
                    >
                      <svg
                        className="w-4 h-4 text-purple-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        />
                      </svg>
                      <span>View Referral Code</span>
                    </button>
                  )}

            {/* Regenerate Referral Code Button - Only show if user has referral code */}
            {hasReferralCode && (
              <button
                className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                onClick={async () => {
                  try {
                    if (actionModalState.userId) {
                      const result = await handleCreateReferralCode(
                        actionModalState.userId,
                        true // Force regenerate
                      );
                      if (result) {
                        showAlert(
                          `Referral code regenerated successfully! New code: ${result.referral_code}`,
                          "success"
                        );
                      }
                    }
                    setActionModalState({
                      isOpen: false,
                      userId: null,
                      userEmail: null,
                      userName: null,
                      isAdmin: false,
                    });
                  } catch (error: any) {
                    showAlert(
                      error.message || "Failed to regenerate referral code",
                      "danger"
                    );
                  }
                }}
              >
                <svg
                  className="w-4 h-4 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                <span>Regenerate Code</span>
              </button>
            )}
          </>
        );
      })()}

            {/* Rest of your existing buttons remain the same */}
            <button
              className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
              onClick={(e) => {
                e.stopPropagation();
                const setPasswordUrl = `${window.location.protocol}//${window.location.host
                  }/users/set_password?tenant_id=${encodeURIComponent(
                    String(params.id)
                  )}&email=${encodeURIComponent(actionModalState.userEmail!)}`;
                const loginUrl = `${window.location.protocol}//${window.location.host
                  }/users/login?tenant_id=${encodeURIComponent(
                    String(params.id)
                  )}&email=${encodeURIComponent(actionModalState.userEmail!)}`;
                setSuccessModal({
                  show: true,
                  inviteUrl: setPasswordUrl,
                  loginUrl: loginUrl,
                });
                setActionModalState({
                  isOpen: false,
                  userId: null,
                  userEmail: null,
                  userName: null,
                  isAdmin: false,
                });
              }}
            >
              <Download className="w-4 h-4 text-gray-500" />
              <span>Download</span>
            </button>

      {!actionModalState.isAdmin && (
        <button
          className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
          onClick={() => {
            setPromoteModalState({
              isOpen: true,
              userId: actionModalState.userId,
              userName: actionModalState.userName,
              isLoading: false,
            });
            setActionModalState({
              isOpen: false,
              userId: null,
              userEmail: null,
              userName: null,
              isAdmin: false,
            });
          }}
        >
          <svg
            className="w-4 h-4 text-primary"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>Promote to Admin</span>
        </button>
      )}

            {actionModalState.isAdmin && (
              <button
                className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                onClick={() => {
                  setDemoteModalState({
                    isOpen: true,
                    userId: actionModalState.userId,
                    userName: actionModalState.userName,
                    isLoading: false,
                  });
                  setActionModalState({
                    isOpen: false,
                    userId: null,
                    userEmail: null,
                    userName: null,
                    isAdmin: false,
                  });
                }}
              >
                <MinusCircle className="w-4 h-4 text-primary" />
                <span>Demote Admin</span>
              </button>
            )}

            {users
              .find((user) => user.id === actionModalState.userId)
              ?.status?.toLowerCase() === "active" ? (
              <button
                className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                onClick={() => {
                  const user = users.find(
                    (u) => u.id === actionModalState.userId
                  );
                  setStatusModalState({
                    isOpen: true,
                    userId: actionModalState.userId,
                    userEmail: actionModalState.userEmail,
                    userName: actionModalState.userName,
                    currentStatus: user?.status || null,
                    newStatus: "Inactive",
                    isLoading: false,
                  });
                  setActionModalState({
                    isOpen: false,
                    userId: null,
                    userEmail: null,
                    userName: null,
                    isAdmin: false,
                  });
                }}
              >
                <svg
                  className="w-4 h-4 text-amber-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                  />
                </svg>
                <span>Deactivate User</span>
              </button>
            ) : (
              <button
                className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                onClick={() => {
                  const user = users.find(
                    (u) => u.id === actionModalState.userId
                  );
                  setStatusModalState({
                    isOpen: true,
                    userId: actionModalState.userId,
                    userEmail: actionModalState.userEmail,
                    userName: actionModalState.userName,
                    currentStatus: user?.status || null,
                    newStatus: "Active",
                    isLoading: false,
                  });
                  setActionModalState({
                    isOpen: false,
                    userId: null,
                    userEmail: null,
                    userName: null,
                    isAdmin: false,
                  });
                }}
              >
                <svg
                  className="w-4 h-4 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>Activate User</span>
              </button>
            )}

            <button
              className="w-full px-3 py-2 text-red-600 hover:bg-gray-100 flex items-center gap-2"
              onClick={() => {
                setDeleteModalState({
                  isOpen: true,
                  userId: actionModalState.userId,
                  userName: actionModalState.userName,
                  isLoading: false,
                });
                setActionModalState({
                  isOpen: false,
                  userId: null,
                  userEmail: null,
                  userName: null,
                  isAdmin: false,
                });
              }}
            >
              <Trash2 className="w-4 h-4 text-red-500" />
              <span>Delete User</span>
            </button>
          </div>
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={promoteModalState.isOpen}
        onClose={() =>
          setPromoteModalState({
            isOpen: false,
            userId: null,
            userName: null,
            isLoading: false,
          })
        }
        onConfirm={() =>
          promoteModalState.userId &&
          handlePromoteToAdmin(promoteModalState.userId)
        }
        title="Promote to Admin"
        message={`Are you sure you want to promote ${promoteModalState.userName || "this user"
          } to admin? This will give them full administrative privileges.`}
        isLoading={promoteModalState.isLoading}
        confirmText="Promote"
        confirmButtonClass="bg-primary-600 hover:bg-primary-700"
      />
      <DeleteConfirmationModal
        isOpen={demoteModalState.isOpen}
        onClose={() =>
          setDemoteModalState({
            isOpen: false,
            userId: null,
            userName: null,
            isLoading: false,
          })
        }
        onConfirm={() =>
          demoteModalState.userId &&
          handleDemoteFromAdmin(demoteModalState.userId)
        }
        title="Demote from Admin"
        message={`Are you sure you want to demote ${demoteModalState.userName || "this user"
          } from admin? This will remove their administrative privileges.`}
        isLoading={demoteModalState.isLoading}
        confirmText="Demote"
        confirmButtonClass="bg-primary-600 hover:bg-primary-700"
      />
      {/* Add Status Change Modal */}
      <DeleteConfirmationModal
        isOpen={statusModalState.isOpen}
        onClose={() =>
          setStatusModalState({
            isOpen: false,
            userId: null,
            userEmail: null,
            userName: null,
            currentStatus: null,
            newStatus: "",
            isLoading: false,
          })
        }
        onConfirm={() =>
          statusModalState.userId &&
          handleUpdateStatus(
            statusModalState.userId,
            statusModalState.newStatus
          )
        }
        title={
          statusModalState.newStatus === "Active"
            ? "Activate User"
            : "Deactivate User"
        }
        message={`Are you sure you want to ${statusModalState.newStatus === "Active" ? "activate" : "deactivate"
          } ${statusModalState.userName || "this user"}?`}
        isLoading={statusModalState.isLoading}
        confirmText={
          statusModalState.newStatus === "Active" ? "Activate" : "Deactivate"
        }
        confirmButtonClass={
          statusModalState.newStatus === "Active"
            ? "bg-green-600 hover:bg-green-700"
            : "bg-amber-600 hover:bg-amber-700"
        }
      />

      {/* Upgrade Plan Modal */}
      <UpgradePlanModal
        isOpen={isUpgradePlanModalOpen}
        onClose={() => setIsUpgradePlanModalOpen(false)}
        onUpgrade={handleUpgradePlanSubmit}
        selectedUser={getSelectedUser()}
        isLoading={isUpgrading}
        fetchUsers={fetchUsers}
      />

      {showBulkUserModel && <BulkUserModel />}
    </div>
  );
};

// Filter Dropdown Component
interface FilterDropdownProps {
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  onClose: () => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  filters,
  setFilters,
  onClose,
}) => {
  const handleClickOutside = (e: MouseEvent) => {
    if (!(e.target as Element).closest(".filter-dropdown")) {
      onClose();
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50 py-2 filter-dropdown">
      <div className="px-4 py-2">
        <h3 className="typography-body-sm font-weight-medium text-gray-900">
          Status
        </h3>
        <div className="mt-2 space-y-2">
          {["Active", "Inactive"].map((status) => (
            <label key={status} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.status === status}
                onChange={() =>
                  setFilters({
                    ...filters,
                    status: status === filters.status ? "" : status,
                  })
                }
                className="rounded border-gray-300"
              />
              <span className="ml-2 typography-body-sm text-gray-700">
                {status}
              </span>
            </label>
          ))}
        </div>
      </div>

      <div className="px-4 py-2 border-t">
        <h3 className="typography-body-sm font-weight-medium text-gray-900">
          Role
        </h3>
        <div className="mt-2 space-y-2">
          {["Admin", "User"].map((role) => (
            <label key={role} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.role === role}
                onChange={() =>
                  setFilters({
                    ...filters,
                    role: role === filters.role ? "" : role,
                  })
                }
                className="rounded border-gray-300"
              />
              <span className="ml-2 typography-body-sm text-gray-700">
                {role}
              </span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UsersPage;