import React from 'react'

export const TabSelector = ({
    activeView,
    onViewChange
  }: {
    activeView: 'drafts' | 'announcements';
    onViewChange: (view: 'drafts' | 'announcements') => void;
  }) => (
    <div className="border-b border-gray-200 mb-6">
      <nav className="-mb-px flex gap-6" aria-label="Tabs">
        <button
          onClick={() => onViewChange('drafts')}
          className={`pb-4 px-1 border-b-2 font-weight-medium typography-body-sm ${
            activeView === 'drafts'
              ? 'border-primary text-primary'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Saved Drafts
        </button>
        <button
          onClick={() => onViewChange('announcements')}
          className={`pb-4 px-1 border-b-2 font-weight-medium typography-body-sm ${
            activeView === 'announcements'
              ? 'border-primary text-primary'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Announcements
        </button>
      </nav>
    </div>
  );