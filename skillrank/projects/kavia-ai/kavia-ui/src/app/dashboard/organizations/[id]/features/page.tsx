// src/app/dashboard/organizations/[id]/features/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Info } from 'lucide-react';
import { Toggle } from "@/components/UserOnboarding/ui/Toggle";
import { fetchOrganization } from "@/utils/api";
import Loader from "@/components/UserOnboarding/ui/Loader"


interface Organization {
  configurations: {
    max_users: number;
    role_customization: boolean;
    api_access: boolean;
    github_integration: boolean;
    jira_integration: boolean;
    custom_reports: boolean;
    export_capabilities: boolean;
  };
  id: string;
}

interface PageProps {
  params: {
    id: string;
  }
}

const FeaturesPage = ({ params }: PageProps) => {
  const organizationId = params.id;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [enableAllFeatures, setEnableAllFeatures] = useState(false);
  const [disableOptional, setDisableOptional] = useState(false);
  const [maxUsers, setMaxUsers] = useState(2);
  const [featureToggles, setFeatureToggles] = useState({
    userManagement: {
      enabled: true,
      roleCustomization: false,
    },
    integrationHub: {
      enabled: true,
      apiAccess: false,
      github: false,
      jira: false,
      apiAccessRight: false,
      githubRight: false,
      jiraRight: false,
    },
    analyticsBoard: {
      enabled: true,
      customReports: false,
      exportCapabilities: false,
    }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const data = await fetchOrganization(organizationId);
        
        // Update maxUsers
        setMaxUsers(data.configurations.max_users);

        // Update feature toggles based on API response
        setFeatureToggles({
          userManagement: {
            enabled: true,
            roleCustomization: data.configurations.role_customization,
          },
          integrationHub: {
            enabled: true,
            apiAccess: data.configurations.api_access,
            github: data.configurations.github_integration,
            jira: data.configurations.jira_integration,
            apiAccessRight: data.configurations.api_access,
            githubRight: data.configurations.github_integration,
            jiraRight: data.configurations.jira_integration,
          },
          analyticsBoard: {
            enabled: true,
            customReports: data.configurations.custom_reports,
            exportCapabilities: data.configurations.export_capabilities,
          }
        });

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch organization data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [organizationId]);

  if (isLoading) {
    return (
      <Loader type="features" />
    );
  }

  if (error) {
    return (
      <div className="text-red-600 text-center p-4">
        Error loading organization features: {error}
      </div>
    );
  }

  return (
    <div className="w-full max-w-[1200px]">
      {/* Page Header */}
      <div className="mb-6 relative">
        <h1 className="typography-heading-4 font-weight-semibold">Feature Configuration</h1>
        <p className="text-gray-600 mt-1">Manage organization features and their settings</p>
      </div>

      {/* Wrap all feature sections in a relative container */}
      <div className="relative">
        {/* Coming Soon Watermark - Covers all feature sections */}
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 z-10">
          <span className="typography-heading-4 font-weight-semibold text-gray-400 rotate-[-30deg]">Feature Coming Soon</span>
        </div>

        {/* Global Toggles */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <span className="block mb-2 typography-body-sm text-gray-600">
              Enable all available features
            </span>
            <Toggle
              enabled={enableAllFeatures}
              onChange={setEnableAllFeatures}
              size="small"
            />
          </div>
          <div>
            <span className="block mb-2 typography-body-sm text-gray-600">
              Disable optional features
            </span>
            <Toggle
              enabled={disableOptional}
              onChange={setDisableOptional}
              size="small"
            />
          </div>
        </div>

        {/* Feature Sections */}
        <div className="space-y-6 overflow-auto">
          {/* User Management Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <span className="text-primary font-weight-medium">User Management</span>
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
              </div>
              <Toggle
                enabled={featureToggles.userManagement.enabled}
                onChange={(enabled) => setFeatureToggles(prev => ({
                  ...prev,
                  userManagement: { ...prev.userManagement, enabled }
                }))}
                size="small"
              />
            </div>

            <div className="grid grid-cols-2 gap-8">
              <div>
                <span className="block mb-2 typography-body-sm text-gray-600">Maximum users</span>
                <input
                  type="number"
                  value={maxUsers}
                  onChange={(e) => setMaxUsers(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  min={1}
                />
              </div>
              <div>
                <span className="block mb-2 typography-body-sm text-gray-600">Role customization</span>
                <Toggle
                  enabled={featureToggles.userManagement.roleCustomization}
                  onChange={(enabled) => setFeatureToggles(prev => ({
                    ...prev,
                    userManagement: { ...prev.userManagement, roleCustomization: enabled }
                  }))}
                  size="small"
                />
              </div>
            </div>
          </div>

          {/* Integration Hub Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <span className="text-primary font-weight-medium">Integration Hub</span>
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
              </div>
              <Toggle
                enabled={featureToggles.integrationHub.enabled}
                onChange={(enabled) => setFeatureToggles(prev => ({
                  ...prev,
                  integrationHub: { ...prev.integrationHub, enabled }
                }))}
                size="small"
              />
            </div>

            <div className="grid grid-cols-2 gap-8">
              <div className="space-y-4">
                {['API access', 'Jira'].map((item, index) => (
                  <div key={index}>
                    <span className="block mb-2 typography-body-sm text-gray-600">{item}</span>
                    <Toggle
                      enabled={featureToggles.integrationHub[item.toLowerCase().replace(' ', '') as keyof typeof featureToggles.integrationHub]}
                      onChange={(enabled) => setFeatureToggles(prev => ({
                        ...prev,
                        integrationHub: { 
                          ...prev.integrationHub, 
                          [item.toLowerCase().replace(' ', '')]: enabled 
                        }
                      }))}
                      size="small"
                    />
                  </div>
                ))}
              </div>
              <div className="space-y-4">
                {[ 'Github'].map((item, index) => (
                  <div key={index}>
                    <span className="block mb-2 typography-body-sm text-gray-600">{item}</span>
                    <Toggle
                      enabled={featureToggles.integrationHub[`${item.toLowerCase().replace(' ', '')}Right` as keyof typeof featureToggles.integrationHub]}
                      onChange={(enabled) => setFeatureToggles(prev => ({
                        ...prev,
                        integrationHub: { 
                          ...prev.integrationHub, 
                          [`${item.toLowerCase().replace(' ', '')}Right`]: enabled 
                        }
                      }))}
                      size="small"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Analytics Dashboard Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <span className="text-primary font-weight-medium">Analytics Dashboard</span>
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
              </div>
              <Toggle
                enabled={featureToggles.analyticsBoard.enabled}
                onChange={(enabled) => setFeatureToggles(prev => ({
                  ...prev,
                  analyticsBoard: { ...prev.analyticsBoard, enabled }
                }))}
                size="small"
              />
            </div>

            <div className="grid grid-cols-2 gap-8">
              <div>
                <span className="block mb-2 typography-body-sm text-gray-600">Custom reports</span>
                <Toggle
                  enabled={featureToggles.analyticsBoard.customReports}
                  onChange={(enabled) => setFeatureToggles(prev => ({
                    ...prev,
                    analyticsBoard: { ...prev.analyticsBoard, customReports: enabled }
                  }))}
                  size="small"
                />
              </div>
              <div>
                <span className="block mb-2 typography-body-sm text-gray-600">Export capabilities</span>
                <Toggle
                  enabled={featureToggles.analyticsBoard.exportCapabilities}
                  onChange={(enabled) => setFeatureToggles(prev => ({
                    ...prev,
                    analyticsBoard: { ...prev.analyticsBoard, exportCapabilities: enabled }
                  }))}
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesPage;