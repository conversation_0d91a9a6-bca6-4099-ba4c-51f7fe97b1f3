import React, { useState, useRef, useEffect, useContext } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { FaTimes, FaCodeBranch, FaTasks, FaFileCode, FaChartLine, FaCode, } from "react-icons/fa";
import { Loader2, ChevronLeft, ChevronRight, FileText } from 'lucide-react';
import CodeDiscussionPanel from "@/components/CodeDiscussionPanel";
import DocumentsPanel from "@/components/CodeGenrationPanel/DocumentsPanel/DocumentsPanel";
import FunctionCallsComponent from "@/components/CodeGenrationPanel/FunctionCallPanel/FunctionCallPanel";
import StepsComponent from "@/components/CodeGenrationPanel/StepsComponent";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import TerminalPanel from "@/components/CodeGenrationPanel/TerminalPanel/TerminalPanel";
import StatusPanel from "@/components/CodeGenrationPanel/StatusPanel/StatusPanel";
import CodeViewPanel from "@/components/CodeGenrationPanel/CodeViewPanel/CodeViewPanel";
import DebugPanel from "@/components/CodeGenrationPanel/DebugPanel/DebugPanel";
import CodeStreamViewer from "@/components/CodeGenrationPanel/CodeStreamViewer";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { stopBatchTask } from "@/utils/api";
import ExistingInfo from "@/components/CodeGenrationPanel/ExistingInfoTab";
import AdvancedQueryHandler from "./AdvancedQueryHandler";
import { updateTask } from "@/utils/batchAPI";
import { usePlanRestriction } from "@/components/Context/PlanRestrictionContext";
import CodeGenerationHandler from "./CodeGenerationHandler";

const DeepQueryModal = () => {
  const {
    isVisible,
    isFullScreen,
    tab,
    closeModal,
    notification,
    expandedTab,
    setExpandedTab,
    activeTab,
    setActiveTab,
    setSelectedFile,
    functionCalls,
    architectureId,
    showCodeMaintenanceHandler,
    newDocAlert
  } = useCodeGeneration();

  const { showPlanRestriction, creditLimitCrossed } = usePlanRestriction();

  const [portNumber, setPortNumber] = useState(3000);
  const [showMoreDropdown, setShowMoreDropdown] = useState(false);
  const [isCodeInitialized, setIsCodeInitialized] = useState(true);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isCodeViewLoaded, setCodeViewLoaded] = useState(false);
  const [isLeftPanelVisible, setIsLeftPanelVisible] = useState(true);
  const [isPanelExpanded, setIsPanelExpanded] = useState(false);
  const { projectId } = useParams();
  const searchParams = useSearchParams();
  const currentTaskId = searchParams.get("task_id");
  const tabRefs = useRef({});
  const { showAlert } = useContext(AlertContext);
  const [showRestartModal, setShowRestartModal] = useState(false);
  const [showCodeGeneration, setShowCodeGeneration] = useState(false);
  const [instanceData, setInstanceData] = useState(null);
  const selectedRepos = sessionStorage.getItem("selectedRepos");
  const sessionName = sessionStorage.getItem("sessionName");
  const sessionDescription = sessionStorage.getItem("sessionDescription");
  const [showMaintenanceHandler, setShowMaintenanceHandler] = useState(false);
  const [hoverPosition, setHoverPosition] = useState('left');
  const [buttonTranslateX, setButtonTranslateX] = useState(0);
  const [sessionInputValue, setSessionInputValue] = useState(searchParams.get("session_name") || sessionName || 'Untitled Session');
  const [isUpdating, setIsUpdating] = useState(false);
  const isFirstRender = useRef(true);

  const toggleTab = (label) => {
    setActiveTab(label);
  };

  useEffect(() => {
    if (searchParams.get("session_name")) {
      setSessionInputValue(searchParams.get("session_name"));
    }
  }, [searchParams])

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false
      return;
    }

    if (!showPlanRestriction && creditLimitCrossed) { //if modal closed but the limit is crossed, close the deep query modal too
      handleStopAndClose();
    }

  }, [showPlanRestriction])

  const handleToggleHover = (e) => {
    if (!isLeftPanelVisible) return; // Don't track hover when panel is closed

    const rect = e.currentTarget.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const isRightSide = mouseX > rect.width / 2;

    setHoverPosition(isRightSide ? 'right' : 'left');
    setButtonTranslateX(isRightSide ? 5 : -5); // Move 5px in hover direction
  };


  const handlePortChange = (event) => {
    setPortNumber(Number(event.target.value));
  };

  const toggleMoreDropdown = () => {
    setShowMoreDropdown(!showMoreDropdown);
  };

  const handleConfirmClose = () => {
    setShowConfirmModal(true);
    if (sessionStorage.getItem("isCodeMaintenance")) {
      sessionStorage.removeItem("isCodeMaintenance");
      sessionStorage.removeItem("sessionName");
      sessionStorage.removeItem("selectedRepos");
      sessionStorage.removeItem("sessionDescription");
    }
  };

  const confirmCloseModal = () => {
    setShowConfirmModal(false);
    closeModal();
  };

  const handleStopAndClose = async () => {
    setIsUpdating(true);
    try {
      // Run both operations in parallel
      await Promise.all([
        // Update session name if changed
        sessionInputValue !== sessionName
          ? updateTask(currentTaskId, { session_name: sessionInputValue })
          : Promise.resolve(),
        // Stop task (no need to await since we assume success)
        stopBatchTask({ taskId: currentTaskId, projectId })
      ]);

      showAlert("Task closed successfully", "success");
      setShowConfirmModal(false);
      closeModal();
    } catch (error) {
      // Only show error for name update failure
      showAlert("Failed to update session name", "error");
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    if (activeTab !== "File Watch" && setSelectedFile) {
      setSelectedFile(null);
    }
  }, [activeTab, setSelectedFile]);

 ;

  useEffect(() => {
    if (tab === "Editor" && !isCodeInitialized) {
      setIsCodeInitialized(true);
    }
  }, [tab]);

  useEffect(() => {
    if (activeTab === "Editor" && !isCodeViewLoaded) {
      setCodeViewLoaded(true);
    }
  }, [activeTab]);

  const allTabs = [
    { label: "Status", icon: FaChartLine },
    { label: "Documents", icon: FileText },
    ...(functionCalls.length > 0 ? [{ label: "Function Calls", icon: FaCodeBranch }] : []),
    { label: "Terminal", icon: FaCode },
    { label: "Editor", icon: FaFileCode },
    { label: "File Watch", icon: FaTasks },
  ];

  if (!isVisible) return null;

  return (
    <>
      <div className={`fixed discussion-modal inset-0 backdrop-blur-modal flex justify-center items-center z-50 ${isFullScreen ? "w-full h-full" : ""}`}>
        <div className={`bg-[#ffffff] rounded-sm ${isFullScreen ? "w-full h-full" : "w-[97%] h-[93%]"} flex flex-col relative`}>
          <div className="flex flex-grow overflow-hidden px-5 py-5">
            {/* Left Panel */}
            <div
              className={`${isLeftPanelVisible
                  ? (isPanelExpanded ? "min-w-[100%] max-w-[100%]" : "min-w-[30%] max-w-[30%]")
                  : "w-0"
                } transition-all duration-300 ease-in-out relative`}
            >
              {/* Toggle Buttons */}
              <div
                className="absolute -right-4 top-1/2 transform -translate-y-1/2 z-10"
                onMouseMove={(e) => {
                  // Only track hover position when panel is visible
                  if (isLeftPanelVisible) {
                    const rect = e.currentTarget.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const isRightSide = mouseX > rect.width / 2;

                    setHoverPosition(isRightSide ? 'right' : 'left');
                    setButtonTranslateX(isRightSide ? 5 : -5);
                  }
                }}
                onMouseLeave={() => {
                  setHoverPosition('left');
                  setButtonTranslateX(0);
                }}
              >
                <button
                  onClick={() => {
                    if (!isLeftPanelVisible) {
                      setIsLeftPanelVisible(true);
                      setIsPanelExpanded(false);
                    } else if (hoverPosition === 'right') {
                      setIsPanelExpanded(true);
                    } else {
                      setIsLeftPanelVisible(false);
                      setIsPanelExpanded(false);
                    }
                  }}
                  className={`bg-white border border-gray-200 rounded-sm p-1.5
      shadow-md hover:bg-gray-50 hover:shadow-lg
      focus:outline-none focus:ring-2 focus:ring-primary
      transition-all duration-200
      ${!isLeftPanelVisible ? 'hover:translate-x-1' : ''}`}
                  style={{
                    transform: isLeftPanelVisible ? `translateX(${buttonTranslateX}px)` : '',
                    transition: 'all 0.2s ease-out'
                  }}
                  title={!isLeftPanelVisible
                    ? "Show chat panel"
                    : hoverPosition === 'right'
                      ? "Expand to full width"
                      : "Collapse panel"
                  }
                >
                  {!isLeftPanelVisible ? (
                    // When panel is closed, single expand option
                    <ChevronRight className="h-4 w-4 text-gray-700 transition-transform duration-200" />
                  ) : (
                    // When panel is open, direction follows hover
                    hoverPosition === 'right' ? (
                      <ChevronRight className="h-4 w-4 text-gray-700" />
                    ) : (
                      <ChevronLeft className="h-4 w-4 text-gray-700" />
                    )
                  )}
                </button>
              </div>
              {/* Panel Content */}
              <div className={`${isLeftPanelVisible ? "opacity-100 visible" : "opacity-0 invisible"
                } transition-all duration-300 h-full`}>
                <CodeDiscussionPanel isPanelExpanded={isPanelExpanded} />
              </div>
            </div>

            {/* Right Panel */}
            <div className={`flex-grow overflow-auto justify-between transition-all duration-300 ease-in-out ${isLeftPanelVisible ? "border-gray-200 pl-0 border-l border-t border-r border-b" : "pl-0"}`}>
              <div className="flex flex-col h-full bg-opacity-20">
                {/* Tabs Header */}
                <div className="tab-buttons border-b border-gray-200">
                  <div className="flex items-center w-full px-0.5 py-1">
                    <div className="flex flex-grow gap-1 justify-evenly items-center">
                      {allTabs.map(({ label, icon: Icon }) => (
                        <button
                          key={label}
                          ref={(el) => (tabRefs.current[label] = el)}
                          role="tab"
                          aria-selected={activeTab === label}
                          className={`relative flex items-center justify-center gap-1 py-2 typography-body-sm whitespace-nowrap cursor-pointer rounded-md transition-all duration-200 flex-grow
                              ${activeTab === label
                              ? "text-[#1c64f2] bg-white shadow-sm border border-gray-200"
                              : "text-gray-500 hover:bg-gray-100"
                            }`}
                          onClick={() => toggleTab(label)}
                        >
                          <Icon size={14} className="flex-shrink-0" />
                          <span>{label}</span>
                          {label == "Documents" && newDocAlert &&
                            <div className="absolute -top-[1px] -right-[1px] w-2 h-2 bg-red-500 rounded-full"></div>
                          }
                        </button>
                      ))}
                    </div>

                    <button
                      onClick={handleConfirmClose}
                      className="ml-2 p-2 text-gray-500 hover:text-gray-700 bg-gray-200 rounded-sm hover:bg-gray-300 transition-colors flex-shrink-0"
                    >
                      <FaTimes size={20} />
                    </button>
                  </div>
                </div>

                {/* Tab Content */}
                <div className="overflow-auto custom-scrollbar flex-grow">
                  <div className="tab-content h-full">
                    {activeTab === "Status" && (
                      <div className="flex flex-col h-[80vh]">
                        <div className="sticky top-0 z-10 mx-4 mt-2">
                          <div className="bg-white shadow-md rounded-lg">
                            <StatusPanel />
                          </div>
                        </div>
                        <div className="flex-1 overflow-y-auto custom-scrollbar">
                          <div className="mt-4 mx-4">
                            <StepsComponent />
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab && activeTab === "Documents" && <DocumentsPanel task_id={currentTaskId} />}

                    {isCodeViewLoaded && activeTab === "Editor" && <CodeViewPanel />}

                    {activeTab === "File Watch" && <CodeStreamViewer />}

                    {activeTab === "Terminal" && <TerminalPanel />}

                    {activeTab === "Debug" && <DebugPanel />}

                    {activeTab === "Function Calls" && functionCalls.length > 0 && (
                      <div className="p-4">
                        <FunctionCallsComponent />
                      </div>
                    )}

                    {activeTab === "Info" && <ExistingInfo />}

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification.show && (
        <div className="fixed bottom-10 right-10 z-[60] animate-fade-in">
          <div className="bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg flex items-center space-x-2">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      {/* Restart Modal */}
      {showRestartModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
                Instance Restart in Progress
              </h2>
              <p className="text-gray-600 text-center">
                Your instance has been stopped. We are currently restarting it.
                This process will take just a moment.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Code Generation Handler */}
      {(showCodeGeneration && instanceData) && (
        <CodeGenerationHandler
          projectId={projectId}
          itemId={architectureId}
          onComplete={() => {
            setShowCodeGeneration(false);
          }}
        />
      )}

      {/* Code Maintenance Handler */}
      {(showCodeMaintenanceHandler && !showRestartModal) && (
        <AdvancedQueryHandler
          projectId={projectId}
          onComplete={() => setShowMaintenanceHandler(false)}
          selectedRepos={JSON.parse(selectedRepos)}
          sessionName={sessionName}
          description={sessionDescription}
        />
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div
            className="fixed inset-0 bg-gray-900 bg-opacity-60 backdrop-blur-sm"
            onClick={() => setShowConfirmModal(false)}
          />
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-8 z-50 relative transform transition-all">
            {/* Close button */}
            <button
              onClick={() => setShowConfirmModal(false)}
              className="absolute top-4 right-4 p-1.5 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
            >
              <FaTimes size={16} />
            </button>

            <div className="space-y-6">
              {/* Header */}
              <div className="text-center space-y-2">
                <h3 className="typography-heading-4 font-weight-semibold text-gray-900">
                  Close Deep Analysis Session
                </h3>
                <p className="typography-body-sm text-gray-600">
                  Choose how you'd like to handle the current session
                </p>
              </div>

              {/* Session Name Input */}
              <div className="space-y-2">
                <label className="block typography-body-sm font-weight-medium text-gray-700">
                  Session Name
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={sessionInputValue}
                    onChange={(e) => setSessionInputValue(e.target.value)}
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                    placeholder="Enter session name"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-4 pt-2">
                <button
                  onClick={confirmCloseModal}
                  className="inline-flex justify-center items-center px-4 py-2.5 border border-gray-300 rounded-lg typography-body-sm font-weight-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200"
                >
                  Continue in Background
                </button>
                <button
                  onClick={handleStopAndClose}
                  disabled={isUpdating}
                  className="inline-flex justify-center items-center px-4 py-2.5 rounded-lg typography-body-sm font-weight-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdating ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Closing...</span>
                    </div>
                  ) : (
                    "Stop & Close"
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DeepQueryModal;