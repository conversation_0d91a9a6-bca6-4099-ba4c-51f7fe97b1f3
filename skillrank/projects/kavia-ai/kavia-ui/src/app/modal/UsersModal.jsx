import React, { useState, useEffect, useRef, useContext } from "react";
import { X, UserPlus, Users, UserX } from "lucide-react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { DiscussionChatContext } from "@/components/Context/DiscussionChatContext";
import { getDiscussionParticipants } from "@/utils/discussionAPI";
import { addUserToDiscussion } from "@/utils/api";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { SearchInput } from "@/components/UIComponents/Inputs/SearchInput";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Cookies from 'js-cookie';

const UsersModal = ({ isOpen, onClose, discussionId }) => {
  const [users, setUsers] = useState({ current: [], available: [] });
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const { showAlert } = useContext(AlertContext);
  const { setAllUsers } = useContext(DiscussionChatContext);
  const [isProcessing, setIsProcessing] = useState(false);
  const modalRef = useRef(null);
  const userId = Cookies.get('userId');

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen, discussionId]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const data = await getDiscussionParticipants(discussionId, 'all');
      const currentParticipantsObj = data.current_participants || {};
      const currentParticipants = Object.values(currentParticipantsObj);
      const filteredUsers = currentParticipants.filter(user => user.username !== userId);

      setUsers({
        current: data.current_participants || [],
        available: data.participants_to_add || []
      });
      setAllUsers(filteredUsers);
    } catch (error) {

      showAlert("Failed to fetch users", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleUser = (user) => {
    setSelectedUsers(prev =>
      prev.includes(user) ? prev.filter(u => u !== user) : [...prev, user]
    );
  };

  const handleAddUsers = async () => {
    setIsProcessing(true);
    try {
      await addUserToDiscussion(discussionId, selectedUsers.map(u => u.username));
      showAlert("Users added successfully!", "success");
      fetchUsers();
      setSelectedUsers([]);
    } catch (error) {

      showAlert("Failed to add users", "error");
    } finally {
      setIsProcessing(false);
    }
  };


  const UserAvatar = ({ user }) => {
    const getInitials = (name) => {
      return name
        .split(' ')
        .map(word => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    };

    const getInitialsColor = (name) => {
      const hash = name.split('').reduce((acc, char) => ((acc << 5) - acc) + char.charCodeAt(0), 0);
      const hue = hash % 360;
      return `hsl(${hue}, 80%, 60%)`;
    };

    if (user.profilePicture) {
      return (
        <img
          src={user.profilePicture}
          alt={`${user.name}'s avatar`}
          className="relative inline-flex items-center justify-center w-10 h-10 overflow-hidden rounded-full mr-4"
        />
      );
    }
    return (
      <div className="relative inline-flex items-center justify-center w-10 h-10 overflow-hidden rounded-full mr-4" style={{ backgroundColor: getInitialsColor(user.name || user.email || "Unknown") }}>
        <span className="font-weight-medium text-white">
          {getInitials(user.name || user.email || "Unknown")}
        </span>
      </div>
    );
  };

  const getUserDisplayName = (user) => {
    return user.name || user.email || user.username;
  };

  const getButtonText = (isAdded) => {
    return isAdded ? "Remove" : "Add";
  };

  const getButtonVariant = (isUser) => {
    return isUser
      ? "linkDanger"
      : "linkPrimary";
  };

  const getButtonIcon = () => {
    return isUser
      ? UserX
      : UserPlus;
};

  const filteredAvailableUsers = users.available.filter(user => {
    const displayName = getUserDisplayName(user).toLowerCase();
    const email = (user.email || '').toLowerCase();
    return displayName.includes(searchTerm.toLowerCase()) ||
      email.includes(searchTerm.toLowerCase());
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50 backdrop-blur-sm">
      <div ref={modalRef} className="bg-white rounded-lg shadow-xl w-full max-w-xl">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <div className="flex justify-between items-center gap-2">
            <Users className="h-5 w-5 text-gray-500" />
            <h2 className="typography-heading-4 translate-y-[2px]">Discussion Participants</h2>
          </div>
          <DynamicButton
            variant="ghost"
            size="sqSmall"
            icon={X}
            onClick={onClose}
            tooltip="Close"
          />
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto" />
            <p className="mt-2 typography-body text-gray-600">Loading users...</p>
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {/* Current Participants Section */}
            <div>
              <h3 className="typography-body-sm font-weight-medium text-gray-700 mb-2">
                Current Participants
              </h3>
              <div className="max-h-32 overflow-y-auto custom-scrollbar rounded-md border border-gray-200">
                {users.current.map(user => (
                  <div key={user.username}
                    className="flex items-center justify-between p-2 hover:bg-gray-50">
                    <div className="flex items-center gap-2">
                      <UserAvatar user={user} />
                      <span className="typography-body-sm">{getUserDisplayName(user)}</span>
                    </div>
                  </div>
                ))}

              </div>
            </div>

            {/* Add Participants Section */}
            <div>
              <h3 className="typography-body-sm font-weight-medium text-gray-700 mb-2">
                Add Participants
              </h3>
              <div className="mb-4">
                <SearchInput
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search users..."
                />
              </div>

              {users.available.length === 0 ? (
                <div className="text-center "><EmptyStateView type="noUsers" /></div>
              ) : filteredAvailableUsers.length === 0 ? (
                <div className="text-center">
                  <EmptyStateView type="noSearchResults" />
                </div>
              ) : (
                <div className="max-h-48 overflow-y-auto custom-scrollbar">
                  {filteredAvailableUsers.map(user => (
                    <div key={user.username}
                      className="flex items-center p-4 rounded-lg border border-gray-100 mb-2 hover:bg-gray-50 transition-all">
                      <UserAvatar user={user} />
                      <div className="flex-1">
                        <h3 className="font-weight-medium text-gray-900">
                          {getUserDisplayName(user)}
                        </h3>
                        {user.email && (
                          <p className="typography-body-sm text-gray-500">
                            {user.email}
                          </p>
                        )}
                      </div>
                      <DynamicButton
                        variant={getButtonVariant(selectedUsers.includes(user))}
                        size="default"
                        onClick={() => handleToggleUser(user)}
                        text={getButtonText(selectedUsers.includes(user))}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <DynamicButton
                variant="primary"
                icon={UserPlus}
                text="Add Selected Users"
                disabled={selectedUsers.length === 0}
                isLoading={isProcessing}
                onClick={handleAddUsers}
                tooltip={`Add ${selectedUsers.length} users to discussion`}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UsersModal;