#!/bin/bash

# Redis initialization script
set -e

REDIS_PASSWORD="RedisKAVia\$IDpass"
REDIS_PORT=6379
REDIS_DIR="/tmp/redis"

# Create Redis directory
sudo mkdir -p "$REDIS_DIR"
sudo chown redis:redis "$REDIS_DIR"

# Start Redis server
sudo -u redis redis-server \
    --port "$REDIS_PORT" \
    --bind 127.0.0.1 \
    --dir "$REDIS_DIR" \
    --daemonize yes

# Wait for Redis to start
sleep 2

# Configure password
redis-cli -p "$REDIS_PORT" << EOF
CONFIG SET requirepass "$REDIS_PASSWORD"
AUTH "$REDIS_PASSWORD"
CONFIG REWRITE
EOF

# Test connection
if redis-cli -a "$REDIS_PASSWORD" -p "$REDIS_PORT" ping | grep -q "PONG"; then
    echo "Redis initialized successfully on port $REDIS_PORT"
else
    echo "Redis initialization failed"
    exit 1
fi