import asyncio
import atexit
import base64
import os
import random
import re
import select
import string
import threading
import time
import traceback
from socket import socket
import concurrent.futures
from typing import Optional
import docker
import subprocess
import json
import logging
import abc
from docker.errors import DockerException, ImageNotFound
from docker.types import Mount

from code_generation_core_agent.agents.tools.executor.remote_executor import ExecutorBase
import hashlib

from code_generation_core_agent.agents.setup_logger import setup_logger
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.agents.utilities import ApplicationType, PrintLogger, get_container_name
from code_generation_core_agent.config import config
from code_generation_core_agent.shutdown import graceful_shutdown

DEBUG_DOCKER_EXECUTOR = config.getboolean("SYSTEM", "debug_docker_executor", fallback=False)

# Global mutex lock for Docker container operations
GLOBAL_MUTEX = asyncio.Lock()

CONTAINER_INSTANCES = {}

# Custom shell prompt prefix and postfix
SHELL_PREFIX = b"llm_shell"
SHELL_POSTFIX = b"$>"
KAVIA_USER = "kavia"

# Custom shell prompt
CUSTOM_SHELL_PROMPT = '%s:[uid:\\u token=\${DOCKER_CMD_TOKEN} last_cmd_pid:$! last_return:$?] \\w %s' % (SHELL_PREFIX.decode(), SHELL_POSTFIX.decode())

ANDROID_HOME = "/opt/android-sdk-linux"
CUSTOM_GLOBAL_SHELL_PATH = f"/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/sdks/flutter/bin:/opt/flutter/bin:{ANDROID_HOME}/platform-tools:{ANDROID_HOME}/cmdline-tools/latest/bin:{ANDROID_HOME}/emulator"

# We use --norc and --noprofile to avoid running any shell startup and use the custom prompt
shell_setup_script = f"""
export PATH=$PATH:/home/<USER>/.local/bin
export PS1='{CUSTOM_SHELL_PROMPT}'
export PROMPT_COMMAND='PS1="{CUSTOM_SHELL_PROMPT}"'
export PATH="/usr/local/bin:/home/<USER>/.local/bin:$PATH"
exec /bin/bash --norc --noprofile -e
"""

class DockerShellCallback:

    def on_output(self, output):
        pass

    def on_exit(self, return_code):
        pass


def get_container_ports(logger, default=None):
    """
    Parse the container ports from the config.

    The configuration comes from two sources. The config file and the runtime config.
    The runtime config is set by the backend_service_extractor.

    This approach allows us to have pre-configured ports, but also to expose the ports dynamically.
    Note that the port configuration can be done only one time. If the container is already running, the ports
    cannot be changed.

    We ensure that the ServiceExtractor sets the runtime config before the container is started.

    Args:
        default (dict, optional): Default ports to use if parsing fails. Defaults to None.

    Returns:
        dict: Dictionary of port mappings
    """
    if default is None:
        default = {
            "3000/tcp": 3000,
            "8080/tcp": 8080,
            "8088/tcp": 8088,
            "8089/tcp": 8089,
            "5900/tcp": 5900,
            "5002/tcp": 5001
        }

    # Check if the CONTAINER section exists
    if not config.has_section('CONTAINER'):
        logger.error("CONTAINER section not found in config")
        return default

    runtime_config_ports = config.get_runtime_config('CONTAINER', 'ports', None)

    # Check if the ports option exists
    if not config.has_option('CONTAINER', 'ports'):
        logger.error("ports option not found in CONTAINER section")
        return default

    ports_str = config.get('CONTAINER', 'ports')

    try:
        ports = json.loads(ports_str)
        # Merge the runtime config ports
        if runtime_config_ports:
            ports.update(json.loads(runtime_config_ports))
        return ports
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing ports JSON: {str(e)}")
        return default


def extend_env(env: Optional[list] = None):
    """
    Extend the environment with custom environment variables
    """
    if env is None:
        env = []

    env.append("DEBIAN_FRONTEND=noninteractive")
    env.append(f"PATH={CUSTOM_GLOBAL_SHELL_PATH}")

    return env


class DockerExecutionException(Exception):
    pass


def prepare_shell_command(command):
    """
    Prepare the shell command to run in the Docker container
    """
    return command


class DockerExecutor(ExecutorBase):
    """
    Executor that runs commands inside a Docker container.
    """

    def append_env_variables(self, env_variables: dict[str, str]):
        self.env_variables.update(env_variables)

    def __init__(
            self,
            container_name: str = "kavia_default_container_image",
            session_dir=os.path.join(config.get("SYSTEM", "workspace_dir"), "kavia"),
            logger=PrintLogger(),
    ):
        """
        The executor will keep a container instance alive a given container_name alive until
        the application exits. If a container with the supplied name container_name already running,
         the constructor will return the running instance instead of rebuilding the container.

        The started container instances have a name: custom_image_<short md5 hash>
        :param container_name: Name of the container
        :param session_dir: The working directory for the container
        :param platform: The platform for which the container is being created
        :param android_platform_version: The Android platform version for the emulator container
        :param base_release_version: The base release version for the emulator container
        """
        self.logger = setup_logger("DockerExecutor", session_dir, logging.DEBUG) if DEBUG_DOCKER_EXECUTOR else logger
        self.session_dir = session_dir
        self.working_dir = config.get("SYSTEM", "workspace_dir")
        self.image_name = get_container_name()

        self.container_name = get_container_name().replace('/', '_').replace(':', '_')
        self.container = None
        self.client = None
        self.running = False
        self.init_docker_client()
        self.env_variables = {}
        atexit.register(self.cleanup_all_containers)

    def is_running(self) -> bool:
        return self.running

    def background_startup(self):
        """
        Start container preparation in a background thread.
        This ensures both the Docker image and container are ready when needed.
        The method doesn't wait for completion as subsequent direct calls will handle that.
        """

        def _background_container_prep():
            try:
                future = shared_loop.run_coroutine(self.ensure_container())
                future.result()
                self.logger.debug("Background container preparation completed successfully")
            except Exception as e:
                self.logger.error(f"Background container preparation failed: {e}")
                graceful_shutdown()

        threading.Thread(target=_background_container_prep, daemon=True).start()

    def run_one_shot_command(self, cmd, timeout=120, work_directory=None) -> (str, int):

        async def _one_shot_docker_command():
            stdout_data, return_code = await self.create_subprocess_shell_one_shot(cmd, timeout=timeout - 3,
                                                                                   work_directory=work_directory)
            return stdout_data, return_code

        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command())

            output, returncode = future.result(timeout=timeout)

            if returncode != 0:
                if "lsof" in cmd and output == "":
                    # lsof command may return empty output if no process is using the port
                    return output, returncode

                raise Exception(f"Error executing Docker command. {returncode}. Error: {output}")

            return output, returncode
        except concurrent.futures.TimeoutError:
            raise DockerExecutionException(f"Docker command execution timed out after {timeout} seconds")
        except Exception as e:
            raise DockerExecutionException(f"Error executing Docker command. {type(e).__name__} : {str(e)}")

    @staticmethod
    def get_docker_socket_path():
        """
        Get the path to the Docker socket. This is required to initialize the Docker client.
        Depending on the platform, the Docker socket path can be different.
        """
        try:
            result = subprocess.run(['docker', 'context', 'inspect'], capture_output=True, text=True)
            if result.returncode != 0:
                raise RuntimeError(f"Failed to run 'docker context inspect': {result.stderr}")

            context_data = json.loads(result.stdout)
            if not context_data or 'Endpoints' not in context_data[0]:
                raise RuntimeError("Invalid docker context data")

            docker_endpoint = context_data[0]['Endpoints']['docker']
            if 'Host' in docker_endpoint:
                return docker_endpoint['Host'].replace('unix://', '')
            else:
                raise RuntimeError("Docker socket path not found in context data")
        except Exception as e:
            raise RuntimeError(f"Error getting Docker socket path: {str(e)}")

    @staticmethod
    def get_docker_binary_path():
        """
        Get the path to the Docker binary. This is required to initialize the Docker client.
        Depending on the platform, the Docker binary path can be different.
        """
        try:
            result = subprocess.run(['which', 'docker'], capture_output=True, text=True)
            if result.returncode != 0:
                raise RuntimeError(f"Failed to find Docker binary: {result.stderr}")
            return result.stdout.strip()
        except Exception as e:
            raise RuntimeError(f"Error getting Docker binary path: {str(e)}")

    def init_docker_client(self):
        """
        Initialize the Docker client.
        """
        try:
            socket_path = self.get_docker_socket_path()
            self.client = docker.DockerClient(base_url=f'unix://{socket_path}')
            self.client.ping()
        except docker.errors.DockerException as e:
            print("***************************************************")
            print("***  ERROR:Docker client initialization failed  ***")
            print("***************************************************")
            print("Docker may not be running or the Docker socket path is incorrect.")
            print("See README.md for more information on running LLM in Docker.")

            raise RuntimeError(f"Failed to initialize Docker client: {e}")

    async def ensure_image(self):
        """
        Ensure that required Docker images exist. For Flutter and Android platforms,
        attempt to pull the emulator image from DockerHub if local build fails.
        """
        try:
            try:
                self.client.images.get(self.image_name)
            except ImageNotFound:
                print(f"Image {self.image_name} not found. Building...")


        except DockerException as e:
            raise RuntimeError(f"Failed to ensure images: {e}")

    async def ensure_container(self):
        """
        Ensure that the Docker container exists and is running.
        """
        async with GLOBAL_MUTEX:
            await self.ensure_image()
            if self.container_name not in CONTAINER_INSTANCES:
                # Stop and remove containers with similar names but different tags to avoid port conflicts
                base_name = self.container_name.rsplit('_', 1)[0]  # Remove tag/version
                for container in self.client.containers.list(all=True):
                    if container.name.startswith(base_name) and container.name != self.container_name:
                        try:
                            self.logger.info(f"Stopping and removing old container: {container.name}")
                            container.stop(timeout=3)
                            container.remove(force=True)
                        except Exception as e:
                            self.logger.warning(f"Failed to stop/remove container {container.name}: {e}")
                
                # Check if the container already exists and is running when CONTAINER_INSTANCES hasn't been updated yet due to a restart
                try:
                    container = self.client.containers.get(self.container_name)
                    container.reload()
                    if container.status == 'running':
                        CONTAINER_INSTANCES[self.container_name] = container
                        self.running = True
                        return
                except docker.errors.NotFound:
                    pass  # Container does not exist, proceed to create it
                
                try:
                    container = await self._create_container()
                    CONTAINER_INSTANCES[self.container_name] = container
                    self.running = True
                except docker.errors.APIError as e:
                    if e.status_code == 409:
                        # Container with this name already exists
                        print(f"Container {self.container_name} already exists. Removing it...")
                        try:
                            existing_container = self.client.containers.get(self.container_name)
                            print ("starting the container")

                            existing_container.start()

                            if existing_container.status == 'stopped':

                                existing_container.start()

                            CONTAINER_INSTANCES[self.container_name] = existing_container

                            self.running = True

                            print(f"Container {self.container_name} already exists. Using it...")

                            # # Try to create the container again
                            # container = await self._create_container()
                            # CONTAINER_INSTANCES[self.container_name] = container
                        except docker.errors.APIError as inner_e:
                            raise RuntimeError(f"Failed to remove existing container and create a new one: {inner_e}")
                    else:
                        raise RuntimeError(f"Failed to create container: {e}")
                except DockerException as e:
                    raise RuntimeError(f"Failed to create container: {e}")

    async def _create_container(self):
        """Create a Docker container with platform-specific configuration."""
        uid = os.getuid()
        gid = os.getgid()

        socket_dir = os.path.dirname(self.get_docker_socket_path())

        from dotenv import load_dotenv
        load_dotenv()

        ai_key = os.environ.get('OPENAI_API_KEY', "")

        mounts = []

        socket_mount = Mount(
            type='bind',
            source=socket_dir,
            target='/var/run_docker',
            read_only=False
        )
        mounts.append(socket_mount)

        working_dir_mount = Mount(
            type='bind',
            source=self.working_dir,
            target=self.working_dir,
            read_only=False
        )
        mounts.append(working_dir_mount)
        
        enable_shelly = config.getboolean("SYSTEM", "enable_shelly", fallback=False)

        env = [
            f"HOST_UID={uid}",
            f"LOCAL_UID={uid}",
            f"LOCAL_GID={gid}",
            f"PS1={CUSTOM_SHELL_PROMPT}",
            "DEBIAN_FRONTEND=noninteractive"
        ]
        if enable_shelly:
            env.append(f"SHELLY_AI_KEY={ai_key}")

        ports = get_container_ports(self.logger, default=None)
        
        try:
            container = self.client.containers.run(
                self.image_name,
                name=self.container_name,
                command="tail -f /dev/null",
                detach=True,
                tty=True,
                stdin_open=True,
                privileged=True,
                mounts=mounts,
                environment=env,
                ports=ports,
                working_dir=self.session_dir,
                user="root",
                extra_hosts={'host.docker.internal': 'host-gateway'},
            )
            time.sleep(3)

            container.reload()

            if container.status != 'running':
                logs = container.logs().decode('utf-8')
                raise RuntimeError(f"Container failed to start. Logs:\n{logs}")
            return container

        except Exception as e:
            print(f"Failed to create container: {str(e)}")
            raise

    def safe_decode(self, data):
        """
        Attempts to decode bytes as UTF-8, falling back to base64 if that fails.

        Args:
            data (bytes): The bytes to decode

        Returns:
            decoded_string
        """
        try:
            return data.decode('utf-8')
        except UnicodeDecodeError:
            return f"base64://{base64.b64encode(data).decode('ascii')}"

    async def create_subprocess_shell_one_shot(self, cmd, timeout=60, work_directory=None, **kwargs):
        """
        Create a subprocess shell in the Docker container to run a single command. The command is executed
        in a shell and the shell is terminated after the command is executed.

        Note: never call this function with blocking commands. This function is only for one-shot commands
        that are always guaranteed to finish in a reasonable time.

        @param cmd: The command to run in the shell
        @param timeout: Timeout in seconds
        @param kwargs: Additional arguments to pass to the shell
            Available arguments:
                - work_directory: The working directory for the shell
                - env: Environment variables for the shell

        @return: Tuple containing (output, error_code)
        """
        if DEBUG_DOCKER_EXECUTOR:
            log_message = f"[OneShot] Executing command: {cmd}\n[OneShot] Working directory: {work_directory}\n"
            self.logger.debug(log_message)
            print(log_message)

        await self.ensure_container()
        cmd = prepare_shell_command(cmd)
        if not work_directory:
            work_directory = kwargs.get('work_directory', self.working_dir)

        if 'env' in kwargs:
            env = kwargs.get('env')
        else:
            env = []

        env = extend_env(env)

        for key, value in self.env_variables.items():
            env.append(f"{key}={value}")
        container = CONTAINER_INSTANCES[self.container_name]

        # Use the coreutils timeout command to run the command with a timeout
        # This ensures that the shell is terminated after the timeout.
        # It is a much better way then trying to kill the shell process after the timeout.
        exec_id = self.client.api.exec_create(
            container.id,
            ["timeout", "--preserve-status", f"{timeout}s", "bash", "-c", cmd],
            stdout=True,
            stderr=True,
            privileged=True,
            workdir=work_directory,
            environment=env,
            user=kwargs.get('user', 'kavia')
        )
        output = self.client.api.exec_start(exec_id)

        exec_info = self.client.api.exec_inspect(exec_id)
        error_code = exec_info['ExitCode']

        if DEBUG_DOCKER_EXECUTOR:
            log_message = f"[OneShot] Command completed with exit code: {error_code}\n[OneShot] Output: {self.safe_decode(output)}"
            self.logger.debug(log_message)
            print(log_message)

        return self.safe_decode(output), error_code

    async def create_subprocess_shell(self, cmd, callback : DockerShellCallback = None, **kwargs):
        """
        Create a subprocess shell in the Docker container. This is an async function that returns a DockerSubprocess
        object. The shell is interactive and can be used to run multiple commands. The caller is responsible for
        terminating the shell when it is no longer needed.

        @param cmd: The command to run in the shell
        @param kwargs: Additional arguments to pass to the shell
            Available arguments:
                - work_directory: The working directory for the shell
                - is_background: If True, the shell will run in the background. Default is False.
                                 Background shells need to be terminated manually. For foreground shells,
                                 the shell will be terminated automatically after the command is executed.

        @return: DockerSubprocess object that represents the shell

        """
        if DEBUG_DOCKER_EXECUTOR:
            log_message = f"[Shell] Creating interactive shell\n[Shell] Initial command: {cmd}\n[Shell] Working directory: {kwargs.get('work_directory', self.working_dir)}\n"
            self.logger.debug(log_message)
            print(log_message)

        await self.ensure_container()
        cmd = prepare_shell_command(cmd)
        # Get work_directory from kwargs
        if 'work_directory' in kwargs:
            work_directory = kwargs['work_directory']
        else:
            work_directory = self.working_dir
        cmd += "\n"

        if 'env' in kwargs:
            env = kwargs.get('env')
        else:
            env = []

        env = extend_env(env)

        for key, value in self.env_variables.items():
            env.append(f"{key}={value}")

        env.append("PS1='llm_shell:[uid:\\u token=${DOCKER_CMD_TOKEN} last_cmd_pid:$! last_return:$?] \\w $>'")
        if 'one_shot' in kwargs and kwargs['one_shot']:
            one_shot = True
        else:
            one_shot = False

        try:
            container = CONTAINER_INSTANCES[self.container_name]
            exec_id = self.client.api.exec_create(
                container.id,
                ["/bin/bash", "-c", shell_setup_script],
                stdout=True,
                stderr=True,
                stdin=True,
                tty=True,
                privileged=True,
                environment=env,
                workdir=work_directory,
                user=kwargs.get('user', 'kavia'),
            )

            socket_wrapper = SocketWrapper(self.client.api.exec_start(exec_id,
                                                                      tty=True,
                                                                      stream=True, socket=True))

            retries = 3
            while retries > 0:
                exec_info = self.client.api.exec_inspect(exec_id)
                pid = exec_info.get('Pid')
                exit_code = exec_info.get('ExitCode')

                if exit_code or pid != 0:
                    break
                await asyncio.sleep(1)
                retries -= 1

            sub_process = DockerSubprocess(self.client, exec_id, socket_wrapper, pid, callback=callback, logger=self.logger)

            # Send the command to the shell
            sub_process.stdin.write(cmd.encode())
            if one_shot:
                sub_process.stdin.write("exit\n".encode())

            if DEBUG_DOCKER_EXECUTOR:
                log_message = f"[Shell] Created exec instance: {exec_id}\n[Shell] Subprocess created with PID: {pid}\n[Shell] Sending initial command to shell"
                self.logger.debug(log_message)
                print(log_message)

            return sub_process
        except DockerException as e:
            self.logger.error(f"Failed to create subprocess: {e}")
            return DockerSubprocess(self.client, None, None, -1, self.logger)

        except Exception as e:
            self.logger.error(f"Failed to create subprocess: {e}")
            if DEBUG_DOCKER_EXECUTOR:
                self.logger.debug(f"[Shell] Stack trace: {traceback.format_exc()}")
            return DockerSubprocess(self.client, None, None, -1, self.logger)

    async def terminate_process(self, process):
        """
        Terminate the Docker subprocess
        """
        await process.terminate()

    def create_task(self, task):
        """
        Create a new asyncio task
        """
        return asyncio.create_task(task)

    async def wait(self, fs, timeout=None):
        """
        Wait for multiple futures to complete or timeout
        """
        try:
            done, pending = await asyncio.wait(fs, timeout=timeout, return_when=asyncio.FIRST_COMPLETED)

            # Cancel any pending tasks
            for task in pending:
                task.cancel()

            # Wait for cancelled tasks to finish
            await asyncio.gather(*pending, return_exceptions=True)

            return done, pending
        except asyncio.TimeoutError:
            # If a timeout occurs, cancel all tasks
            for task in fs:
                task.cancel()

            # Wait for cancelled tasks to finish
            await asyncio.gather(*fs, return_exceptions=True)

            return set(), set(fs)

    @staticmethod
    def cleanup_all_containers():
        """
        Cleanup all Docker containers when the application exits.
        """
        for container_name, container in CONTAINER_INSTANCES.items():
            try:
                container.stop(timeout=1)
                container.remove(force=True)
                print(f"Cleaned up container: {container_name}")
            except Exception as e:
                print(f"Error during cleanup of container {container_name}: {e}")
        CONTAINER_INSTANCES.clear()

    async def gather(self, *futures):
        """
        Gather multiple futures
        """
        return await asyncio.gather(*futures)

    def run_coroutine_threadsafe(self, coroutine, loop):
        return asyncio.run_coroutine_threadsafe(coroutine, loop)

    async def wait_for(self, coro, timeout=None):
        return await asyncio.wait_for(coro, timeout)

    def cleanup(self):
        """
        Cleanup the Docker container
        """
        if self.container:
            try:
                self.container.stop(timeout=1)
                self.container.remove(force=True)
            except Exception as e:
                self.logger.debug(f"Error during cleanup: {e}")

    def __del__(self):
        pass


class SocketWrapper:
    _read_mutex = threading.Lock()
    _write_mutex = threading.Lock()

    def __init__(self, sock):
        self.sock = sock

    def sendall(self, data):
        with self._write_mutex:
            self.sock.flush()

            # BUG: DockerPy does not flush the socket after sending data
            # writing immediately to the socket causes the command being discarded
            # We should avoid this sleep and find a better solution
            # 0.1s sleep works but adding a higher value for safety
            time.sleep(0.5)

            self.sock._sock.sendall(data)

    def recv(self, n):
        with self._read_mutex:
            return self.sock._sock.recv(n)

    def select(self, timeout=1):
        return select.select([self.sock._sock], [], [], timeout)

    def close(self):
        self.sock.close()


class DockerSubprocess:
    """
    Class to represent a subprocess running inside a Docker container.
    """

    def __init__(self, client, exec_id, socket_wrapper, pid, callback=None, logger=None):
        self.exec_id = exec_id
        self.client = client
        self.socket_wrapper = socket_wrapper
        self.returncode = None
        self._stdout = None
        self._stderr = None
        self._stdin = None
        self.pid = pid
        self.logger = logger
        self._stdout = DockerStreamReader(self.socket_wrapper, client, exec_id, callback=callback, logger=logger)
        self._stdin = DockerStdinWriter(self.socket_wrapper, self.exec_id, logger)

    @property
    def stdout(self):
        return self._stdout

    @property
    def stdin(self):
        return self._stdin

    @property
    def stderr(self):
        # all outputs are redirected to stdout
        return self.stdout

    async def wait(self, timeout=5):
        """
        Wait for the subprocess to complete
        Parameters:
        timeout: int - Timeout in seconds
        Returns:
        returncode: int - The exit code of the subprocess
        output: str - The output of the subprocess
        """
        output = b''
        if self.returncode is None:
            output = self.stdout.read(timeout=timeout)
            self.returncode = self._get_exit_code()

        return self.returncode, output.decode()

    def _get_exit_code(self):
        try:
            exec_inspect = self.client.api.exec_inspect(self.exec_id)
            exit_code = exec_inspect['ExitCode']
            if exit_code is None:
                return 0

            return exit_code
        except docker.errors.APIError as e:
            self.logger.debug(f"Failed to get exit code: {e}")
            return -1

    async def terminate(self):
        """
        Terminate the subprocess and all its child processes
        """
        self.logger.debug("Terminating subprocess and all child processes")

        try:
            # Send CTRL-C to interrupt any running processes
            self._stdin.write(b'\x03')

            # Kill all processes in the current session
            self._stdin.write(b"kill -9 -$$\n")

            # Send CTRL-D to exit the shell
            # We need to send multiple CTRL-D to exit the shell
            # for the case when multiple nested shells are running
            for i in range(5):
                self._stdin.write(b'\x04')

            # Wait a brief moment for the commands to take effect
            await asyncio.sleep(0.5)

            # Close the socket
            self.socket_wrapper.close()

            # Set the return code
            self.returncode = -1

            self.logger.debug("Subprocess terminated successfully")
        except Exception as e:
            self.logger.debug(f"Error during subprocess termination: {e}")
            self.returncode = -2


class DockerStreamReader:
    """
    Class to read from the stdout of a Docker subprocess
    """

    def __init__(self, socket_wrapper, client, exec_id, callback=None, logger=None):
        self.socket_wrapper = socket_wrapper
        self.buffer = b''
        self._loop = asyncio.get_event_loop()
        self.callback = callback
        self.terminated = False
        self.exit_code = None
        self.client = client
        self._buffer_lines = []
        self.exec_id = exec_id
        self.logger = logger

        if self.callback:
            self.read_thread = threading.Thread(target=self._read_forever)
            self.read_thread.start()

    def _read_forever(self):
        while not self.terminated:
            result = self._read_without_check(timeout=1)
            if result:
                self.callback.on_output(self._process_output(result))
            exec_info = self.client.api.exec_inspect(self.exec_id)

            if exec_info['ExitCode'] is not None:
                self.exit_code = exec_info['ExitCode']
                self.terminated = True
                self.callback.on_exit(self.exit_code)
                break

    @staticmethod
    def _process_output(output):
        """
        Helper function to remove shell alive messages, empty shell lines, and handle carriage returns
        """

        # Remove ANSI escape sequences
        ansi_escape = re.compile(rb'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        output = ansi_escape.sub(b'', output)

        lines = re.split(rb'[\r\n]', output)
        filtered_lines = []

        for line in lines:
            line = line.strip()

            if not line:
                continue

            if line.startswith(SHELL_PREFIX) and b'export DOCKER_CMD_TOKEN' in line:
                continue

            filtered_lines.append(line)

        return b'\n'.join(filtered_lines)

    def _read_without_check(self, timeout=0.5):
        """
        Read data from socket without sending a shell check command
        """
        start_time = time.time()
        result = b''
        try:
            while time.time() - start_time < timeout:
                ready = self.socket_wrapper.select(0.1)
                if ready[0]:
                    chunk = self.socket_wrapper.recv(4096)
                    if not chunk:
                        return b''
                    result += chunk
                    return result
            return b''
        except Exception as e:
            print(f"Error reading from socket: {e}")
            return b''


    def read_with_partial(self, n=-1, timeout=10):
        """
        Read will read from the shell until timeout or until there is a responsive shell.
        """
        start_time = time.time()
        inactive_time = start_time
        inactivity_timeout = 10
        select_timeout = 0.2  # Shorter timeout for more frequent checks

        # The responsiveness of the shell is ensured by injecting a random string and checking if it is echoed back
        # If the echo completes that means the previous command has finished executing or it is in the background
        # We always generate a new random string to avoid false positives

        random_string = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))

        # Check if the shell is responsive by injecting a random string
        # The string is used in the shell prompt to indicate that the shell is responsive
        check_command = f'\n export DOCKER_CMD_TOKEN={random_string}{random_string} # testing shell response \n'

        self.socket_wrapper.sendall(check_command.encode())

        random_pattern = re.compile(f'token={random_string}{random_string}'.encode())

        partial = False
        try:
            while time.time() - start_time < timeout:

                ready = self.socket_wrapper.select(select_timeout)

                if ready[0]:
                    chunk = self.socket_wrapper.recv(4096)

                    if not chunk:
                        break
                    self.buffer += chunk
                    if random_pattern.search(self.buffer):
                        return self._process_output(self.buffer), partial
                    if n != -1 and len(self.buffer) >= n:
                        break

                    # still getting output, keep alive
                    inactive_time = time.time()

                now = time.time()
                if now - start_time >= timeout or now - inactive_time > inactivity_timeout:
                    self.logger.debug("stdout read timeout. Returning partial output...")
                    break

            # Process the buffer
            if n == -1:
                result = self.buffer
                self.buffer = b''
            else:
                result = self.buffer[:n]
                self.buffer = self.buffer[n:]

            now = time.time()
            if now - start_time >= timeout or now - inactive_time > inactivity_timeout:
                self.logger.debug("stdout read timeout. Returning partial output...")
                partial = True

            return self._process_output(result), partial

        except Exception as e:
            self.logger.debug("f stack trace: ", traceback.format_exc())
            self.logger.debug(f"An error occurred while reading: {e}")
            return b''

    def read(self, n=-1, timeout=10):
        output, partial = self.read_with_partial(n, timeout)
        return output


class DockerStdinWriter:
    """
    Class to write to the stdin of a Docker subprocess
    """

    def __init__(self, socket_wrapper, exec_id, logger):
        self.socket_wrapper = socket_wrapper
        self.exec_id = exec_id
        self.logger = logger

    def write(self, data):
        if DEBUG_DOCKER_EXECUTOR:
            self.logger.debug(f"{self.exec_id} :: Executing command: {data}")
        self.socket_wrapper.sendall(data)

    def flush(self):
        pass
