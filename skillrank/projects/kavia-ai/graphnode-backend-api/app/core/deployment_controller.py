from datetime import datetime
from typing import Dict, Any, Callable
import os
import json
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME, DEPLOYMENT_COLLECTION_NAME
from app.utils.datetime_utils import generate_timestamp
from app.core.websocket.client import WebSocket<PERSON>lient
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
import zipfile
import tempfile
import os
import boto3
import json
from app.core.Settings import settings
import time
import uuid
from pydantic import BaseModel
import requests
import yaml
import asyncio
import aiohttp
import ssl
import random
from urllib.parse import urlparse
import threading
import concurrent.futures
from app.core.custom_docker_executor import DockerExecutor, DockerShellCallback
from typing import Optional
import functools

def run_in_daemon_thread(func):
    """Decorator to run function in a daemon thread"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(
            target=func,
            args=args,
            kwargs=kwargs,
            daemon=True,
            name=f"{func.__name__}_daemon"
        )
        thread.start()
        return thread
    return wrapper

class DeploymentModel(BaseModel):
    _id: str
    deployment_id: str
    project_id: str
    task_id: str
    app_id: str
    branch_name: str
    app_url: str
    artifact_path: str
    custom_domain:str
    job_id: str
    status: str
    message: str
    command: str
    root_path: str
    build_path: str
    created_at: datetime
    updated_at: datetime



# class DeploymentCommandModel(BaseModel):

class AmplifyHealthChecker:
    """
    Integrated health checker for Amplify deployments
    """
    
    def __init__(self, options: Dict[str, Any] = None):
        options = options or {}
        self.max_retries = options.get('max_retries', 60)  # 5 minutes with 5s intervals
        self.retry_interval = options.get('retry_interval', 5.0)  # 5 seconds
        self.timeout = options.get('timeout', 10.0)  # 10 seconds
        self.expected_status_codes = options.get('expected_status_codes', [200, 301, 302])
        self.user_agent = options.get('user_agent', 'AmplifyHealthChecker/1.0')
        
    async def check_url(self, url: str) -> Dict[str, Any]:
        """Check if URL is accessible with proper SSL"""
        start_time = time.time() * 1000  # milliseconds
        
        try:
            # Parse URL
            parsed_url = urlparse(url)
            is_https = parsed_url.scheme == 'https'
            
            # Configure SSL context for HTTPS
            ssl_context = None
            if is_https:
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = True
                ssl_context.verify_mode = ssl.CERT_REQUIRED
            
            # Configure timeout
            timeout = aiohttp.ClientTimeout(total=self.timeout, connect=self.timeout)
            
            # Configure headers
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
            
            async with aiohttp.ClientSession(
                timeout=timeout,
                connector=aiohttp.TCPConnector(ssl=ssl_context) if is_https else None
            ) as session:
                async with session.get(url, headers=headers, allow_redirects=True) as response:
                    end_time = time.time() * 1000
                    response_time = int(end_time - start_time)
                    
                    is_success = response.status in self.expected_status_codes
                    
                    return {
                        'success': is_success,
                        'status_code': response.status,
                        'headers': dict(response.headers),
                        'ssl_valid': is_https,  # If we got here with HTTPS, SSL is valid
                        'response_time': response_time
                    }
                    
        except asyncio.TimeoutError:
            end_time = time.time() * 1000
            return {
                'success': False,
                'error': 'Request timeout',
                'code': 'TIMEOUT',
                'response_time': int(end_time - start_time)
            }
        except ssl.SSLError as e:
            end_time = time.time() * 1000
            return {
                'success': False,
                'error': f'SSL Error: {str(e)}',
                'code': 'SSL_ERROR',
                'response_time': int(end_time - start_time)
            }
        except aiohttp.ClientError as e:
            end_time = time.time() * 1000
            return {
                'success': False,
                'error': f'Connection Error: {str(e)}',
                'code': 'CONNECTION_ERROR',
                'response_time': int(end_time - start_time)
            }
        except Exception as e:
            end_time = time.time() * 1000
            return {
                'success': False,
                'error': f'Unexpected Error: {str(e)}',
                'code': 'UNKNOWN_ERROR',
                'response_time': int(end_time - start_time)
            }
    
    async def wait_for_accessible(self, url: str, message_callback: Callable[[str], None] = None) -> Dict[str, Any]:
        """Wait for URL to become accessible with exponential backoff"""
        if message_callback:
            message_callback(f"🔍 Starting health check for: {url}")
        
        for attempt in range(1, self.max_retries + 1):
            result = await self.check_url(url)
            
            if result['success']:
                if message_callback:
                    message_callback(f"✅ URL is accessible! ({result['response_time']}ms)")
                    message_callback(f"   HTTP Status: {result['status_code']}")
                    message_callback(f"   SSL Certificate: {'Valid' if result.get('ssl_valid') else 'N/A'}")
                    message_callback(f"   Total Attempts: {attempt}")
                    message_callback(f"   Total Wait Time: {(attempt - 1) * self.retry_interval:.1f}s")
                
                return {
                    'success': True,
                    'attempts': attempt,
                    'final_result': result,
                    'total_wait_time': (attempt - 1) * self.retry_interval
                }
            
            # Log the attempt
            time_elapsed = (attempt - 1) * self.retry_interval
            error_msg = result.get('error', f"HTTP {result.get('status_code', 'Unknown')}")
            
            if message_callback:
                progress = (attempt / self.max_retries) * 100
                message_callback(f"⏳ Attempt {attempt}/{self.max_retries} ({progress:.1f}%) - {error_msg} ({time_elapsed:.1f}s elapsed)")
                
                # Additional SSL-specific messaging
                if result.get('code') == 'SSL_ERROR':
                    message_callback("   SSL certificate may still be propagating...")
                elif result.get('code') == 'CONNECTION_ERROR':
                    message_callback("   DNS propagation may still be in progress...")
            
            # Don't wait after the last attempt
            if attempt < self.max_retries:
                # Exponential backoff with jitter
                backoff_seconds = min(
                    self.retry_interval * (1.2 ** (attempt - 1)),
                    30.0  # Max 30 seconds
                )
                jitter_seconds = random.random()  # Add up to 1s jitter
                
                sleep_time = backoff_seconds + jitter_seconds
                await asyncio.sleep(sleep_time)
        
        if message_callback:
            message_callback(f"❌ URL not accessible after {self.max_retries} attempts ({self.max_retries * self.retry_interval:.1f}s total)")
            message_callback("The application may still be propagating. Consider:")
            message_callback("  - Checking DNS propagation manually")
            message_callback("  - Verifying SSL certificate status")
            message_callback("  - Waiting a few more minutes and trying again")
        
        return {
            'success': False,
            'attempts': self.max_retries,
            'total_wait_time': self.max_retries * self.retry_interval
        }

class DeploymentController:
    
    WILDCARD_CERTIFICATE_ARN = "arn:aws:acm:us-east-1:058264095463:certificate/487bc3c3-fdb0-40e0-93ef-f14dfa921b9f"
    
    class BuildOutputCallback(DockerShellCallback):
        """Custom callback for streaming build output"""
        
        def __init__(self, send_message_func):
            self.send_message = send_message_func
            self.exit_code = None
            
        def on_output(self, output):
            """Handle live output from Docker container"""
            if output and output.strip():
                # Send each line of output as it comes
                lines = output.decode('utf-8') if isinstance(output, bytes) else str(output)
                for line in lines.strip().split('\n'):
                    if line.strip():
                        self.send_message(f"🔨 {line.strip()}")
                        
        def on_exit(self, return_code):
            """Handle process completion"""
            self.exit_code = return_code
            if return_code == 0:
                self.send_message("✅ Build process completed successfully!")
            else:
                self.send_message(f"❌ Build process failed with exit code: {return_code}")
    
    def __init__(self, task_id, ws_client, db):
        self.task_id = task_id
        self.ws_client = ws_client
        self.db = db
        self.kaviarootdb = get_mongo_db(KAVIA_ROOT_DB_NAME).db
        self._command_map = self._initialize_command_map()
        # Initialize health checker with custom settings
        self.health_checker = AmplifyHealthChecker({
            'max_retries': 60,  # 5 minutes with 5s intervals
            'retry_interval': 5.0,
            'timeout': 15.0,
            'expected_status_codes': [200, 301, 302, 404]  # 404 is acceptable for some apps
        })
        
    def _initialize_command_map(self) -> Dict[str, Callable]:
        """Initialize mapping of command names to their handler methods"""
        return {
            'list_dir': self._handle_list_dir,
            'start_deploy': self._handle_start_deploy,
            'deployment_status': self._handle_deployment_status,
            'manifest': self._handle_manifest,
            'save_configuration': self._handle_save_configuration
        }
    
    async def check_url_health_async(self, url: str) -> Dict[str, Any]:
        """
        Async wrapper for health checking with progress reporting
        """
        return await self.health_checker.wait_for_accessible(
            url, 
            message_callback=self._send_message
        )

    def check_url_health_sync(self, url: str) -> Dict[str, Any]:
        """
        Synchronous wrapper for health checking
        """
        try:
            # Create new event loop if none exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run the async health check
            if loop.is_running():
                # If loop is already running, use asyncio.create_task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.check_url_health_async(url))
                    return future.result()
            else:
                return loop.run_until_complete(self.check_url_health_async(url))
                
        except Exception as e:
            self._send_message(f"❌ Health check error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'attempts': 1,
                'total_wait_time': 0
            }

    def _run_build_and_deploy(self, deployment_id: str, deployment_type: str, build_command: str, root_path: str, data: Dict[str, Any]):
        """
        Run build and deployment process synchronously in daemon thread
        """
        try:
            print(f"Starting build and deploy in thread {threading.current_thread().name}")
            
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the async build method
            result = loop.run_until_complete(
                self.build(deployment_id, deployment_type, build_command, root_path)
            )
            
            # Send completion message based on result
            if result.get('status') == 'success':
                self._send_message(f"🎯 Build completed successfully for deployment {deployment_id}")
                
                # Now trigger artifact deployment
                try:
                    self._send_message("🚀 Build successful! Starting artifact deployment...")
                    
                    # Use get_build_path to find the actual build directory
                    build_path = self.get_build_path(root_path)
                    
                    if build_path:
                        self._send_message(f"📦 Found build directory: {build_path}")
                        
                        # Update data with build path
                        data["build_path"] = build_path
                        
                        self._send_message(f"🎯 Triggering artifact deployment with build path: {build_path}")
                        
                        # Trigger handle_deploy_artifact
                        deployment_result = self.handle_deploy_artifact(build_path, self.ws_client, data)
                        
                        if deployment_result.get('status') == 'success':
                            self._send_message(f"🎉 Complete deployment pipeline successful!")
                            self._send_message(f"   • Build completed in: {root_path}")
                            self._send_message(f"   • Artifacts found in: {build_path}")
                            self._send_message(f"   • App URL: {deployment_result.get('app_url', 'Pending...')}")
                        else:
                            self._send_message(f"❌ Artifact deployment failed: {deployment_result.get('message', 'Unknown error')}")
                    else:
                        self._send_message(f"⚠️ No build directory found in {root_path}")
                        self._send_message(f"   • Checked for: dist/, build/, output/")
                        self._send_message(f"   • Build may have failed or uses different output directory")
                        
                        # Update deployment status to indicate missing build artifacts
                        self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                            {"_id": deployment_id},
                            {"$set": {
                                "status": "build_artifacts_missing",
                                "message": f"Build completed but no artifacts found in {root_path}",
                                "updated_at": datetime.now()
                            }}
                        )
                        
                except Exception as artifact_error:
                    error_msg = f"Artifact deployment failed: {str(artifact_error)}"
                    self._send_message(f"❌ {error_msg}")
                    
                    # Update deployment status
                    self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                        {"_id": deployment_id},
                        {"$set": {
                            "status": "artifact_deployment_failed",
                            "message": error_msg,
                            "updated_at": datetime.now()
                        }}
                    )
                    
            else:
                self._send_message(f"⚠️ Build failed for deployment {deployment_id}: {result.get('message', 'Unknown error')}")
                
        except Exception as e:
            error_msg = f"Build and deploy process failed: {str(e)}"
            self._send_message(f"❌ {error_msg}")
            
            # Update deployment status in database on error
            try:
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": deployment_id},
                    {"$set": {
                        "status": "build_failed",
                        "message": error_msg,
                        "updated_at": datetime.now()
                    }}
                )
            except Exception as db_error:
                self._send_message(f"❌ Failed to update database: {str(db_error)}")
        finally:
            # Clean up the event loop
            try:
                loop.close()
            except:
                pass
            print(f"Build and deploy thread {threading.current_thread().name} completed")

    def get_build_path(self, root_path: str) -> Optional[str]:
        """
        Check for common build directories (dist, build, output) in the root path.
        
        Args:
            root_path (str): The root directory path to search in
            
        Returns:
            Optional[str]: Complete path to the first found build directory, 
                        or None if no build directory is found
                        
        Raises:
            ValueError: If root_path doesn't exist or is not a directory
        """
        # Validate input
        if not os.path.exists(root_path):
            raise ValueError(f"Root path does not exist: {root_path}")
        
        if not os.path.isdir(root_path):
            raise ValueError(f"Root path is not a directory: {root_path}")
        
        # Common build directory names (in order of preference)
        build_dirs = ['dist', 'build', 'output']
        
        # Check each build directory
        for build_dir in build_dirs:
            build_path = os.path.join(root_path, build_dir)
            if os.path.exists(build_path) and os.path.isdir(build_path):
                return os.path.abspath(build_path)
        
        # No build directory found
        return None

    async def build(self, deployment_id: str, deployment_type: str, build_command: str, root_path: str):
        self._send_message(f"Building deployment {deployment_id} with type {deployment_type} and command {build_command} and root path {root_path}")
        
        try:
            # Initialize Docker executor
            self._send_message("Initializing Docker executor for build process...")
            docker_executor = DockerExecutor()
            
            # Validate root path
            if not os.path.exists(root_path):
                raise FileNotFoundError(f"Root path does not exist: {root_path}")
            
            self._send_message(f"Starting build process in directory: {root_path}")
            self._send_message(f"Executing build command: {build_command}")
            
            # Create custom callback for streaming output
            build_callback = self.BuildOutputCallback(self._send_message)
            
            # Create subprocess with streaming callback
            subprocess = await docker_executor.create_subprocess_shell(
                cmd=build_command,
                callback=build_callback,
                work_directory=root_path,
                one_shot=True  # Exit after command completion
            )
            
            # Set a reasonable timeout for build commands (30 minutes)
            timeout = 1800  # 30 minutes
            
            # Wait for the build process to complete with streaming output
            self._send_message("🚀 Build started - streaming live output...")
            
            try:
                return_code, output = await subprocess.wait(timeout=timeout)
            except asyncio.TimeoutError:
                self._send_message(f"❌ Build process timed out after {timeout} seconds")
                await subprocess.terminate()
                raise Exception(f"Build process timed out after {timeout} seconds")
            
            # Get final exit code from callback (more reliable)
            final_exit_code = build_callback.exit_code if build_callback.exit_code is not None else return_code
            
            if final_exit_code == 0:
                self._send_message("🎉 Build completed successfully!")
                
                # Update deployment status in database
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": deployment_id},
                    {"$set": {
                        "status": "build_success",
                        "message": "Build completed successfully",
                        "build_path": root_path,
                        "updated_at": datetime.now()
                    }}
                )
                
                return {
                    "status": "success",
                    "message": "Build completed successfully",
                    "return_code": final_exit_code
                }
            else:
                error_msg = f"Build failed with return code {final_exit_code}"
                self._send_message(f"💥 {error_msg}")
                
                # Update deployment status in database
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": deployment_id},
                    {"$set": {
                        "status": "build_failed",
                        "message": error_msg,
                        "build_path": root_path,
                        "updated_at": datetime.now()
                    }}
                )
                
                return {
                    "status": "failed",
                    "message": error_msg,
                    "return_code": final_exit_code
                }
                
        except FileNotFoundError as e:
            error_msg = f"Path error: {str(e)}"
            self._send_message(f"❌ {error_msg}")
            
            # Update deployment status in database
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "build_failed",
                    "message": error_msg,
                    "updated_at": datetime.now()
                }}
            )
            
            return {
                "status": "failed",
                "message": error_msg,
                "return_code": -1
            }
            
        except Exception as e:
            error_msg = f"Build process failed: {str(e)}"
            self._send_message(f"❌ {error_msg}")
            
            # Update deployment status in database
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "build_failed",
                    "message": error_msg,
                    "updated_at": datetime.now()
                }}
            )
            
            return {
                "status": "failed",
                "message": error_msg,
                "return_code": -1
            }
        
        finally:
            # Clean up Docker executor if needed
            try:
                if 'docker_executor' in locals():
                    docker_executor.cleanup()
            except Exception as cleanup_error:
                self._send_message(f"Warning: Docker executor cleanup failed: {str(cleanup_error)}")

    @run_in_daemon_thread
    def _handle_start_deploy(self, data: Dict[str, Any]):
        """Handle start deployment request - runs in daemon thread"""
        print(f"Handling start deployment command with input: {data}")
        if not data.get("id", ""):
            data["id"] = str(uuid.uuid4())[:8]
        
        # Ensure we have a valid branch name
        branch_name = data.get("branch_name", "")
        if not branch_name or len(branch_name.strip()) == 0:
            branch_name = "kavia-main"  # Default to 'main' if branch name is empty
            data["branch_name"] = branch_name
            self._send_message(f"Using default branch name '{branch_name}' as none was provided")
        
        self._send_message("Deployment started")
        
        # Get task details first to include project_id in data
        task_details = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})
        
        # Convert project_id to string if it's not already
        project_id = task_details.get("project_id", "")
        if not isinstance(project_id, str):
            project_id = str(project_id)
        
        # Add project_id and other task details to data
        data["project_id"] = project_id
        data["app_id"] = str(task_details.get("app_id", ""))
        
        # ✅ CREATE INITIAL DEPLOYMENT RECORD HERE (before build starts)
        deployment_model = DeploymentModel(
            _id=str(data.get("id", "")),
            deployment_id=str(data.get("id", "")),
            project_id=str(project_id),  # Use the converted string project_id
            task_id=str(self.task_id),
            app_id=str(task_details.get("app_id", "")),
            branch_name=str(branch_name),  # Use validated branch name
            app_url=str(data.get("app_url", "")),
            custom_domain="",
            artifact_path=str(data.get("artifact_path", "")),
            command=str(data.get("command", "")),
            root_path=str(data.get("root_path", "")),
            build_path="",  # Initialize with empty string
            job_id="",  # Initialize with empty string
            status="processing",  # Initial status
            message="Deployment started",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # Log what we're storing in the database
        self._send_message(f"Storing initial deployment record with branch_name={branch_name}")
        
        # Insert initial document in database
        self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
            {"_id": data.get("id", "")},
            {"$set": deployment_model.model_dump()},
            upsert=True
        )
        
        # Start build process in this daemon thread
        self._send_message("🚀 Starting build process...")
        self._send_message(f"🔧 Using project_id: {project_id}")
        self._run_build_and_deploy(
            deployment_id=data.get("id", ""), 
            deployment_type=data.get("deployment_type", "web"), 
            build_command=data.get("command", ""), 
            root_path=data.get("root_path", ""),
            data=data
        )
        
        # ✅ REMOVED: No longer overwriting the status at the end!

    @run_in_daemon_thread
    def _handle_deployment_status(self, data: Dict[str, Any]):
        """Handle deployment status request - runs in daemon thread"""
        print(f"Handling deployment status command with input: {data}")
        # Placeholder for actual deployment status logic
        self._send_message("Deployment status updated")
        self.ws_client.send_message("deployment_status", data)
        
        status = data.get("status", "").lower()
        # Build success
        if status == "success":
            self._send_message("Deployment completed successfully")
            data["status"] = "deploying"
            self.ws_client.send_message("deployment_status", data)
            self.handle_deploy_artifact(data.get("build_path", ""), self.ws_client, data)
        else:
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": status, 
                    "message": data.get("message", ""),
                    "build_path": data.get("build_path", ""),
                    "updated_at": datetime.now()
                }}
            )
    
    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Dynamically handle deployment commands - handlers run in daemon threads"""
        print(f"Handling deployment command: {command} with input: {input_data}")
        handler = self._command_map.get(command)
        if handler:
            # Handler is decorated with @run_in_daemon_thread, so it returns immediately
            # and runs in background. Any exceptions are handled within the daemon thread.
            handler(input_data or {})
            return True
        return False

    def _send_message(self, content: str):
        """Helper method to send messages through websocket and update DB"""
        # Truncate content if it exceeds 2500 characters
        if len(content) > 2500:
            content = content[:2497] + "..."
            
        message_obj = Message(
            content=content, 
            sender="Deployment", 
            timestamp=generate_timestamp()
        )
        print(message_obj)
        if self.ws_client is not None:
            self.ws_client.send_message("deployment_output", message_obj.to_dict())

        if self.db is not None:
            # Update in database
            self.db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {
                    "$push": {
                        "messages": message_obj.to_dict()
                    }
                }
            )
    
    def _get_root_directory(self):
        """Get the root workspace directory for the current task"""
        print(f"[DeploymentController] Getting root directory for task_id={self.task_id}")
        if os.getenv("LOCAL_DEBUG"):
            if self.task_id.startswith("code-generation") or self.task_id.startswith("cg"): 
                return "/tmp/kavia/workspace/code-generation"
            return f"/tmp/kavia/workspace/{self.task_id}"
        if self.task_id.startswith("code-generation") or self.task_id.startswith("cg"): 
            return "/home/<USER>/workspace/code-generation"
        return f"/home/<USER>/workspace/{self.task_id}"
    
    @run_in_daemon_thread
    def _handle_list_dir(self, input_data: Dict[str, Any]):
        """Handle listing directory contents - runs in daemon thread"""
        try:
            base_dir = input_data.get("base_dir", "")
            
            # If base_dir is "root_folder", use the root directory
            if base_dir == "root_folder":
                dir_path = self._get_root_directory()
            else:
                # Otherwise, join with the root directory if it's not an absolute path
                if os.path.isabs(base_dir):
                    dir_path = base_dir
                else:
                    dir_path = os.path.join(self._get_root_directory(), base_dir)
            
            # Check if directory exists
            if not os.path.exists(dir_path):
                self._send_message(f"Directory not found: {dir_path}")
                self.ws_client.send_message("dir_list", {"error": f"Directory not found: {dir_path}"})
                return
            
            # List files and directories
            dir_contents = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                item_type = "folder" if os.path.isdir(item_path) else "file"
                dir_contents.append({"name": item, "type": item_type})
            
            # Send directory listing to client
            self.ws_client.send_message("dir_list", {
                "path": dir_path,
                "contents": dir_contents
            })
            
            # Format message for display
            message = f"**Directory Listing for {dir_path}:**\n\n"
            for item in dir_contents:
                icon = "📁" if item["type"] == "folder" else "📄"
                message += f"{icon} {item['name']}\n"
            
            self._send_message(message)
        except Exception as e:
            error_message = f"Error listing directory: {str(e)}"
            self._send_message(error_message)
            self.ws_client.send_message("dir_list", {"error": error_message}) 
    
    @run_in_daemon_thread
    def _handle_manifest(self, input_data: Dict[str, Any]):
        """Handle manifest reading and parsing - runs in daemon thread"""
        try:
            # Get the root directory
            root_dir = self._get_root_directory()
            manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
            
            self._send_message(f"Reading manifest file from: {manifest_path}")
            
            # Check if manifest file exists
            if not os.path.exists(manifest_path):
                error_msg = f"Manifest file not found: {manifest_path}"
                self._send_message(error_msg)
                self.ws_client.send_message("manifest", {"error": error_msg})
                return
            
            # Read and parse the YAML manifest file
            with open(manifest_path, 'r', encoding='utf-8') as file:
                manifest_data = yaml.safe_load(file)
            
            if not manifest_data or 'containers' not in manifest_data:
                error_msg = "Invalid manifest format: 'containers' section not found"
                self._send_message(error_msg)
                self.ws_client.send_message("manifest", {"error": error_msg})
                return
            
            # Parse containers and format response
            response = {}
            containers = manifest_data.get('containers', [])
            
            for container in containers:
                container_name = container.get('container_name', '')
                container_type = container.get('container_type', '')
                workspace = container.get('workspace', '')
                
                if not container_name or not container_type:
                    self._send_message(f"Skipping container with missing name or type: {container}")
                    continue
                
                base_path = f'{workspace}/{container_name}'
                
                container_info = {
                    "base_path": base_path,
                    "container_name": container_name,
                    "framework": container.get('framework', ''),
                    "type": container_type,
                    "ports": container.get('ports', ''),
                    "buildCommand": container.get('buildCommand', ''),
                    "startCommand": container.get('startCommand', ''),
                    "lintCommand": container.get('lintCommand', ''),
                    "env": container.get('env', {}),
                    "container_details": container.get('container_details', {})
                }
                
                # Add to response under the container type
                if container_type in response:
                    # If multiple containers of same type, convert to list or handle as needed
                    if not isinstance(response[container_type], list):
                        response[container_type] = [response[container_type]]
                    response[container_type].append(container_info)
                else:
                    response[container_type] = container_info
            
            # Send successful response 
            self._send_message(f"Successfully parsed manifest with {len(containers)} containers")
            
            # Add project info directly to response
            response["project_name"] = manifest_data.get('overview', {}).get('project_name', '')
            response["description"] = manifest_data.get('overview', {}).get('description', '')
            
            self.ws_client.send_message("manifest", response)
            
        except yaml.YAMLError as e:
            error_msg = f"Error parsing YAML manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("manifest", {"error": error_msg})
        except Exception as e:
            error_msg = f"Error reading manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("manifest", {"error": error_msg})
    
    def _handle_save_configuration(self, input_data: Dict[str, Any]):
        """Handle saving environment configuration to manifest file"""
        try:
            # Get the root directory
            root_dir = self._get_root_directory()
            manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
            
            self._send_message(f"Reading manifest file from: {manifest_path}")
            
            # Check if manifest file exists
            if not os.path.exists(manifest_path):
                error_msg = f"Manifest file not found: {manifest_path}"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Read and parse the YAML manifest file
            with open(manifest_path, 'r', encoding='utf-8') as file:
                manifest_data = yaml.safe_load(file)
            
            if not manifest_data or 'containers' not in manifest_data:
                error_msg = "Invalid manifest format: 'containers' section not found"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Get environment data from input
            env_data = input_data.get('env_data', {})
            container_name = input_data.get('container_name', '')
            
            if not env_data:
                error_msg = "No environment data provided"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            if not container_name:
                error_msg = "No container name provided"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Find the container by name
            containers = manifest_data.get('containers', [])
            container_found = False
            
            for container in containers:
                if container.get('container_name') == container_name:
                    container_found = True
                    
                    # Get existing environment or initialize if not present
                    existing_env = container.get('env', {})
                    if existing_env is None:
                        existing_env = {}
                    
                    # Replace environment variables completely (not merge)
                    container['env'] = env_data.copy()
                    
                    # Count new, updated, and removed variables
                    new_vars = [key for key in env_data.keys() if key not in existing_env]
                    updated_vars = [key for key in env_data.keys() if key in existing_env and existing_env[key] != env_data[key]]
                    removed_vars = [key for key in existing_env.keys() if key not in env_data]
                    
                    self._send_message(f"✅ Updated environment variables for container: {container_name}")
                    
                    if new_vars:
                        self._send_message(f"📝 Added {len(new_vars)} new environment variables:")
                        for key in new_vars:
                            self._send_message(f"   ➕ {key}: {env_data[key]}")
                    
                    if updated_vars:
                        self._send_message(f"🔄 Updated {len(updated_vars)} existing environment variables:")
                        for key in updated_vars:
                            self._send_message(f"   🔄 {key}: {existing_env[key]} → {env_data[key]}")
                    
                    if removed_vars:
                        self._send_message(f"🗑️ Removed {len(removed_vars)} environment variables:")
                        for key in removed_vars:
                            self._send_message(f"   ➖ {key}: {existing_env[key]}")
                    
                    self._send_message(f"📊 Total environment variables in container: {len(env_data)}")
                    
                    break
            
            if not container_found:
                error_msg = f"Container '{container_name}' not found in manifest"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Write the updated manifest back to file
            try:
                with open(manifest_path, 'w', encoding='utf-8') as file:
                    yaml.dump(manifest_data, file, default_flow_style=False, allow_unicode=True, sort_keys=False)
                
                self._send_message(f"✅ Successfully saved configuration to manifest file")
                
                # Send success response
                response = {
                    "status": "success",
                    "message": f"Environment variables saved for container: {container_name}",
                    "container_name": container_name,
                    "env_variables_count": len(env_data),
                    "env_variables": env_data,
                    "changes": {
                        "added": len(new_vars),
                        "updated": len(updated_vars),
                        "removed": len(removed_vars)
                    }
                }
                
                self.ws_client.send_message("save_configuration", response)
                
            except Exception as write_error:
                error_msg = f"Error writing manifest file: {str(write_error)}"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
        except yaml.YAMLError as e:
            error_msg = f"Error parsing YAML manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("save_configuration", {"error": error_msg})
        except Exception as e:
            error_msg = f"Error saving configuration: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("save_configuration", {"error": error_msg})
    
    def setup_instant_custom_domain(self, amplify_client, app_id: str, 
                               project_id: str, branch_name: str, project_name: str = "", deployment_id: str = "") -> dict:
        """Set up custom domain using wildcard certificate - INSTANT!"""
        try:
            # Clean project name first - moved this to the top
            if project_name:
                # Convert to lowercase and replace underscores/spaces with hyphens
                clean_project_name = project_name.lower()
                clean_project_name = clean_project_name.replace("_", "-").replace(" ", "-")
                # Remove any special characters except hyphens
                clean_project_name = "".join(c for c in clean_project_name if c.isalnum() or c == "-")
                # Collapse multiple hyphens into single hyphen
                while "--" in clean_project_name:
                    clean_project_name = clean_project_name.replace("--", "-")
                # Remove leading/trailing hyphens
                clean_project_name = clean_project_name.strip("-")
                # Add stage suffix only for develop and qa environments
                if settings.STAGE in ["develop", "qa"]:
                    stage_suffix = "dev" if settings.STAGE == "develop" else "qa"
                    clean_project_name = f"{clean_project_name}-{stage_suffix}"
            else:
                clean_project_name = "application"  # Default if no project name

            self._send_message(f"Cleaned project name: {clean_project_name}")

            # First check if deployment already has custom domain and project name
            existing_deployment_record = None
            if deployment_id:
                existing_deployment_record = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": deployment_id})
            
            # Check if we already have custom domain and project name in the database
            if existing_deployment_record and existing_deployment_record.get("custom_domain") and existing_deployment_record.get("project_name"):
                existing_custom_domain = existing_deployment_record.get("custom_domain")
                existing_project_name = existing_deployment_record.get("project_name")
                
                self._send_message(f"🔄 Found existing custom domain setup:")
                self._send_message(f"   • Project name: {existing_project_name}")
                self._send_message(f"   • Custom domain: {existing_custom_domain}")
                
                # Extract subdomain prefix from existing custom domain
                if existing_custom_domain.startswith("https://") and existing_custom_domain.endswith(".kavia.app"):
                    subdomain_prefix = existing_custom_domain.replace("https://", "").replace(".kavia.app", "")
                    custom_url = existing_custom_domain
                    clean_project_name = existing_project_name
                else:
                    self._send_message("⚠️ Existing custom domain format unexpected, creating new one")
                    # Fall back to creating new domain below
                    existing_deployment_record = None
            
            # Only create new custom domain if we don't have existing one
            if not existing_deployment_record or not existing_deployment_record.get("custom_domain"):
                # Get all existing subdomains for this project
                existing_subdomains = list(self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find({
                    "custom_domain": {"$exists": True, "$ne": ""},
                    "subdomain": {"$exists": True, "$ne": ""}
                }, {"subdomain": 1}))

                # Check if database is empty or no subdomains exist
                if not existing_subdomains:
                    self._send_message("No existing subdomains found, using clean project name")
                    subdomain_prefix = clean_project_name
                else:
                    used_subdomains = set()
                    for doc in existing_subdomains:
                        if doc.get("subdomain"):
                            used_subdomains.add(doc["subdomain"])

                    self._send_message(f"Found existing subdomains: {list(used_subdomains)}")

                    # Check if base name is available
                    if clean_project_name not in used_subdomains:
                        subdomain_prefix = clean_project_name
                    else:
                        # Find the next available number
                        number = 1
                        while f"{clean_project_name}-{number}" in used_subdomains:
                            number += 1
                        subdomain_prefix = f"{clean_project_name}-{number}"

                self._send_message(f"new subdomain: {subdomain_prefix}")

                custom_url = f"https://{subdomain_prefix}.kavia.app"
            
            self._send_message(f"⚡ Setting up INSTANT custom project name: {project_name}")
            self._send_message(f"⚡ Setting up INSTANT custom domain: {custom_url}")

            # Store the subdomain information only if we created a new domain
            if not existing_deployment_record or not existing_deployment_record.get("custom_domain"):
                self._send_message(f"💾 Storing new custom domain configuration in database")
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": deployment_id},
                    {"$set": {
                        "project_name": clean_project_name,
                        "app_url": custom_url,
                        "custom_domain": custom_url,
                        "updated_at": datetime.now()
                    }}
                )
            else:
                self._send_message(f"💾 Using existing custom domain configuration from database")
            
            # Check existing domain associations
            try:
                existing_domains = amplify_client.list_domain_associations(appId=app_id)
                domain_associations = existing_domains.get('domainAssociations', [])
                
                # Look for existing kavia.app domain
                kavia_domain = None
                for domain_assoc in domain_associations:
                    if domain_assoc.get('domainName') == 'kavia.app':
                        kavia_domain = domain_assoc
                        break
                
                if kavia_domain:
                    # Check if our subdomain already exists
                    existing_subdomains = [
                        sub.get('prefix', '') 
                        for sub in kavia_domain.get('subDomains', [])
                    ]
                    
                    if subdomain_prefix in existing_subdomains:
                        self._send_message(f"✅ Subdomain {subdomain_prefix} already configured")
                    else:
                        self._send_message(f"📝 Adding new subdomain {subdomain_prefix} to existing domain")
                        
                        # Actually update the domain association with the new subdomain
                        try:
                            # Get all existing subdomains
                            existing_subdomain_settings = []
                            for sub in kavia_domain.get('subDomains', []):
                                existing_subdomain_settings.append({
                                    'prefix': sub.get('prefix', ''),
                                    'branchName': sub.get('branchName', '')
                                })
                            
                            # Add our new subdomain
                            existing_subdomain_settings.append({
                                'prefix': subdomain_prefix,
                                'branchName': branch_name
                            })
                            
                            self._send_message(f"🔄 Updating domain association with {len(existing_subdomain_settings)} subdomains")
                            
                            # Update the domain association
                            response = amplify_client.update_domain_association(
                                appId=app_id,
                                domainName="kavia.app",
                                subDomainSettings=existing_subdomain_settings,
                                enableAutoSubDomain=False
                            )
                            
                            self._send_message(f"✅ Successfully added subdomain {subdomain_prefix} to existing domain")
                            
                        except Exception as update_error:
                            self._send_message(f"⚠️ Failed to update domain association: {str(update_error)}")
                            self._send_message("🔄 Trying to create new domain association instead...")
                            
                            # Fallback: try to create new domain association
                            try:
                                response = amplify_client.create_domain_association(
                                    appId=app_id,
                                    domainName="kavia.app",
                                    subDomainSettings=[{
                                        'prefix': subdomain_prefix,
                                        'branchName': branch_name
                                    }],
                                    enableAutoSubDomain=False
                                )
                                
                                self._send_message(f"✅ Created new domain association for {subdomain_prefix}.kavia.app")
                            except Exception as create_error:
                                self._send_message(f"❌ Failed to create domain association: {str(create_error)}")
                                # Continue anyway - subdomain might still work
                       
                else:
                    # Create new domain association
                    self._send_message("🔧 Creating domain association with wildcard certificate...")
                    response = amplify_client.create_domain_association(
                        appId=app_id,
                        domainName="kavia.app",
                        subDomainSettings=[{
                            'prefix': subdomain_prefix,
                            'branchName': branch_name
                        }],
                        enableAutoSubDomain=False
                    )
                    
                    self._send_message(f"✅ Domain association created for {subdomain_prefix}.kavia.app")
            
            except Exception as domain_error:
                error_msg = str(domain_error)
                self._send_message(f"⚠️ Domain association error: {error_msg}")
                
                if "DomainAlreadyAssociatedException" in error_msg:
                    self._send_message("ℹ️ Domain already associated with another app - this is often fine")
                    # This is often fine - the subdomain may still work
                elif "LimitExceededException" in error_msg:
                    self._send_message("⚠️ Domain association limit reached")
                    return {'status': 'error', 'message': 'Domain limit exceeded'}
                elif "BadRequestException" in error_msg:
                    self._send_message("⚠️ Bad request - domain configuration issue")
                else:
                    self._send_message(f"⚠️ Unexpected domain error: {error_msg}")
                
                # Continue with success even if domain association had issues
                # The subdomain might still work due to wildcard certificate
            
            # Always log the final status
            self._send_message(f"🔧 Domain setup completed for: {custom_url}")
            self._send_message(f"🎯 Final custom domain URL: {custom_url}")
            self._send_message("⚡ Using wildcard certificate - no SSL wait time needed!")
            
            return {
                'status': 'instant_success',
                'url': custom_url,
                'setup_time': '< 10 seconds',
                'subdomain_prefix': subdomain_prefix
            }
            
        except Exception as e:
            self._send_message(f"❌ Error setting up custom domain: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    @run_in_daemon_thread
    def setup_custom_domain_background(self, amplify_client, amplify_app_id: str, 
                                     deployment_details: dict, data: dict, job_id: str, 
                                     branch_name: str, amplify_url: str):
        """Set up custom domain in background after Amplify URL is already sent - runs in daemon thread"""
        try:
            self._send_message("🔧 Setting up custom domain in background...")
            
            USE_CUSTOM_DOMAIN = True
            project_id = deployment_details.get("project_id", "") or data.get("project_id", "")
            
            # Get project name from deployment details or data
            project_name = deployment_details.get("project_name", "") or data.get("project_name", "")
            
            # If still no project name, try to get it from manifest
            if not project_name:
                try:
                    root_dir = self._get_root_directory()
                    manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
                    if os.path.exists(manifest_path):
                        with open(manifest_path, 'r', encoding='utf-8') as file:
                            manifest_data = yaml.safe_load(file)
                            project_name = manifest_data.get('overview', {}).get('project_name', '')
                            if project_name:
                                self._send_message(f"Found project name in manifest custom domain name: {project_name}")
                except Exception as e:
                    self._send_message(f"Warning: Could not read project name from manifest: {str(e)}")
            
            self._send_message(f"Found project name in manifest custom project name: {project_name}")

            if project_id and USE_CUSTOM_DOMAIN:
                # Verify wildcard certificate first
                if self.verify_wildcard_certificate():
                    self._send_message("🚀 Setting up custom domain with wildcard certificate...")
                    
                    domain_result = self.setup_instant_custom_domain(
                        amplify_client, amplify_app_id, project_id, branch_name, project_name, data.get("id", "")
                    )
                    
                    if domain_result['status'] == 'instant_success':
                        custom_domain_url = domain_result['url']
                        subdomain_prefix = domain_result.get('subdomain_prefix', '')
                        self._send_message(f"✅ Custom domain configured: {custom_domain_url}")
                        self._send_message("⚡ Starting health check for custom domain availability...")
                        
                        try:
                            # No need of health check for custom domain
                            health_result = {
                                'success': True,
                                'attempts': 1,
                                'total_wait_time': 0,
                                'final_result': {
                                    'response_time': 0
                                }
                            }
                            
                            if health_result['success']:
                                self._send_message(f"🎉 Custom domain is now accessible!")
                                self._send_message(f"🔗 You now have both URLs available:")
                                self._send_message(f"   • Amplify URL: {amplify_url}")
                                self._send_message(f"   • Custom domain: {custom_domain_url}")
                                
                                # Send updated status with custom domain as primary
                                self.ws_client.send_message("deployment_status", {
                                    "id": data.get("id", ""),
                                    "status": "custom_domain_ready",
                                    "job_id": job_id,
                                    "message": f"Custom domain is live and accessible!",
                                    "app_url": custom_domain_url,  # Custom domain as primary
                                    "custom_domain": custom_domain_url,
                                    "subdomain": subdomain_prefix,  # Include subdomain info
                                    "amplify_url": amplify_url,  # Include Amplify URL
                                    "app_id": amplify_app_id,
                                    "branch": branch_name,
                                    "health_check": {
                                        "attempts": health_result['attempts'],
                                        "total_wait_time": health_result['total_wait_time'],
                                        "response_time": health_result.get('final_result', {}).get('response_time', 0)
                                    }
                                })
                                
                                # Update database with custom domain as primary URL
                                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                                    {"_id": data.get("id", "")},
                                    {"$set": {
                                        "status": "success",  # ✅ Preserve success status!
                                        "message": "Custom domain is live and accessible!",
                                        "app_url": custom_domain_url,  # Custom domain as primary
                                        "custom_domain": custom_domain_url,
                                        "subdomain": subdomain_prefix,  # Store the subdomain
                                        "amplify_url": amplify_url,  # Store Amplify URL as backup
                                        "updated_at": datetime.now()
                                    }}
                                )
                                
                            else:
                                self._send_message(f"⚠️ Custom domain not yet accessible after health check")
                                self._send_message(f"   • Health check failed after {health_result['attempts']} attempts")
                                self._send_message(f"   • Total wait time: {health_result['total_wait_time']:.1f} seconds")
                                self._send_message(f"🔗 Continue using Amplify URL: {amplify_url}")
                                self._send_message(f"💡 Custom domain may become available in a few more minutes")
                                
                                # Send status indicating health check failed but domain is configured
                                self.ws_client.send_message("deployment_status", {
                                    "id": data.get("id", ""),
                                    "status": "custom_domain_pending",
                                    "job_id": job_id,
                                    "message": f"Custom domain configured but not yet accessible. Use Amplify URL for now.",
                                    "app_url": amplify_url,  # Keep Amplify URL as primary
                                    "custom_domain_url": custom_domain_url,
                                    "amplify_url": amplify_url,
                                    "app_id": amplify_app_id,
                                    "branch": branch_name,
                                    "health_check": {
                                        "success": False,
                                        "attempts": health_result['attempts'],
                                        "total_wait_time": health_result['total_wait_time']
                                    }
                                })
                                
                                # Update database - deployment is still successful, just custom domain pending
                                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                                    {"_id": data.get("id", "")},
                                    {"$set": {
                                        "status": "success",  # ✅ Deployment successful, domain pending
                                        "message": "Deployment successful! Custom domain configured but not yet accessible.",
                                        "app_url": amplify_url,  # Keep Amplify URL as primary for now
                                        "custom_domain": custom_domain_url,
                                        "amplify_url": amplify_url,
                                        "updated_at": datetime.now()
                                    }}
                                )
                                
                        except Exception as health_error:
                            self._send_message(f"❌ Health check failed with error: {str(health_error)}")
                            self._send_message(f"🔗 Continue using Amplify URL: {amplify_url}")
                            
                            # Send error status
                            self.ws_client.send_message("deployment_status", {
                                "id": data.get("id", ""),
                                "status": "custom_domain_error",
                                "job_id": job_id,
                                "message": f"Custom domain health check failed. Use Amplify URL.",
                                "app_url": amplify_url,  # Keep Amplify URL as primary
                                "amplify_url": amplify_url,
                                "app_id": amplify_app_id,
                                "branch": branch_name,
                                "error": str(health_error)
                            })
                            
                            # Update database - deployment successful, custom domain had issues
                            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                                {"_id": data.get("id", "")},
                                {"$set": {
                                    "status": "success",  # ✅ Deployment successful
                                    "message": "Deployment successful! Custom domain setup had issues, use Amplify URL.",
                                    "app_url": amplify_url,  # Amplify URL as primary
                                    "amplify_url": amplify_url,
                                    "updated_at": datetime.now()
                                }}
                            )
                            
                    else:
                        self._send_message(f"⚠️ Custom domain setup failed: {domain_result.get('message', 'Unknown error')}")
                        self._send_message(f"✅ App remains available at Amplify URL: {amplify_url}")
                        
                        # Update database - deployment successful, custom domain setup failed
                        self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                            {"_id": data.get("id", "")},
                            {"$set": {
                                "status": "success",  # ✅ Deployment successful
                                "message": "Deployment successful! Custom domain setup failed, use Amplify URL.",
                                "app_url": amplify_url,  # Amplify URL as primary
                                "amplify_url": amplify_url,
                                "updated_at": datetime.now()
                            }}
                        )
                else:
                    # Certificate not ready, use Amplify URL
                    self._send_message(f"⚠️ Wildcard certificate not ready, continuing with Amplify URL: {amplify_url}")
                    
                    # Update database - deployment successful, certificate not ready
                    self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                        {"_id": data.get("id", "")},
                        {"$set": {
                            "status": "success",  # ✅ Deployment successful
                            "message": "Deployment successful! Wildcard certificate not ready, use Amplify URL.",
                            "app_url": amplify_url,  # Amplify URL as primary
                            "amplify_url": amplify_url,
                            "updated_at": datetime.now()
                        }}
                    )
            else:
                self._send_message("ℹ️ Custom domain not configured (no project ID), using Amplify URL only")
                
                # Update database - deployment successful, no custom domain
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": data.get("id", "")},
                    {"$set": {
                        "status": "success",  # ✅ Deployment successful
                        "message": "Deployment successful! No custom domain configured, use Amplify URL.",
                        "app_url": amplify_url,  # Amplify URL as primary
                        "amplify_url": amplify_url,
                        "updated_at": datetime.now()
                    }}
                )
                
        except Exception as e:
            self._send_message(f"⚠️ Custom domain setup error: {str(e)}")
            self._send_message(f"✅ App remains available at Amplify URL: {amplify_url}")
            
            # Update database - deployment successful, custom domain setup had exception
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": "success",  # ✅ Deployment successful
                    "message": f"Deployment successful! Custom domain setup error: {str(e)}. Use Amplify URL.",
                    "app_url": amplify_url,  # Amplify URL as primary
                    "amplify_url": amplify_url,
                    "updated_at": datetime.now()
                }}
            )

    def verify_wildcard_certificate(self) -> bool:
        """Verify that the wildcard certificate is valid and ready"""
        try:
            acm_client = boto3.client('acm',
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            response = acm_client.describe_certificate(
                CertificateArn=self.WILDCARD_CERTIFICATE_ARN
            )
            
            status = response['Certificate']['Status']
            if status != 'ISSUED':
                self._send_message(f"⚠️ Wildcard certificate status: {status}")
                return False
                
            return True
            
        except Exception as e:
            self._send_message(f"Could not verify wildcard certificate: {str(e)}")
            return False
    
    def check_domain_availability(self, url: str, max_timeout: int = 120) -> bool:
        """Check if a domain is available by making HTTP requests
        
        Args:
            url: The URL to check availability for
            max_timeout: Maximum time in seconds to wait for availability
            
        Returns:
            bool: True if domain is available, False otherwise
        """
        self._send_message(f"🔍 Checking availability of {url}")
        start_time = time.time()
        available = False
        
        # Try up to max_timeout seconds
        while time.time() - start_time < max_timeout:
            try:
                # Set a short timeout for each individual request
                response = requests.get(url, timeout=2, allow_redirects=True)
                
                if response.status_code < 500:  # Consider any non-server error as available
                    self._send_message(f"✅ Domain {url} is available! (Status: {response.status_code})")
                    available = True
                    break
                else:
                    self._send_message(f"⏳ Domain still initializing... (Status: {response.status_code})")
            except requests.RequestException as e:
                self._send_message(f"⏳ Domain not yet available: {str(e)}")
            
            # Wait before trying again
            time.sleep(2)
        
        elapsed = time.time() - start_time
        if not available:
            self._send_message(f"⚠️ Domain not available after {elapsed:.1f} seconds. It may take longer to initialize.")
        else:
            self._send_message(f"✅ Domain became available after {elapsed:.1f} seconds")
        
        return available
    
    def handle_deploy_artifact(self, build_path: str, ws_client: WebSocketClient, data: Dict[str, Any]):
        """Handle deploying an artifact
        
        Zips all files in the build_path directory, uploads to S3 bucket,
        and triggers a deployment using AWS Amplify
        
        Args:
            build_path: Path to the directory containing build artifacts
            ws_client: WebSocket client for sending status updates
        """
        
        self.ws_client = ws_client
        
        try:
            # Create a friendly deployment name based on timestamp
            timestamp = int(time.time())
            if data.get("id", ""):
                deployment_id = str(data.get("id", ""))
            else:
                deployment_id = str(uuid.uuid4())[:8]
            artifact_name = f"deployment-{timestamp}-{deployment_id}.zip"
            
            self._send_message(f"Starting deployment process for pre-built artifacts in {build_path}")
            
            # Validate build path exists
            if not os.path.exists(build_path):
                raise FileNotFoundError(f"Build path does not exist: {build_path}")
            
            # Check if build directory contains index.html (crucial for web apps)
            if not os.path.exists(os.path.join(build_path, 'index.html')):
                self._send_message("⚠️ Warning: No index.html found in build directory. This may cause issues with deployment.")
            
            # Create a temporary zip file
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
                temp_path = temp_file.name
                
            # Zip all files in the build_path
            self._send_message("Compressing build artifacts...")
            with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                file_count = 0
                for root, _, files in os.walk(build_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Get the relative path inside the build_path
                        rel_path = os.path.relpath(file_path, build_path)
                        zipf.write(file_path, rel_path)
                        file_count += 1
                
                self._send_message(f"Added {file_count} files to deployment package")
            
            # Upload to S3
            self._send_message(f"Uploading deployment artifact to S3...")
            s3_client = boto3.client('s3', 
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            # Get the bucket name from settings or use default
            bucket_name = settings.S3_BUILD_ARTIFACTS_BUCKET
            
            with open(temp_path, 'rb') as file_data:
                s3_client.upload_fileobj(
                    file_data, 
                    bucket_name, 
                    f"deployments/{artifact_name}"
                )
            
            s3_url = f"s3://{bucket_name}/deployments/{artifact_name}"
            self._send_message(f"Artifact uploaded to {s3_url}")
            
            # Create Amplify client
            amplify_client = boto3.client('amplify',
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            # Start deployment with Amplify - this assumes the app is already set up in Amplify
            # Get app ID from environment or pass it as parameter
            deployment_details = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": data.get("id", "")})
            if not deployment_details:
                self._send_message("Warning: No deployment details found in database")
                deployment_details = {}
                
            # First check if app_id exists in the incoming data
            amplify_app_id = data.get("app_id", "")
            
            # If not in incoming data, try to get from deployment details
            if not amplify_app_id:
                amplify_app_id = deployment_details.get("app_id", "")
                
            # Log the app ID for debugging
            self._send_message(f"Retrieved app_id from data/DB: '{amplify_app_id}'")
            
            # Strip any whitespace and validate app ID is not empty
            if amplify_app_id:
                amplify_app_id = amplify_app_id.strip()
                
            # Verify the app ID by checking if it exists in Amplify
            app_exists = False
            if amplify_app_id:
                try:
                    # Try to get the app to verify it exists
                    amplify_client.get_app(appId=amplify_app_id)
                    app_exists = True
                    self._send_message(f"Verified existing Amplify app with ID: {amplify_app_id}")
                except Exception as e:
                    self._send_message(f"App ID validation failed: {str(e)}")
                    self._send_message("Will create a new app instead.")
                    amplify_app_id = ""  # Reset to empty to trigger new app creation
            
            # Get branch name from data or DB
            branch_name = data.get("branch_name", "")
            if not branch_name:
                branch_name = deployment_details.get("branch_name", "")
                
            # Ensure branch name is valid
            if not branch_name or len(branch_name.strip()) == 0:
                branch_name = "kavia-main"  # Default to 'main' if branch name is empty
                self._send_message(f"Using default branch name '{branch_name}' as none was provided")
                
            # Log all the values for debugging
            self._send_message(f"Final values: app_id='{amplify_app_id}', branch_name='{branch_name}', app_exists={app_exists}")
            
            # Create a new Amplify app if no valid app ID is provided or app doesn't exist
            if not amplify_app_id or not app_exists:
                self._send_message("Creating new Amplify app for manual deployment...")
                app_name = f"app-{deployment_id}"
                
                # Improved custom rules with more specific patterns for SPA routing
                custom_rules = [
                    # Serve index.html for routes without file extensions (SPA routes)
                    {
                        "source": "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|woff2|ttf|map|json)$)([^.]+$)/>",
                        "target": "/index.html",
                        "status": "200"
                    },
                    # Serve static files directly
                    {
                        "source": "/<*>",
                        "target": "/<*>",
                        "status": "200"
                    }
                ]
                
                # Create the new Amplify app with improved settings
                app_response = amplify_client.create_app(
                    name=app_name,
                    description=f"Manual deployment app {deployment_id}",
                    platform="WEB",
                    customRules=custom_rules,
                    enableBranchAutoBuild=False,  # Manual deployments only
                    buildSpec=json.dumps({
                        "version": 1,
                        "frontend": {
                            "phases": {
                                "build": {
                                    "commands": [
                                        "echo 'Using pre-built artifacts'"
                                    ]
                                }
                            },
                            "artifacts": {
                                "baseDirectory": "/",
                                "files": ["**/*"]
                            }
                        }
                    })
                )
                # app
                amplify_app_id = app_response['app']['appId']
                self._send_message(f"Created new Amplify app with ID: {amplify_app_id}")
                
                # Ensure we have a valid branch name
                if not branch_name or len(branch_name.strip()) == 0:
                    branch_name = "kavia-main"  # Default to 'main' if branch name is empty
                    self._send_message(f"Using default branch name '{branch_name}' as none was provided")
                
                # Create a new branch for the app
                branch_response = amplify_client.create_branch(
                    appId=amplify_app_id,
                    branchName=branch_name,
                    stage="PRODUCTION",  # Mark as production branch
                    enableAutoBuild=False  # Manual deployments only
                )
                
                self._send_message(f"Created branch '{branch_name}' for app ID: {amplify_app_id}")
            
            # Ensure we have a valid branch name before starting deployment
            if not branch_name or len(branch_name.strip()) == 0:
                branch_name = "kavia-main"  # Default to 'main' if branch name is empty
                self._send_message(f"Using default branch name '{branch_name}' as none was provided")
            
            # Generate a pre-signed URL for the S3 object (more reliable for deployments)
            s3_parts = s3_url.replace("s3://", "").split("/", 1)
            bucket = s3_parts[0]
            key = s3_parts[1]
            
            # Generate a pre-signed URL that's valid for 1 hour
            presigned_url = s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket, 'Key': key},
                ExpiresIn=3600
            )
            
            self._send_message(f"Generated temporary access URL for deployment artifact")
            
            # Start deployment job using pre-signed URL for reliability
            try:
                self._send_message(f"Initiating manual deployment for app ID: {amplify_app_id}, branch: {branch_name}")
                response = amplify_client.start_deployment(
                    appId=amplify_app_id,
                    branchName=branch_name,
                    sourceUrl=presigned_url
                )
                
                job_id = response.get('jobId', 'Unknown')
                self._send_message(f"Amplify manual deployment started. Job ID: {job_id}")
            except Exception as e:
                error_details = str(e)
                self._send_message(f"Error starting deployment: {error_details}")
                raise
            
            # Wait longer for deployment initialization before checking status
            self._send_message("Waiting for deployment to initialize...")
            
            # Poll for app details with increased timeout and more detailed information
            app_url = ""
            max_attempts = 5
            for attempt in range(1, max_attempts + 1):
                try:
                    # Wait between attempts with exponential backoff
                    wait_time = 5 * attempt
                    self._send_message(f"Waiting {wait_time} seconds for deployment initialization (attempt {attempt}/{max_attempts})...")
                    time.sleep(wait_time)
                    
                    # Get app details
                    app_details = amplify_client.get_app(appId=amplify_app_id)
                    app_info = app_details['app']
                    
                    # Log detailed app information for debugging
                    self._send_message(f"App name: {app_info.get('name')}")
                    self._send_message(f"App ID: {app_info.get('appId')}")
                    self._send_message(f"Default domain: {app_info.get('defaultDomain', 'Not available yet')}")
                    
                    domain = app_info.get('defaultDomain', '')
                    if domain:
                        # First, send the Amplify URL immediately so users can access the app
                        amplify_url = f"https://{branch_name}.{domain}"
                        production_url = f"https://{domain}"
                        
                        self._send_message(f"✅ Application deployed to Amplify: {amplify_url}")
                        self._send_message(f"Default Amplify URL: {production_url}")
                        
                        # ALWAYS send Amplify URL first - this is the immediate access URL
                        self.ws_client.send_message("deployment_status", {
                            "id": data.get("id", ""),
                            "status": "success",
                            "job_id": job_id,
                            "message": f"✅ Deployment successful! App is live at Amplify URL.",
                            "app_url": amplify_url,  # Always Amplify URL first
                            "amplify_url": amplify_url,
                            "app_id": amplify_app_id,
                            "branch": branch_name,
                            "deployment_type": "amplify_ready"
                        })
                        
                        # Perform health check on Amplify URL
                        self._send_message("🔍 Performing health check on Amplify URL...")
                        amplify_health_result = self.check_url_health_sync(amplify_url)
                        
                        if amplify_health_result['success']:
                            self._send_message(f"🎉 Amplify URL is accessible!")
                        else:
                            self._send_message(f"⚠️ Amplify URL health check failed, but continuing with custom domain setup")
                        
                        # Set app_url to Amplify URL for database update
                        app_url = amplify_url
                        
                        # Now try to set up custom domain in background (separate process)
                        self.setup_custom_domain_background(amplify_client, amplify_app_id, deployment_details, data, job_id, branch_name, amplify_url)
                        
                        break
                    else:
                        self._send_message("Domain not yet available, will retry...")
                    
                except Exception as e:
                    self._send_message(f"Could not retrieve app URL (attempt {attempt}/{max_attempts}): {str(e)}")
                    if attempt == max_attempts:
                        self._send_message("❗ Failed to get app URL after maximum attempts. Check AWS Console directly.")
                        self._send_message("Your app may still be deploying correctly despite this error.")
            
            # Check job status to verify deployment is proceeding
            if job_id != 'Unknown':
                try:
                    job_details = amplify_client.get_job(appId=amplify_app_id, branchName=branch_name, jobId=job_id)
                    job_status = job_details.get('job', {}).get('status', 'UNKNOWN')
                    job_summary = job_details.get('job', {}).get('summary', {})
                    
                    self._send_message(f"Deployment job status: {job_status}")
                    if job_summary:
                        self._send_message(f"Job summary: {json.dumps(job_summary, default=str)}")
                except Exception as e:
                    self._send_message(f"Could not retrieve job status: {str(e)}")
            
            # Clean up the temporary file
            os.unlink(temp_path)

            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": "success",
                    "message": f"Pre-built artifacts deployed with job ID: {job_id}",
                    "app_url": app_url,
                    "app_id": amplify_app_id,
                    "branch": branch_name,
                    "job_id": job_id,
                    "updated_at": datetime.now()
                }}
            )
            
            return {
                "status": "success",
                "type": "deployment",
                "message": f"Pre-built artifacts deployed with job ID: {job_id}",
                "job_id": job_id,
                "artifact_path": s3_url,
                "app_url": app_url,
                "app_id": amplify_app_id,
                "branch": branch_name,
                "note": "Deployment in progress. URL may not be immediately accessible."
            }
            
        except Exception as e:
            error_msg = f"Error during deployment: {str(e)}"
            self._send_message(error_msg)
            
            # Send error status via WebSocket
            self.ws_client.send_message("deployment_status", {
                "status": "failed",
                "message": error_msg
            })
            
            # Return failure
            return {
                "status": "failed",
                "message": error_msg
            }