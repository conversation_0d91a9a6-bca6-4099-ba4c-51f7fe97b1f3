# app/routes/figma_route.py
from llm_wrapper.core.llm_interface import LLMInterface
from fastapi import APIRouter, Depends, HTTPException, Query,UploadFile, File
from fastapi.responses import JSONResponse, StreamingResponse
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from app.utils.auth_utils import get_current_user as get_current_user_v1
from app.models.figma_model import FrameLink
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.figma_utils import (
    extract_file_key,
    extract_frame_data,
    extract_all_node_data,
    fetch_frame_images,
    fetch_frame_image,
    get_frame_details,
    get_figma_access_token,
    get_figma_file_data_limited,
    figma_access_token,
)
from app.core.constants import FIGMA_BASE_PATH
from app.utils.kg_inspect.knowledge_reporter import Reporter
from app.celery_app import celery_task_id, get_websocket_session, create_websocket_session,cleanup_websocket_session
from app.core.websocket import websocket_manager
import requests
import io
import zipfile
import json
import re
import base64
import time
from app.models.uiux.figma_model import FigmaModel, UserModel, FigmaRequestModel
from app.models.project.project_model import ProjectModel
from datetime import datetime
from app.connection.tenant_middleware import get_tenant_id
from app.models.tenant.settings_model import TenantSettings
from app.classes.S3Handler import S3Handler
from app.core.Settings import settings
from app.models.uiux.figma_model import (
    FigmaSizesModel,
    ProcessingStatus,
    FigmaFrameModel,
)
import asyncio
from fastapi import BackgroundTasks
import httpx
from app.utils.figma_utils import (
    get_figma_file_data_limited_async,
    fetch_frame_images_async,
    process_frame
)
from app.core.websocket.client import WebSocketClient
from app.connection.establish_db_connection import get_mongo_db
from app.utils.datetime_utils import generate_timestamp

from app.discussions.figma.tools.extraction import Extraction
import uuid
from fastapi import Body
import os

from pydantic import BaseModel
from app.models.figma_model import FigmaExtractionRequest, ImageTemplate, ExtractionTypes

from sse_starlette.sse import EventSourceResponse

from copy import copy, deepcopy

from app.core.constants import FIGMA_BASE_PATH as BASE_PATH
from app.tasks import Task, process_figma_in_celery
import traceback
from app.discussions.figma.tools.work_input_discovery_tool import WorkInputDiscovery
from llm_wrapper.core.llm_interface import LLMInterface
from pydantic import Field
from app.telemetry.logger_config import get_logger, setup_logging
from sse_starlette.sse import ServerSentEvent
from app.utils.file_utils.image_compressor import compress_image

setup_logging()

_SHOW_NAME = "figma"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)

def convert_png_to_bas64url(png_path: str):
    with open(png_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')
    
# figma_access_token.set("*********************************************")


async def get_current_user(current_user=Depends(get_current_user_v1)):
    tenant_id = get_tenant_id()
    settings = await TenantSettings.get_settings(tenant_id)
    figma_api_key = next(
        (
            setting.value
            for setting in settings.integrations.figma
            if setting.name == "figma_api_key"
        ),
        "",
    )
    figma_access_token.set(figma_api_key)
    return current_user


def get_figma_file_fize(figma_data):
    json_str = json.dumps(figma_data)
    size_bytes = len(json_str.encode("utf-8"))
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    return {
        "size_kb": round(size_kb, 2),
        "size_mb": round(size_mb, 2),
        "byte_limit": None,
        "mb_limit": None,
    }


async def process_figma_file(
    project_id: str, figma_link: FigmaRequestModel, is_new_file: bool = True
):
    """
    Common function to process Figma file for both add and update operations
    """
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)
    file_name = f"{tenant_id}-{project_id}-{file_key}.json"

    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    s3_handler = S3Handler(tenant_id)

    # Check if file exists (only for new files)
    # if is_new_file and s3_handler.is_file(file_name):
    #     raise HTTPException(status_code=400, detail="Design already linked to this project")

    # Get and process Figma file data
    data, sizes = get_figma_file_data_limited(
        figma_link.url, get_figma_access_token(), mb_limit=2
    )
    frames = extract_frame_data(data)
    frame_ids = [frame["id"] for frame in frames]
    image_urls = fetch_frame_images(file_key, frame_ids)

    frames_with_images = [
        {**frame, "imageUrl": image_urls.get(frame["id"])} for frame in frames
    ]

    file_data = {
        "frames": frames_with_images,
        "fileKey": file_key,
        "document": data["document"],
        "sizes": sizes,
    }

    # Handle S3 storage
    if is_new_file:
        s3_handler.add_file(file_name, json.dumps(file_data))
    else:
        s3_handler.update_file(file_name, json.dumps(file_data))

    return file_data, sizes



# Helper function to retry image fetch
async def retry_fetch_image(
    file_key: str, frame_id: str, max_retries: int = 3
) -> Optional[str]:
    """
    Retry fetching frame image with exponential backoff.

    Args:
        file_key (str): Figma file key
        frame_id (str): Frame ID
        max_retries (int): Maximum number of retry attempts

    Returns:
        Optional[str]: Image URL if successful, None otherwise
    """
    for attempt in range(max_retries):
        try:
            url = f"https://api.figma.com/v1/images/{file_key}"
            params = {"ids": frame_id, "scale": 2, "format": "png"}
            headers = {"X-Figma-Token": get_figma_access_token()}

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url, params=params, headers=headers, timeout=10.0
                )
                if response.status_code == 200:
                    data = response.json()
                    return data["images"].get(frame_id)

                # Rate limit handling
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 5))
                    await asyncio.sleep(retry_after)
                    continue

        except Exception:
            pass

        # Exponential backoff
        await asyncio.sleep(2**attempt)

    return None


# async def background_process_figma_file(
#     project_id: str,
#     figma_link: FigmaRequestModel,
#     tenant_id: str,
#     figma_api_key: str,
#     is_new_file: bool = True,
# ):
#     """Background task to process Figma file with frame status tracking"""
#     print("Background task started")
#     file_logger = get_logger(__name__)
#     processed_frames = []
#     completed_count = 0
#     failed_count = 0
#     total_frames = 0
#     data = None

#     try:
#         figma_access_token.set(figma_api_key)
#         file_key = extract_file_key(figma_link.url)
#         figma_id = f"{tenant_id}-{project_id}-{file_key}"

#         llm = LLMInterface(
#             f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}",
#             instance_name=f"figma_discussion_{project_id}",
#             user_id=tenant_id,
#             project_id=project_id,
#             agent_name="FigmaExtractionAgent"
#         )

#         work_input_discovery : WorkInputDiscovery = WorkInputDiscovery(
#             callback_functions=None,
#             base_path=f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}",
#             logger=file_logger,
#             llm=llm

#         )
#         ws_client = WebSocketClient(f"figma-{project_id}", uri=settings.WEBSOCKET_URI)
#         ws_client.connect()
   

#         # Use async client for HTTP requests
#         async with httpx.AsyncClient() as client:
#             # Process Figma file data first to get total frames count
#             data, sizes = await get_figma_file_data_limited_async(
#                 client, figma_link.url, figma_api_key, mb_limit=5
#             )
#             frames = extract_frame_data(data)
#             total_frames = len(frames)

#             # Update FigmaModel with initial frame count and status
#             update_data = {
#                 "total_frames": total_frames,
#                 "completed_frames": 0,
#                 "failed_frames": 0,
#                 "sizes": FigmaSizesModel(**sizes).dict(),
#                 "time_updated": generate_timestamp(),
#             }
#             await FigmaModel.update(figma_id, update_data)

#             # Process frames and track counts
#             frame_ids = [frame["id"] for frame in frames]
#             image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
#             has_errors = False

#             for frame in frames:
#                 try:
#                     # Process frame with status tracking
#                     frame_model = await process_frame(frame, file_key, image_urls)

#                     # Convert to format matching process_figma_file
#                     # Ensure absoluteBoundingBox is properly structured
#                     bounding_box = frame.get("absoluteBoundingBox", {})
#                     if not bounding_box:
#                         # Provide default dimensions if missing
#                         bounding_box = {
#                             "x": 0,
#                             "y": 0,
#                             "width": 800,  # default width
#                             "height": 600,  # default height
#                         }

#                     processed_frame = {
#                         "id": frame["id"],
#                         "name": frame["name"],
#                         "type": frame["type"],
#                         "absoluteBoundingBox": bounding_box,
#                         "imageUrl": (
#                             frame_model.imageUrl
#                             if frame_model.status == ProcessingStatus.COMPLETED
#                             else None
#                         ),
#                         "status": frame_model.status,
#                         "error_message": (
#                             frame_model.error_message
#                             if hasattr(frame_model, "error_message")
#                             else None
#                         ),
#                         "time_updated": frame_model.time_updated,
#                         # Add fields needed for frontend
#                         "dimensions": {
#                             "width": round(bounding_box.get("width", 800)),
#                             "height": round(bounding_box.get("height", 600)),
#                         },
#                     }

#                     processed_frames.append(processed_frame)

#                     if frame_model.status == ProcessingStatus.COMPLETED:
#                         completed_count += 1
#                     elif frame_model.status == ProcessingStatus.FAILED:
#                         failed_count += 1
#                         has_errors = True

#                     if(FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000):
#                         status = ProcessingStatus.FAILED
#                     else:
#                         status = ProcessingStatus.PROCESSING

#                     # Update MongoDB with current counts
#                     update_data = {
#                         "total_frames": total_frames,
#                         "completed_frames": completed_count,
#                         "failed_frames": failed_count,
#                         "time_updated": generate_timestamp(),
#                         "sizes": FigmaSizesModel(**sizes).dict(),
#                         "status": status,
#                     }
#                     if has_errors:
#                         update_data["status"] = ProcessingStatus.PARTIALLY_COMPLETED
#                     ws_client.send_message(
#                         "figma_update",
#                         {"figma_id": figma_id, "update_data": update_data},
#                     )
#                     await FigmaModel.update(figma_id, update_data)

#                 except Exception as e:
#                     print(f"Error processing frame {frame['id']}: {str(e)}")
#                     failed_count += 1
#                     has_errors = True

#             # Store frames data in S3 in the same format as process_figma_file
#             file_data = {
#                 "frames": processed_frames,
#                 "fileKey": file_key,
#                 "document": data["document"],
#                 "sizes": sizes,
#                 "progress": {
#                     "total": total_frames,
#                     "completed": completed_count,
#                     "failed": failed_count,
#                 },
#             }
#             hashes, work_item = work_input_discovery.process_figma_json(file_data)
#             update_data["work_input_sh256"] = hashes
#             update_data["work_input"] = work_item
#             s3_handler = S3Handler(tenant_id)
#             await s3_handler.update_file_async(
#                 f"{figma_id}.json", json.dumps(file_data)
#             )

#         # Final update
#         final_status = (
#             ProcessingStatus.COMPLETED
#             if failed_count == 0
#             else ProcessingStatus.PARTIALLY_COMPLETED
#         )
#         final_update = {
#             "status": final_status,
#             "error_message": (
#                 f"{failed_count} frames failed to process" if failed_count > 0 else None
#             ),
#             "time_updated": generate_timestamp(),
#         }
#         # ws_client.send_message(
#         #     "figma_update", {"figma_id": figma_id, "update_data": final_update}
#         # )
#         await FigmaModel.update(figma_id, final_update)
#         async with httpx.AsyncClient() as client:
#             # Process Figma file data first to get total frames count
#             base_path = "/app/data"
#             if os.environ.get("LOCAL_DEBUG"):
#                 base_path = "/tmp"
            
#             attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"
#             os.makedirs(attachment_path, exist_ok=True)
#             frames = extract_all_node_data(data)
#             total_frames = len(frames)

#             # Build frame hierarchy map to find children relationships
#             frame_children_map = build_frame_children_map(data)

#             # Update FigmaModel with initial frame count and status
#             update_data = {
#                 "total_frames": total_frames,
#                 "completed_frames": 0,
#                 "failed_frames": 0,
#                 "sizes": FigmaSizesModel(**sizes).dict(),
#                 "time_updated": generate_timestamp(),
#             }

#             # Process frames and store each individually
#             frame_ids = [frame["id"] for frame in frames]
#             image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
#             has_errors = False

#             # Determine initial status based on file size
#             status = (
#                 ProcessingStatus.FAILED 
#                 if FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000 
#                 else ProcessingStatus.PROCESSING
#             )

#             # Process each frame
#             for i, frame in enumerate(frames):
#                 try:
#                     frame_model = await process_frame(frame, file_key, image_urls)
#                     frame_data = create_frame_data(frame, frame_model, frame_children_map)
                    
#                     # Store individual frame locally
#                     frame_file_path = os.path.join(attachment_path, f"figma_{frame['id']}.json")
#                     await write_json_file(frame_file_path, frame_data)

#                     # Update counts
#                     if frame_model.status == ProcessingStatus.COMPLETED:
#                         completed_count += 1
#                     elif frame_model.status == ProcessingStatus.FAILED:
#                         failed_count += 1
#                         has_errors = True

#                     # Send WebSocket update every 10 frames or at the end
#                     if (i + 1) % 10 == 0 or i == len(frames) - 1:
#                         update_data = {
#                             "total_frames": total_frames,
#                             "completed_frames": completed_count,
#                             "failed_frames": failed_count,
#                             "time_updated": generate_timestamp(),
#                             "sizes": FigmaSizesModel(**sizes).dict(),
#                             "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
#                             "progress": round(((i + 1) / total_frames) * 100, 2),
#                             "current_frame": i + 1,
#                             "message": f"Processed {i + 1}/{total_frames} frames" + (f" ({failed_count} failed)" if failed_count > 0 else "")
#                         }
                        
#                         # Send WebSocket update
#                         ws_client.send_message(
#                             "figma_update_json",
#                             {"figma_id": figma_id, "update_data": update_data}
#                         )
#                 except Exception as e:
#                     print(f"Error processing frame {frame['id']}: {str(e)}")
#                     failed_count += 1
#                     has_errors = True

#                     # Store error frame data
#                     error_frame_data = create_error_frame_data(frame, frame_children_map, str(e))
                    
#                     try:
#                         frame_file_path = os.path.join(attachment_path, f"figma_{frame['id']}.json")
#                         await write_json_file(frame_file_path, error_frame_data)
#                     except Exception as file_error:
#                         print(f"Error storing failed frame to file: {str(file_error)}")
#                         if (i + 1) % 10 == 0 or i == len(frames) - 1:
#                             update_data = {
#                                 "total_frames": total_frames,
#                                 "completed_frames": completed_count,
#                                 "failed_frames": failed_count,
#                                 "time_updated": generate_timestamp(),
#                                 "sizes": FigmaSizesModel(**sizes).dict(),
#                                 "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
#                                 "progress": round(((i + 1) / total_frames) * 100, 2),
#                                 "current_frame": i + 1,
#                                 "message": f"Processed {i + 1}/{total_frames} frames ({failed_count} failed)"
#                             }
                            
#                             # Send WebSocket update
#                             ws_client.send_message(
#                                 "figma_update_json",
#                                 {"figma_id": figma_id, "update_data": update_data}
#                             )

#             # Store metadata file
#             metadata = create_metadata(file_key, sizes, total_frames, completed_count, failed_count)
#             metadata_file_path = os.path.join(attachment_path, "metadata.json")
#             await write_json_file(metadata_file_path, metadata)

#     except Exception as e:
#         print(f"Error in background task: {str(e)}")
#         # Store any successfully processed frames
#         if processed_frames and data:
#             file_data = {
#                 "frames": processed_frames,
#                 "fileKey": file_key,
#                 "document": data["document"],
#                 "sizes": sizes,
#                 "progress": {
#                     "total": total_frames,
#                     "completed": completed_count,
#                     "failed": failed_count,
#                 },
#             }
#             s3_handler = S3Handler(tenant_id)
#             await s3_handler.update_file_async(
#                 f"{figma_id}.json", json.dumps(file_data)
#             )

#             # Update status to partially completed if any frames were processed
#             if completed_count > 0:
#                 error_update = {
#                     "status": ProcessingStatus.PARTIALLY_COMPLETED,
#                     "error_message": str(e),
#                     "time_updated": generate_timestamp(),
#                 }
#             else:
#                 error_update = {
#                     "status": ProcessingStatus.FAILED,
#                     "error_message": str(e),
#                     "time_updated": generate_timestamp(),
#                 }
#             # ws_client.send_message(
#             #     "figma_update", {"figma_id": figma_id, "update_data": error_update}
#             # )
#             await FigmaModel.update(figma_id, error_update)
#     finally:
#         ws_client.disconnect()
async def process_nodes_optimized_second_phase(
    all_nodes: List[Dict],
    attachment_path: str,
    project_id: str,
    tenant_id:str,
    file_key: str,
    image_urls: Dict,
    frame_children_map: Dict,
    ws_client,
    figma_id: str,
    total_nodes: int,
    max_concurrent: int = 100,
    figma_logger = None
):
    """
    Optimized second phase processing with async semaphore
    Maintains exact same WebSocket message format and behavior
    """
    from datetime import datetime

    # Use the passed logger
    if figma_logger is None:
        # Fallback to creating a new logger if none provided
         # Extract project_id from figma_id
        figma_logger, _ = setup_figma_logger(project_id,tenant_id, figma_id)

    phase2_id = f"processing_json"
    figma_logger.info(f"[{phase2_id}] PHASE 2 STARTED: Processing {total_nodes} nodes with max_concurrent={max_concurrent}")
    figma_logger.info(f"[{phase2_id}] Parameters - attachment_path: {attachment_path}, file_key: {file_key}, figma_id: {figma_id}")

    # Create semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(max_concurrent)
    figma_logger.info(f"[{phase2_id}] Semaphore created with limit {max_concurrent}")

    # Counters for tracking
    json_processed_count = 0
    progress_lock = asyncio.Lock()
    batch_size = 10
    figma_logger.info(f"[{phase2_id}] Initialized counters - batch_size: {batch_size}")

    async def process_single_node(node: Dict, node_index: int) -> bool:
        """Process a single node with semaphore control"""
        nonlocal json_processed_count

        node_id = node.get('id', f'unknown_{node_index}')
        node_name = node.get('name', 'unknown')

        async with semaphore:
            try:
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}/{total_nodes}: Processing node ID: {node_id}, Name: {node_name}")

                # Process each node normally (frames and non-frames)
                frame_model = await process_frame(node, file_key, image_urls)
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Frame processed with status: {frame_model.status}")

                # Create frame data for local storage
                frame_data = create_frame_data(node, frame_model, frame_children_map)
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Frame data created")

                # Store individual node file
                frame_file_path = os.path.join(attachment_path, f"figma_{node['id']}.json")
                await write_json_file(frame_file_path, frame_data)
                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: JSON file written to {frame_file_path}")

                # Update counter thread-safely
                async with progress_lock:
                    json_processed_count += 1
                    current_count = json_processed_count

                # Send WebSocket update every 10 processed nodes or on last iteration
                if (current_count % batch_size == 0) or (node_index == len(all_nodes) - 1):
                    json_progress_percentage = round((current_count / total_nodes) * 100, 1)
                    figma_logger.info(f"[{phase2_id}] Progress update: {current_count}/{total_nodes} ({json_progress_percentage}%)")

                    json_update = {
                        "type": "figma_update_json",
                        "processed_count": current_count,
                        "total_count": total_nodes,
                        "percentage": json_progress_percentage,
                        "time_updated": generate_timestamp(),
                    }

                    ws_client.send_message(
                        "figma_update_json",
                        {"figma_id": figma_id, "update_data": json_update}
                    )
                    figma_logger.info(f"[{phase2_id}] WebSocket progress update sent")

                figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Processing completed successfully")
                return True

            except Exception as e:
                figma_logger.error(f"[{phase2_id}] Node {node_index+1} ERROR: Error processing node {node_id}: {str(e)}")
                print(f"Error processing node {node['id']}: {str(e)}")

                # Store error frame data
                try:
                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Attempting to store error frame data")
                    error_frame_data = create_error_frame_data(node, frame_children_map, str(e))
                    frame_file_path = os.path.join(attachment_path, f"figma_{node['id']}.json")
                    await write_json_file(frame_file_path, error_frame_data)
                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Error frame data stored")

                    # Update counter thread-safely
                    async with progress_lock:
                        json_processed_count += 1
                        current_count = json_processed_count

                    # Send WebSocket update every 10 processed nodes or on last iteration
                    if (current_count % batch_size == 0) or (node_index == len(all_nodes) - 1):
                        json_progress_percentage = round((current_count / total_nodes) * 100, 1)
                        figma_logger.info(f"[{phase2_id}] Progress update (with error): {current_count}/{total_nodes} ({json_progress_percentage}%)")

                        json_update = {
                            "type": "figma_update_json",
                            "processed_count": current_count,
                            "total_count": total_nodes,
                            "percentage": json_progress_percentage,
                            "time_updated": generate_timestamp(),
                        }

                        ws_client.send_message(
                            "figma_update_json",
                            {"figma_id": figma_id, "update_data": json_update}
                        )
                        figma_logger.info(f"[{phase2_id}] WebSocket progress update sent (with error)")

                    figma_logger.info(f"[{phase2_id}] Node {node_index+1}: Error handling completed successfully")
                    return True

                except Exception as file_error:
                    figma_logger.error(f"[{phase2_id}] Node {node_index+1} CRITICAL ERROR: Error storing failed node to file: {str(file_error)}")
                    print(f"Error storing failed node to file: {str(file_error)}")
                    return False
    
    # Create tasks with node index for proper progress tracking
    figma_logger.info(f"[{phase2_id}] Creating {len(all_nodes)} concurrent tasks")
    tasks = [process_single_node(node, i) for i, node in enumerate(all_nodes)]
    figma_logger.info(f"[{phase2_id}] Tasks created, starting concurrent execution")

    # Execute all tasks concurrently
    start_time = datetime.now()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    # Count successful vs failed tasks
    successful_tasks = sum(1 for result in results if result is True)
    failed_tasks = len(results) - successful_tasks

    figma_logger.info(f"[{phase2_id}] PHASE 2 COMPLETED: Duration: {duration:.2f}s")
    figma_logger.info(f"[{phase2_id}] Results - Successful: {successful_tasks}, Failed: {failed_tasks}, Total processed: {json_processed_count}")

    return json_processed_count

async def background_process_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    tenant_id: str,
    figma_api_key: str,
    is_new_file: bool = True,
    figma_logger = None,
):
    """Background task to process Figma file with frame status tracking - optimized single pass"""
    from datetime import datetime
    start_timer = time.time()
    # Use the passed logger or create a new one if not provided
    if figma_logger is None:
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        figma_logger, _ = setup_figma_logger(project_id,tenant_id, figma_id)

    task_id = f"background_task"
    figma_logger.info(f"[{task_id}] BACKGROUND TASK STARTED")
    figma_logger.info(f"[{task_id}] Input parameters - project_id: {project_id}, tenant_id: {tenant_id}, figma_url: {figma_link.url}, is_new_file: {is_new_file}")

    print("Background task started")
    file_logger = get_logger(__name__)

    # Initialize counters and data structures
    processed_frames = []
    completed_count = 0
    failed_count = 0
    total_frames = 0
    data = None
    has_errors = False

    try:
        # Step BG1: Setup figma access token
        figma_logger.info(f"[{task_id}] Step BG1: Setting figma access token")
        figma_access_token.set(figma_api_key)
        figma_logger.info(f"[{task_id}] Step BG1 SUCCESS: Figma access token set")

        # Step BG2: Extract file key
        figma_logger.info(f"[{task_id}] Step BG2: Extracting file key from URL: {figma_link.url}")
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        figma_logger.info(f"[{task_id}] Step BG2 SUCCESS: file_key = {file_key}, figma_id = {figma_id}")

        # Step BG3: Initialize LLM Interface
        figma_logger.info(f"[{task_id}] Step BG3: Initializing LLM Interface")
        llm_base_path = f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}"
        figma_logger.info(f"[{task_id}] Step BG3a: LLM base path = {llm_base_path}")
        llm = LLMInterface(
            llm_base_path,
            instance_name=f"figma_discussion_{project_id}",
            user_id=tenant_id,
            project_id=project_id,
            agent_name="FigmaExtractionAgent"
        )
        figma_logger.info(f"[{task_id}] Step BG3 SUCCESS: LLM Interface initialized")

        # Step BG4: Initialize WorkInputDiscovery
        figma_logger.info(f"[{task_id}] Step BG4: Initializing WorkInputDiscovery")
        work_input_discovery = WorkInputDiscovery(
            callback_functions=None,
            base_path=llm_base_path,
            logger=file_logger,
            llm=llm
        )
        figma_logger.info(f"[{task_id}] Step BG4 SUCCESS: WorkInputDiscovery initialized")

        # Step BG5: Initialize WebSocket client
        figma_logger.info(f"[{task_id}] Step BG5: Initializing WebSocket client")
        ws_client_id = f"figma-{project_id}"
        figma_logger.info(f"[{task_id}] Step BG5a: WebSocket client ID = {ws_client_id}, URI = {settings.WEBSOCKET_URI}")
        ws_client = WebSocketClient(ws_client_id, uri=settings.WEBSOCKET_URI)
        connection_result = ws_client.connect()
        figma_logger.info(f"[{task_id}] Step BG5 SUCCESS: WebSocket client initialized, connection_result = {connection_result}")

        # Step BG6: Setup local storage paths
        figma_logger.info(f"[{task_id}] Step BG6: Setting up local storage paths")
        base_path = "/app/data" if not os.environ.get("LOCAL_DEBUG") else "/tmp"
        attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"
        figma_logger.info(f"[{task_id}] Step BG6a: base_path = {base_path}, attachment_path = {attachment_path}")
        os.makedirs(attachment_path, exist_ok=True)
        figma_logger.info(f"[{task_id}] Step BG6 SUCCESS: Local storage paths created")

        # Step BG7: Create HTTP client and fetch Figma data
        figma_logger.info(f"[{task_id}] Step BG7: Creating HTTP client and fetching Figma data")
        async with httpx.AsyncClient() as client:
            figma_logger.info(f"[{task_id}] Step BG7a: HTTP client created")

            # Get Figma file data
            figma_logger.info(f"[{task_id}] Step BG7b: Fetching Figma file data with mb_limit=100")
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=100
            )
            figma_logger.info(f"[{task_id}] Step BG7b SUCCESS: Figma data fetched - size_kb: {sizes.get('size_kb', 'unknown')}, size_mb: {sizes.get('size_mb', 'unknown')}")

            # Step BG8: Extract frames and nodes
            figma_logger.info(f"[{task_id}] Step BG8: Extracting frames and nodes from data")
            frames = extract_frame_data(data)  # Only FRAME types for S3
            figma_logger.info(f"[{task_id}] Step BG8a: Extracted {len(frames)} frames")
            all_nodes = extract_all_node_data(data)  # All node types for local storage
            figma_logger.info(f"[{task_id}] Step BG8b: Extracted {len(all_nodes)} total nodes")

            # Use frames count for progress tracking
            total_frames = len(frames)
            total_nodes = len(all_nodes)
            figma_logger.info(f"[{task_id}] Step BG8 SUCCESS: total_frames = {total_frames}, total_nodes = {total_nodes}")

            # Step BG9: Build frame hierarchy map
            figma_logger.info(f"[{task_id}] Step BG9: Building frame hierarchy map")
            frame_children_map = build_frame_children_map(data)
            figma_logger.info(f"[{task_id}] Step BG9 SUCCESS: Frame hierarchy map built with {len(frame_children_map)} entries")

            # Step BG10: Determine processing status
            figma_logger.info(f"[{task_id}] Step BG10: Determining processing status")
            # status = (
            #     ProcessingStatus.FAILED
            #     if FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000
            #     else ProcessingStatus.PROCESSING
            # )
            status = ProcessingStatus.PROCESSING
            figma_logger.info(f"[{task_id}] Step BG10 SUCCESS: Status set to {status}")

            # Step BG11: Initial MongoDB update
            figma_logger.info(f"[{task_id}] Step BG11: Performing initial MongoDB update")
            update_data = {
                "total_frames": total_frames,
                "completed_frames": 0,
                "failed_frames": 0,
                "sizes": FigmaSizesModel(**sizes).dict(),
                "time_updated": generate_timestamp(),
                "status": status,
            }
            await FigmaModel.update(figma_id, update_data)
            figma_logger.info(f"[{task_id}] Step BG11 SUCCESS: Initial MongoDB update completed")

            # Step BG12: Fetch frame images
            figma_logger.info(f"[{task_id}] Step BG12: Fetching frame images")
            frame_ids = [frame["id"] for frame in frames]
            figma_logger.info(f"[{task_id}] Step BG12a: Frame IDs to fetch images for: {len(frame_ids)} frames")
            image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
            figma_logger.info(f"[{task_id}] Step BG12 SUCCESS: Frame images fetched - {len(image_urls)} image URLs retrieved")

            # Step BG13: Two-phase processing
            figma_logger.info(f"[{task_id}] Step BG13: Starting two-phase processing")
            figma_logger.info(f"[{task_id}] Step BG13: Phase 1 - Process frames for S3 storage and progress tracking")
            figma_logger.info(f"[{task_id}] Step BG13: Phase 2 - Process all nodes for local storage")

            # Process frames for S3 storage
            figma_logger.info(f"[{task_id}] Step BG13-P1: Starting Phase 1 - Processing {total_frames} frames for S3")
            for i, frame in enumerate(frames):
                try:
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}: Processing frame {i+1}/{total_frames} - ID: {frame.get('id', 'unknown')}, Name: {frame.get('name', 'unknown')}")

                    # Process frame
                    frame_model = await process_frame(frame, file_key, image_urls)
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}a: Frame processed with status: {frame_model.status}")

                    # Create processed frame data for S3 aggregation
                    bounding_box = frame.get("absoluteBoundingBox", {})
                    if not bounding_box:
                        bounding_box = {
                            "x": 0,
                            "y": 0,
                            "width": 800,
                            "height": 600,
                        }
                        figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}b: Using default bounding box for frame")
                    else:
                        figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}b: Using frame bounding box: {bounding_box}")

                    processed_frame = {
                        "id": frame["id"],
                        "name": frame["name"],
                        "type": frame["type"],
                        "absoluteBoundingBox": bounding_box,
                        "imageUrl": (
                            frame_model.imageUrl
                            if frame_model.status == ProcessingStatus.COMPLETED
                            else None
                        ),
                        "status": frame_model.status,
                        "error_message": (
                            frame_model.error_message
                            if hasattr(frame_model, "error_message")
                            else None
                        ),
                        "time_updated": frame_model.time_updated,
                        "dimensions": {
                            "width": round(bounding_box.get("width", 800)),
                            "height": round(bounding_box.get("height", 600)),
                        },
                    }

                    processed_frames.append(processed_frame)
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}c: Frame data created and added to processed_frames")

                    # Update counters
                    if frame_model.status == ProcessingStatus.COMPLETED:
                        completed_count += 1
                        figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}d: Frame completed successfully, completed_count = {completed_count}")
                    elif frame_model.status == ProcessingStatus.FAILED:
                        failed_count += 1
                        has_errors = True
                        figma_logger.warning(f"[{task_id}] Step BG13-P1-F{i+1}d: Frame failed, failed_count = {failed_count}")

                    # Send progress update for each frame processed
                    current_status = (
                        ProcessingStatus.PARTIALLY_COMPLETED if has_errors
                        else status
                    )

                    progress_update = {
                        "total_frames": total_frames,
                        "completed_frames": completed_count,
                        "failed_frames": failed_count,
                        "time_updated": generate_timestamp(),
                        "sizes": FigmaSizesModel(**sizes).dict(),
                        "status": current_status,
                    }

                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}e: Sending WebSocket update - status: {current_status}")
                    # Send WebSocket update for frame processing
                    ws_client.send_message(
                        "figma_update",
                        {"figma_id": figma_id, "update_data": progress_update}
                    )

                    # Update MongoDB
                    await FigmaModel.update(figma_id, progress_update)
                    figma_logger.info(f"[{task_id}] Step BG13-P1-F{i+1}f: MongoDB updated successfully")

                except Exception as e:
                    figma_logger.error(f"[{task_id}] Step BG13-P1-F{i+1} ERROR: Error processing frame {frame.get('id', 'unknown')}: {str(e)}")
                    print(f"Error processing frame {frame['id']}: {str(e)}")
                    failed_count += 1
                    has_errors = True
            # Step BG14: Transition to JSON processing phase
            figma_logger.info(f"[{task_id}] Step BG14: Phase 1 completed, transitioning to JSON processing phase")
            final_update = {
                "status": ProcessingStatus.PROCESSING_JSON,
                "error_message": None,
                "time_updated": generate_timestamp(),
            }
            figma_logger.info(f"[{task_id}] Step BG14a: Sending PROCESSING_JSON status update via WebSocket")
            ws_client.send_message(
                "figma_update", {"figma_id": figma_id, "update_data": final_update}
            )
            figma_logger.info(f"[{task_id}] Step BG14 SUCCESS: Status updated to PROCESSING_JSON")

            # Step BG15: Process all nodes for local storage
            figma_logger.info(f"[{task_id}] Step BG15: Starting Phase 2 - Processing all nodes for local storage")
            max_concurrent = min(100, max(10, len(all_nodes) // 10))
            figma_logger.info(f"[{task_id}] Step BG15a: Calculated max_concurrent = {max_concurrent} for {total_nodes} nodes")

            # Process all nodes for local storage (optimized)
            figma_logger.info(f"[{task_id}] Step BG15b: Starting optimized node processing")
            json_processed_count = await process_nodes_optimized_second_phase(
                all_nodes, attachment_path,project_id,tenant_id, file_key, image_urls, frame_children_map,
                ws_client, figma_id, total_nodes, max_concurrent, figma_logger
            )
            figma_logger.info(f"[{task_id}] Step BG15 SUCCESS: Node processing completed - {json_processed_count} nodes processed")

            # Step BG16: Store aggregated data in S3
            figma_logger.info(f"[{task_id}] Step BG16: Storing aggregated data in S3")
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }
            figma_logger.info(f"[{task_id}] Step BG16a: File data structure created with {len(processed_frames)} frames")

            # Step BG17: Process work input discovery
            figma_logger.info(f"[{task_id}] Step BG17: Processing work input discovery")
            hashes, work_item = work_input_discovery.process_figma_json(file_data, figma_logger)
            figma_logger.info(f"[{task_id}] Step BG17 SUCCESS: Work input discovery completed - {len(hashes) if hashes else 0} hashes generated")

            # Step BG18: Upload to S3
            figma_logger.info(f"[{task_id}] Step BG18: Uploading file data to S3")
            s3_handler = S3Handler(tenant_id)
            await s3_handler.update_file_async(
                f"{figma_id}.json", json.dumps(file_data)
            )
            figma_logger.info(f"[{task_id}] Step BG18 SUCCESS: File uploaded to S3 as {figma_id}.json")

            # Step BG19: Store metadata file locally
            figma_logger.info(f"[{task_id}] Step BG19: Storing metadata file locally")
            metadata = create_metadata(file_key, sizes, total_nodes, len(all_nodes), 0)  # All nodes processed locally
            metadata_file_path = os.path.join(attachment_path, "metadata.json")
            await write_json_file(metadata_file_path, metadata)
            figma_logger.info(f"[{task_id}] Step BG19 SUCCESS: Metadata file stored at {metadata_file_path}")

            # Step BG20: Final status update
            figma_logger.info(f"[{task_id}] Step BG20: Performing final status update")
            final_status = (
                ProcessingStatus.COMPLETED
                if failed_count == 0
                else ProcessingStatus.PARTIALLY_COMPLETED
            )
            figma_logger.info(f"[{task_id}] Step BG20a: Final status determined as {final_status} (failed_count: {failed_count})")

            final_update = {
                "status": final_status,
                "error_message": (
                    f"{failed_count} frames failed to process" if failed_count > 0 else None
                ),
                "time_updated": generate_timestamp(),
            }
            figma_logger.info(f"[{task_id}] Step BG20b: Sending final WebSocket update")
            ws_client.send_message(
                "figma_update", {"figma_id": figma_id, "update_data": final_update}
            )

            figma_logger.info(f"[{task_id}] Step BG20c: Updating MongoDB with final status")
            await FigmaModel.update(figma_id, final_update)
            figma_logger.info(f"[{task_id}] Step BG20 SUCCESS: Final status update completed")

    except Exception as e:
        figma_logger.error(f"[{task_id}] BACKGROUND TASK FAILED: Exception occurred - {str(e)}")
        figma_logger.error(f"[{task_id}] Exception type: {type(e).__name__}")
        import traceback
        figma_logger.error(f"[{task_id}] Full traceback: {traceback.format_exc()}")
        print(f"Error in background task: {str(e)}")

        # Step BG-ERROR1: Store any successfully processed frames
        figma_logger.info(f"[{task_id}] Step BG-ERROR1: Attempting to store successfully processed frames")
        if processed_frames and data:
            figma_logger.info(f"[{task_id}] Step BG-ERROR1a: Found {len(processed_frames)} processed frames to store")
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }

            try:
                figma_logger.info(f"[{task_id}] Step BG-ERROR1b: Uploading partial data to S3")
                s3_handler = S3Handler(tenant_id)
                await s3_handler.update_file_async(
                    f"{figma_id}.json", json.dumps(file_data)
                )
                figma_logger.info(f"[{task_id}] Step BG-ERROR1b SUCCESS: Partial data uploaded to S3")
            except Exception as s3_error:
                figma_logger.error(f"[{task_id}] Step BG-ERROR1b FAILED: S3 upload error - {str(s3_error)}")

            # Step BG-ERROR2: Update status based on what was completed
            figma_logger.info(f"[{task_id}] Step BG-ERROR2: Updating status based on completion (completed: {completed_count}, failed: {failed_count})")
            if completed_count > 0:
                error_status = ProcessingStatus.PARTIALLY_COMPLETED
                figma_logger.info(f"[{task_id}] Step BG-ERROR2a: Setting status to PARTIALLY_COMPLETED")
                error_update = {
                    "status": error_status,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }
            else:
                error_status = ProcessingStatus.FAILED
                figma_logger.info(f"[{task_id}] Step BG-ERROR2a: Setting status to FAILED")
                error_update = {
                    "status": error_status,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }

            try:
                figma_logger.info(f"[{task_id}] Step BG-ERROR2b: Updating MongoDB with error status: {str(e)}")
                await FigmaModel.update(figma_id, error_update)
                figma_logger.info(f"[{task_id}] Step BG-ERROR2b SUCCESS: MongoDB updated with error status")
            except Exception as db_error:
                figma_logger.error(f"[{task_id}] Step BG-ERROR2b FAILED: MongoDB update error - {str(db_error)}")

            try:
                figma_logger.info(f"[{task_id}] Step BG-ERROR2c: Sending error status via WebSocket")
                ws_client.send_message(
                    "figma_update", {"figma_id": figma_id, "update_data": error_update}
                )
                figma_logger.info(f"[{task_id}] Step BG-ERROR2c SUCCESS: Error status sent via WebSocket")
            except Exception as ws_error:
                figma_logger.error(f"[{task_id}] Step BG-ERROR2c FAILED: WebSocket send error - {str(ws_error)}")
        else:
            figma_logger.warning(f"[{task_id}] Step BG-ERROR1 SKIPPED: No processed frames or data to store")

    finally:
        
        figma_logger.info(f"[{task_id}] CLEANUP: Disconnecting WebSocket client")
        try:
            end_timer = time.time()
            time_taken = end_timer - start_timer
            figma_logger.info(f"[{task_id}] TOTAL TIME TAKEN: {time_taken:.2f} seconds total_node: {total_nodes} total_frame: {total_frames}")
            ws_client.disconnect()
            figma_logger.info(f"[{task_id}] CLEANUP SUCCESS: WebSocket client disconnected")
        except Exception as cleanup_error:
            figma_logger.error(f"[{task_id}] CLEANUP ERROR: WebSocket disconnect error - {str(cleanup_error)}")

        figma_logger.info(f"[{task_id}] BACKGROUND TASK COMPLETED")
def classify_exception_status_code(exception: Exception) -> int:
    """
    Classify exceptions and return appropriate HTTP status codes.
    
    Args:
        exception: The caught exception
        
    Returns:
        int: Appropriate HTTP status code
    """
    exception_type = type(exception).__name__
    exception_str = str(exception).lower()
    
    # Network/HTTP related errors (502 Bad Gateway)
    if isinstance(exception, (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError)):
        return 502
    
    # HTTP client errors from external APIs
    if isinstance(exception, httpx.HTTPStatusError):
        # For Figma API authentication errors, return 403
        if exception.response.status_code == 403 and 'figma.com' in str(exception.request.url):
            return 403
        # For other 4xx errors from external APIs that indicate client errors
        elif 400 <= exception.response.status_code < 500:
            return exception.response.status_code
        # For 5xx errors from external APIs, return 502 (bad gateway)
        else:
            return 502
    
    # Authentication/Authorization errors (401/403)
    if any(keyword in exception_str for keyword in ['unauthorized', 'forbidden', 'authentication', 'permission']):
        return 401 if 'unauthorized' in exception_str or 'authentication' in exception_str else 403
    
    # Validation/Bad Request errors (400)
    if (isinstance(exception, (ValueError, TypeError)) or 
        any(keyword in exception_str for keyword in ['invalid', 'bad request', 'validation', 'missing required'])):
        return 400
    
    # Resource not found (404)
    if any(keyword in exception_str for keyword in ['not found', 'does not exist', 'no such']):
        return 404
    
    # Conflict errors (409)
    if any(keyword in exception_str for keyword in ['already exists', 'conflict', 'duplicate']):
        return 409
    
    # Rate limiting (429)
    if any(keyword in exception_str for keyword in ['rate limit', 'too many requests', 'quota exceeded']):
        return 429
    
    # Database/Connection errors (503 Service Unavailable)
    if any(keyword in exception_str for keyword in ['database', 'connection', 'timeout', 'unavailable']):
        return 503
    
    # Default to 500 for unclassified exceptions
    return 500

def setup_figma_logger(project_id: str, tenant_id: str, figma_id: str):
    """Setup centralized figma logger with project-specific path"""
    import logging
    import os
    from datetime import datetime

    # Create logs directory structure: /tmp/kavia/[project_id]/logs/
    base_path = "/app/data" if not os.environ.get("LOCAL_DEBUG") else "/tmp"
    log_dir = f"{base_path}/{tenant_id}/{project_id}/logs"
    os.makedirs(log_dir, exist_ok=True)

    # Setup dedicated logger
    logger_name = f'figma_{figma_id}'
    figma_logger = logging.getLogger(logger_name)
    figma_logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in figma_logger.handlers[:]:
        figma_logger.removeHandler(handler)

    # Create log file path
    log_file = os.path.join(log_dir, f'{figma_id}.log')

    # Create file handler for .log file
    file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    
    # Create simple formatter with only timestamp and message
    class SimpleFormatter(logging.Formatter):
        def format(self, record):
            # Create timestamp
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
            
            # Format: TIMESTAMP - MESSAGE
            log_line = f"{timestamp} - {record.getMessage()}"
            
            return log_line

    # Set simple formatter
    formatter = SimpleFormatter()
    file_handler.setFormatter(formatter)
    
    # Add handler to logger
    figma_logger.addHandler(file_handler)
    figma_logger.propagate = False

    return figma_logger, log_file

############################################################################################################
@router.post("/add_figma_file_v2")
async def add_figma_file_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
):
    from datetime import datetime

    # Get tenant_id and file_key early for logger setup
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)

    if not file_key:
        return JSONResponse(
            status_code=400, content={"message": "Invalid Figma link"}
        )

    figma_id = f"{tenant_id}-{project_id}-{file_key}"

    # Setup centralized logger
    figma_logger, log_file_path = setup_figma_logger(project_id,tenant_id, figma_id)

    request_id = f"Add_figma_file"
    figma_logger.info(f"[{request_id}] Starting add_figma_file_v2 endpoint")
    figma_logger.info(f"[{request_id}] Log file: {log_file_path}")
    figma_logger.info(f"[{request_id}] Input parameters - project_id: {project_id}, figma_url: {figma_link.url}, figma_name: {figma_link.name}")

    try:
        # Step 1: Get tenant ID (already done above)
        figma_logger.info(f"[{request_id}] Step 1: Getting tenant ID")
        figma_logger.info(f"[{request_id}] Step 1 SUCCESS: tenant_id = {tenant_id}")

        # Step 2: Extract file key from Figma URL (already done above)
        figma_logger.info(f"[{request_id}] Step 2: Extracting file key from URL: {figma_link.url}")
        figma_logger.info(f"[{request_id}] Step 2 SUCCESS: file_key = {file_key}")

        # Step 3: Create figma_id (already done above)
        figma_logger.info(f"[{request_id}] Step 3: Created figma_id = {figma_id}")

        # Step 4: Check if design already exists
        figma_logger.info(f"[{request_id}] Step 4: Checking if design already exists")
        existing_design = await FigmaModel.get_one(figma_id)
        if existing_design:
            figma_logger.warning(f"[{request_id}] Step 4 FAILED: Design already exists with id {figma_id}")
            return JSONResponse(
                status_code=400, content={"message": "Design already exists"}
            )
        figma_logger.info(f"[{request_id}] Step 4 SUCCESS: Design does not exist, proceeding")

        # Step 5: Create UserModel
        figma_logger.info(f"[{request_id}] Step 5: Creating UserModel from current_user")
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )
        figma_logger.info(f"[{request_id}] Step 5 SUCCESS: UserModel created for user {user_model.username}")

        # Step 6: Get tenant settings
        figma_logger.info(f"[{request_id}] Step 6: Getting tenant settings")
        settings = await TenantSettings.get_settings(tenant_id)
        figma_logger.info(f"[{request_id}] Step 6 SUCCESS: Retrieved tenant settings")

        # Step 7: Extract Figma API key
        figma_logger.info(f"[{request_id}] Step 7: Extracting Figma API key from settings")
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            figma_logger.error(f"[{request_id}] Step 7 FAILED: Figma API key not configured in tenant settings")
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )
        figma_logger.info(f"[{request_id}] Step 7 SUCCESS: Figma API key found (length: {len(figma_api_key)})")

        # Step 8: Fetch Figma file data
        figma_logger.info(f"[{request_id}] Step 8: Fetching Figma file data with httpx client")
        async with httpx.AsyncClient() as client:
            figma_logger.info(f"[{request_id}] Step 8a: Created httpx AsyncClient")
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=100
            )
            figma_logger.info(f"[{request_id}] Step 8b SUCCESS: Retrieved Figma data - size_kb: {sizes.get('size_kb', 'unknown')}, size_mb: {sizes.get('size_mb', 'unknown')}")

        # Step 9: Determine processing status
        figma_logger.info(f"[{request_id}] Step 9: Determining processing status")
        # if(sizes["size_kb"] < 6000):
        #     status = ProcessingStatus.PENDING
        # else:
        #     status = ProcessingStatus.FAILED
        status = ProcessingStatus.PENDING
        figma_logger.info(f"[{request_id}] Step 9 SUCCESS: Status set to {status}")

        # Step 10: Create figma_data dictionary
        figma_logger.info(f"[{request_id}] Step 10: Creating figma_data dictionary")
        figma_data = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "file_key": file_key,
            "id": figma_id,
            "name": figma_link.name,
            "url": figma_link.url,
            "added_by": user_model.dict(),
            "status": status,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "time_created": generate_timestamp(),
            "time_updated": generate_timestamp(),
            "sizes": FigmaSizesModel(**sizes).dict(),
        }
        figma_logger.info(f"[{request_id}] Step 10 SUCCESS: figma_data dictionary created")

        # Step 11: Add design to project
        figma_logger.info(f"[{request_id}] Step 11: Adding design to project {project_id}")
        success = await ProjectModel.add_design_id(project_id, figma_id)
        if not success:
            figma_logger.error(f"[{request_id}] Step 11 FAILED: Failed to add design to project")
            return JSONResponse(
                status_code=500, content={"message": "Failed to add design to project"}
            )
        figma_logger.info(f"[{request_id}] Step 11 SUCCESS: Design added to project")

        # Step 12: Create design document in database
        figma_logger.info(f"[{request_id}] Step 12: Creating design document in database")
        created_design = await FigmaModel.create(figma_data)
        if not created_design:
            figma_logger.error(f"[{request_id}] Step 12 FAILED: Failed to create design document")
            # Cleanup project if design creation fails
            figma_logger.info(f"[{request_id}] Step 12 CLEANUP: Removing design from project due to creation failure")
            await ProjectModel.remove_design_id(project_id, figma_id)
            return JSONResponse(
                status_code=500, content={"message": "Failed to create design"}
            )
        figma_logger.info(f"[{request_id}] Step 12 SUCCESS: Design document created in database")

        # Step 13: Setup background task
        figma_logger.info(f"[{request_id}] Step 13: Setting up background task")
        try:
            figma_logger.info(f"[{request_id}] Step 13a: Re-fetching tenant settings for background task")
            settings = await TenantSettings.get_settings(tenant_id)
            figma_api_key = next(
                (
                    setting.value
                    for setting in settings.integrations.figma
                    if setting.name == "figma_api_key"
                ),
            )
            figma_logger.info(f"[{request_id}] Step 13b: Re-extracted Figma API key for background task")

            if figma_api_key and status != ProcessingStatus.FAILED:
                figma_logger.info(f"[{request_id}] Step 13c: Preparing figma_link_dict for background task")
                figma_link_dict = {
                    "name": figma_link.name,
                    "url": figma_link.url
                }

                figma_logger.info(f"[{request_id}] Step 13d: Adding background task to process Figma file")
                # celery_task = Task.schedule_task(
                #     process_figma_in_celery,
                #     project_id=project_id,
                #     figma_link=figma_link_dict,
                #     figma_api_key=figma_api_key,
                #     is_new_file=True,
                #     tenant_id=get_tenant_id(),
                #     current_user=current_user.get("cognito:username"),
                # )
                background_tasks.add_task(
                    background_process_figma_file,
                    project_id,
                    figma_link,
                    tenant_id,
                    figma_api_key,
                    True,
                    figma_logger,  # Pass the logger instance
                )
                figma_logger.info(f"[{request_id}] Step 13e SUCCESS: Background task added successfully")
            else:
                figma_logger.warning(f"[{request_id}] Step 13 SKIPPED: Background task not added - api_key_exists: {bool(figma_api_key)}, status: {status}")
        except Exception as e:
            figma_logger.error(f"[{request_id}] Step 13 ERROR: Error setting up background task: {str(e)}")
            print(f"Error setting up background task: {str(e)}")  # Just log the error

        # Step 14: Return success response
        figma_logger.info(f"[{request_id}] Step 14: Returning success response")
        response_content = {
            "message": "Figma file processing started",
            "status": ProcessingStatus.PENDING,
            "id": figma_id,
        }
        figma_logger.info(f"[{request_id}] Step 14 SUCCESS: Endpoint completed successfully - figma_id: {figma_id}")

        return JSONResponse(
            status_code=202,
            content=response_content,
        )

    except Exception as e:
        status_code = classify_exception_status_code(e)
        figma_logger.error(f"[{request_id}] ENDPOINT FAILED: Exception occurred - {str(e)}")
        figma_logger.error(f"[{request_id}] Exception type: {type(e).__name__}")
        figma_logger.error(f"[{request_id}] Status code determined: {status_code}")
        print(f"Error in add_figma_file_v2: {str(e)}")  # Debug print
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )


@router.put("/update_figma_file_v2")
async def update_figma_file_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"

        # Check if design exists
        existing_design = await FigmaModel.get_one(figma_id)
        if not existing_design:
            return JSONResponse(
                status_code=404, content={"message": "Design not found"}
            )

        # Reset the design status and counters
        update_data = {
            "status": ProcessingStatus.PENDING,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "error_message": None,
            "time_updated": generate_timestamp(),
        }

        updated = await FigmaModel.update(figma_id, update_data)
        if not updated:
            return JSONResponse(
                status_code=500, content={"message": "Failed to update design status"}
            )

        # Get Figma API key and start background process
        try:
            settings = await TenantSettings.get_settings(tenant_id)
            figma_api_key = next(
                (
                    setting.value
                    for setting in settings.integrations.figma
                    if setting.name == "figma_api_key"
                ),
                "",
            )
            if figma_api_key:
                background_tasks.add_task(
                    background_process_figma_file,
                    project_id,
                    figma_link,
                    tenant_id,
                    figma_api_key,
                    False,
                )
        except:
            pass  # Continue even if background task setup fails

        return JSONResponse(
            status_code=202,
            content={
                "message": "Figma file update started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )


@router.delete("/delete_design/{project_id}/{figma_id}")
async def delete_design(
    project_id: str, figma_id: str, current_user=Depends(get_current_user)
):
    """
    Delete a design including its S3 data and MongoDB records.

    Args:
        project_id (str): The project ID
        figma_id (str): The Figma design ID

    Returns:
        JSONResponse: Result of the deletion operation
    """
    deletion_results = {
        "s3_deleted": False,
        "mongodb_deleted": False,
        "project_ref_removed": False,
    }

    try:
        # First verify the design exists
        existing_design = await FigmaModel.get_one(figma_id)
        if not existing_design:
            return JSONResponse(
                status_code=404,
                content={
                    "message": "Design not found",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

        # Check if design belongs to project
        if existing_design.get("project_id") != project_id:
            return JSONResponse(
                status_code=400,
                content={
                    "message": "Design does not belong to this project",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

        # 1. Delete S3 data
        try:
            tenant_id = get_tenant_id()
            file_name = f"{figma_id}.json"
            s3_handler = S3Handler(tenant_id)
            if s3_handler.is_file(file_name):
                s3_handler.delete_file(file_name)
                deletion_results["s3_deleted"] = True
        except Exception as e:
            print(f"Error deleting S3 data: {str(e)}")

        # 2. Delete from figma_designs collection
        try:
            deleted = await FigmaModel.delete(figma_id)
            deletion_results["mongodb_deleted"] = bool(deleted)
        except Exception as e:
            print(f"Error deleting MongoDB data: {str(e)}")

        # 3. Remove reference from project
        try:
            removed = await ProjectModel.remove_design_id(project_id, figma_id)
            deletion_results["project_ref_removed"] = bool(removed)
        except Exception as e:
            print(f"Error removing project reference: {str(e)}")

        # Determine success based on critical operations
        if (
            deletion_results["mongodb_deleted"]
            and deletion_results["project_ref_removed"]
        ):
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Design deleted successfully",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )
        else:
            # If some operations failed but others succeeded
            return JSONResponse(
                status_code=207,  # Multi-Status
                content={
                    "message": "Design deletion partially completed",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "message": f"An error occurred during deletion: {str(e)}",
                "id": figma_id,
                "results": deletion_results,
            },
        )


@router.post("/reload_design/{project_id}/{figma_id}")
async def reload_design(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_id: str,
    current_user=Depends(get_current_user),
):
    """Reload a Figma design by re-processing all frames"""
    try:
        # Verify design exists
        design = await FigmaModel.get_one(figma_id)
        if not design:
            return JSONResponse(
                status_code=404, content={"message": "Design not found"}
            )

        if design.get("project_id") != project_id:
            return JSONResponse(
                status_code=400,
                content={"message": "Design does not belong to this project"},
            )

        # Reset design status and counters
        update_data = {
            "status": ProcessingStatus.PENDING,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "error_message": None,
            "time_updated": generate_timestamp(),
        }

        updated = await FigmaModel.update(figma_id, update_data)
        if not updated:
            return JSONResponse(
                status_code=500, content={"message": "Failed to update design status"}
            )

        # Get Figma API key
        tenant_id = design.get("tenant_id")
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )

        # Prepare figma link model
        figma_link = FigmaRequestModel(
            name=design.get("name", ""), url=design.get("url", "")
        )

        # Add the background task
        # This will be executed after the response is sent
        background_tasks.add_task(
            background_process_figma_file,
            project_id,
            figma_link,
            tenant_id,
            figma_api_key,
            False,
        )

        # Return success response - background task will continue running
        return JSONResponse(
            status_code=202,
            content={
                "message": "Design reload started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        # Update the design status to failed if we encounter an error
        try:
            await FigmaModel.update(
                figma_id,
                {
                    "status": ProcessingStatus.FAILED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                },
            )
        except:
            pass  # Ignore errors in error handling

        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )


@router.get("/stream-status/{project_id}/{figma_id}")
async def stream_status(
    project_id: str,
    figma_id: str,
    stream: bool = Query(
        False,
        description="Stream updates until completion if True, else return current status",
    ),
    current_user=Depends(get_current_user),
):
    async def get_current_status():
        try:
            design = await FigmaModel.get_one(figma_id)
            if not design:
                return {"error": "Design not found", "status": ProcessingStatus.FAILED}

            return {
                "id": figma_id,
                "status": design.get("status", ProcessingStatus.PENDING),
                "total_frames": design.get("total_frames", 0),
                "completed_frames": design.get("completed_frames", 0),
                "failed_frames": design.get("failed_frames", 0),
                "error_message": design.get("error_message"),
                "time_updated": design.get("time_updated", generate_timestamp()),
            }
        except Exception as e:
            return {"error": str(e), "status": ProcessingStatus.FAILED}

    async def generate_status_events():
        prev_status = None
        retry_count = 0
        max_retries = 60  # 30 seconds with 0.5s sleep

        while retry_count < max_retries:
            try:
                status_data = await get_current_status()

                current_status = (
                    status_data.get("status"),
                    status_data.get("completed_frames"),
                    status_data.get("failed_frames"),
                )

                if current_status != prev_status:
                    yield f"data: {json.dumps(status_data)}\n\n"
                    prev_status = current_status

                if status_data.get("error") or status_data.get("status") in [
                    ProcessingStatus.COMPLETED,
                    ProcessingStatus.FAILED,
                ]:
                    break

                await asyncio.sleep(0.5)
                retry_count += 1

            except Exception as e:
                yield f"data: {json.dumps({'error': str(e), 'status': ProcessingStatus.FAILED})}\n\n"
                break

    if stream:
        return StreamingResponse(
            generate_status_events(), media_type="text/event-stream"
        )
    else:
        status_data = await get_current_status()
        return JSONResponse(content=status_data)


@router.post("/add_figma_file")
async def add_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        file_data, sizes = await process_figma_file(
            project_id, figma_link, is_new_file=True
        )

        # Add the design to the project
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )
        figma_model = FigmaModel(
            id=project_id,
            name=figma_link.name,
            url=figma_link.url,
            added_by=user_model,
            sizes=FigmaSizesModel(**sizes),
        )

        # Get existing project if it exists
        existing_project = await ProjectModel.get_one(project_id)

        if existing_project:
            designs = existing_project.get("fields", {}).get("designs", [])
            designs.append(figma_model.model_dump())
            project_data = {"_id": project_id, "fields": {"designs": designs}}
        else:
            project_data = {
                "_id": project_id,
                "fields": {"designs": [figma_model.model_dump()]},
            }

        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)

        return JSONResponse(content={"message": "Figma file added successfully"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete_figma_file")
async def delete_figma_file(
    project_id: str,
    figma_design: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        tenant_id = get_tenant_id()
        figma_link = figma_design.url
        # Extract file key and generate filename
        file_key = extract_file_key(figma_link)
        file_name = f"{tenant_id}-{project_id}-{file_key}.json"

        # Delete file from S3
        s3_handler = S3Handler(tenant_id)
        if s3_handler.is_file(file_name):
            s3_handler.delete_file(file_name)

        # Get existing project
        existing_project = await ProjectModel.get_one(project_id)
        if not existing_project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Remove design from project's designs list
        designs = existing_project.get("fields", {}).get("designs", [])
        designs = [d for d in designs if d.get("url") != figma_link]

        # Update project with modified designs list
        project_data = {"_id": project_id, "fields": {"designs": designs}}
        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)

        return JSONResponse(content={"message": "Figma file deleted successfully"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/update_figma_file")
async def update_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        # Verify project exists
        existing_project = await ProjectModel.get_one(project_id)
        if not existing_project:
            raise HTTPException(status_code=404, detail="Project not found")

        file_data, sizes = await process_figma_file(
            project_id, figma_link, is_new_file=False
        )
        # Get existing designs
        designs = existing_project.get("fields", {}).get("designs", [])

        # Update the sizes for the matching design
        for design in designs:
            if design.get("url") == figma_link.url:
                figma_model = FigmaModel(
                    id=design.get("id"),
                    name=design.get("name"),
                    url=design.get("url"),
                    added_by=design.get("added_by"),
                    sizes=FigmaSizesModel(**sizes),
                    time_created=design.get("time_created"),
                    time_updated=generate_timestamp(),
                )
                design.update(figma_model.dict())
                break

        # Update project with modified designs
        project_data = {"_id": project_id, "fields": {"designs": designs}}
        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)
        return JSONResponse(
            content={
                "message": "Figma file updated successfully",
                "file_data": file_data,
                "sizes": sizes,
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_figma_files")
async def get_figma_files(
    project_id: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        print(f"Project ID: {project_id}")
        designs, images  = await FigmaModel.get_by_project(project_id, include_images=True)
        return JSONResponse(content={"designs": designs, "images": images})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_figma_file_data")
async def get_figma_file_data(figma_id: str, current_user=Depends(get_current_user)):
    try:
        tenant_id = get_tenant_id()
        s3_handler = S3Handler(tenant_id)
        file_name = f"{figma_id}.json"
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))
        return JSONResponse(content=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/file")
async def get_figma_file(
    project_id: str,
    figma_link: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link)
        file_name = f"{tenant_id}-{project_id}-{file_key}.json"
        s3_handler = S3Handler(tenant_id)
        if not file_key:
            raise HTTPException(status_code=400, detail="Invalid Figma link")

        if not s3_handler.is_file(file_name):
            raise HTTPException(status_code=404, detail="File not found")

        # data = get_figma_file_data_limited(figma_link, get_figma_access_token(), kb_limit=500)
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))

        return JSONResponse(
            content={
                "frames": data["frames"],
                "fileKey": file_key,
                "document": data["document"],
            }
        )
        # return JSONResponse(content={"frames": frames_with_images, "fileKey": file_key, "document": data["document"]})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/frame-image")
async def get_frame_image(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    image_url = fetch_frame_image(file_key, frame_id)
    if not image_url:
        raise HTTPException(status_code=404, detail="Frame image not found")
    return JSONResponse(content={"imageUrl": image_url})


@router.get("/download")
async def download_all_frames(figma_link: str, current_user=Depends(get_current_user)):
    file_key = extract_file_key(figma_link)
    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    print(file_key)
    zip_buffer = io.BytesIO()

    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()

    data = response.json()
    frames = extract_frame_data(data)

    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for frame in frames:
            json_content = json.dumps(frame, indent=2)
            zip_file.writestr(f"{frame['name']}/{frame['name']}.json", json_content)

            image_url = fetch_frame_image(file_key, frame["id"])
            if image_url:
                image_response = requests.get(image_url, timeout=300)
                image_response.raise_for_status()
                zip_file.writestr(
                    f"{frame['name']}/{frame['name']}.png", image_response.content
                )

    zip_buffer.seek(0)
    return StreamingResponse(
        iter([zip_buffer.getvalue()]),
        media_type="application/zip",
        headers={
            "Content-Disposition": f"attachment; filename=figma_export_all_frames.zip"
        },
    )


@router.get("/frame-details")
async def get_frame_details_route(
    file_key: str, frame_id: str, current_user=Depends(get_current_user)
):
    frame_details = get_frame_details(file_key, frame_id)
    return JSONResponse(content=frame_details)


@router.get("/frame-preview")
async def get_frame_preview(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 2, "format": "png"}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    preview_url = response.json()["images"].get(frame_id)

    if not preview_url:
        raise HTTPException(status_code=404, detail="Frame preview not found")
    return JSONResponse(content={"previewUrl": preview_url})


@router.get("/frame-thumbnail")
async def get_frame_thumbnail(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 0.5, "format": "png"}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    thumbnail_url = response.json()["images"].get(frame_id)

    if not thumbnail_url:
        raise HTTPException(status_code=404, detail="Frame thumbnail not found")
    return JSONResponse(content={"thumbnailUrl": thumbnail_url})


# Update the link-frames endpoint to include image URLs
@router.post("/link-frames")
async def link_frames_to_design(
    frame_link: FrameLink,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    file_key = extract_file_key(frame_link.figma_link)
    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    design_node = await db.get_node_by_label_id(frame_link.design_id, "Design")
    if not design_node:
        raise HTTPException(status_code=404, detail="Design node not found")

    if not frame_link.frame_ids:
        updated_design = await db.update_node_by_id(
            frame_link.design_id, {"LinkedFigmaFrames": "[]"}
        )
        return JSONResponse(
            content={
                "message": "Frames linked successfully",
                "updated_design": updated_design,
            }
        )

    url = f"https://api.figma.com/v1/files/{file_key}/nodes"
    params = {"ids": ",".join(frame_link.frame_ids)}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()

    frames_data = response.json()["nodes"]

    # Fetch image URLs for the frames
    image_urls = fetch_frame_images(file_key, frame_link.frame_ids)

    linked_frames = design_node.get("properties", {}).get("LinkedFigmaFrames", [])
    if linked_frames:
        linked_frames = json.loads(linked_frames)
    if isinstance(linked_frames, str):
        linked_frames = json.loads(linked_frames)

    # Create a set of existing frame IDs for quick lookup
    existing_frame_ids = set(frame["id"] for frame in linked_frames)

    for frame_id, frame_data in frames_data.items():
        # Only add the frame if it doesn't already exist
        if frame_id not in existing_frame_ids:
            linked_frames.append(
                {
                    "id": frame_id,
                    "name": frame_data["document"]["name"],
                    "file_key": file_key,
                    "imageUrl": image_urls.get(frame_id),
                    "thumbnailUrl": f"https://api.figma.com/v1/images/{file_key}?ids={frame_id}&scale=0.5&format=png",
                }
            )
            existing_frame_ids.add(frame_id)  # Add the new frame ID to the set

    updated_design = await db.update_node_by_id(
        frame_link.design_id, {"LinkedFigmaFrames": json.dumps(linked_frames)}
    )

    return JSONResponse(
        content={
            "message": "Frames linked successfully",
            "updated_design": updated_design,
        }
    )


@router.post("/link-figma-components")
async def link_figma_components(
    frame_link: FrameLink,
    design_id: str,
    current_user=Depends(get_current_user),
):
    try:
        db = get_node_db()
        design_id = int(design_id)
        if not design_id:
            raise HTTPException(status_code=400, detail="Design ID is required")

        if not frame_link.figma_link:
            raise HTTPException(status_code=400, detail="Figma link is required")

        file_key = extract_file_key(frame_link.figma_link)
        if not file_key:
            raise HTTPException(status_code=400, detail="Invalid Figma link format")

        existing_design = await db.get_node_by_label_id(design_id, "Design")
        if not existing_design:
            raise HTTPException(status_code=404, detail="Design node not found")

        # Get existing components
        existing_components = []
        if existing_design.get("properties", {}).get("FigmaComponents"):
            try:
                existing_components = json.loads(
                    existing_design["properties"]["FigmaComponents"]
                )
                if not isinstance(existing_components, list):
                    existing_components = []
            except json.JSONDecodeError:
                existing_components = []

        if frame_link.unlink:
            # Remove the component if it exists
            existing_components = [
                comp for comp in existing_components if comp.get("file_key") != file_key
            ]
            message = "Figma component unlinked successfully"
        else:
            # Check if component already exists
            component_exists = False
            for component in existing_components:
                if component.get("file_key") == file_key:
                    component.update(
                        {
                            "figma_link": frame_link.figma_link,
                            "updated_at": datetime.utcnow().isoformat(),
                            "name": frame_link.name,  # Update name if changed
                        }
                    )
                    component_exists = True
                    break

            # If component doesn't exist, add it
            if not component_exists:
                new_component = {
                    "file_key": file_key,
                    "figma_link": frame_link.figma_link,
                    "name": frame_link.name,  # Store the name
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by": {
                        "username": current_user.get("sub"),
                        "name": current_user.get("custom:Name"),
                        "email": current_user.get("email"),
                    },
                }
                existing_components.append(new_component)
            message = "Figma component linked successfully"

        # Update the design node
        design_node = await db.update_node_by_id(
            design_id, {"FigmaComponents": json.dumps(existing_components)}
        )

        if not design_node:
            raise HTTPException(
                status_code=500, detail="Failed to update design node with components"
            )

        return JSONResponse(
            content={
                "message": message,
                "updated_design": design_node,
                "components": existing_components,
            }
        )

    except HTTPException as e:
        raise e
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400, detail="Invalid JSON format in existing components"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


@router.post("/check_design_in_db/{tenant_id}/{project_id}")
async def check_design_in_db(tenant_id: str, project_id: str, figmaDesigns: List[Dict]):
    try:
        deleted_info = []
        mongo_handler = get_mongo_db(
            db_name=(settings.MONGO_DB_NAME), collection_name="figma_designs"
        )
        for design in figmaDesigns:
            foundDesign = await mongo_handler.get_one(
                filter={"file_key": design["file_key"], "project_id": project_id},
                db=mongo_handler.db,
            )
            if foundDesign:
                deleted = False
            else:
                deleted = True
            deleted_info.append({design["file_key"]: deleted})
        return {"data": deleted_info}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )

class StartExtractionRequest(BaseModel):
    selected_design_id:str
    session_name:str = Field(default="Untitled")

@router.post("/start_discussion")
async def start_discussion(
    project_id: int,
    request: StartExtractionRequest,
    extraction_type: ExtractionTypes = ExtractionTypes.Figma,
    current_user=Depends(get_current_user),
):
    try:
        selected_design_id = request.selected_design_id
        session_name=request.session_name

        discussion_id = Extraction.start_discussion(project_id=project_id, 
                                                    selected_design_id=selected_design_id,
                                                    session_name=session_name,
                                                    extraction_type=extraction_type)
        
        return JSONResponse(content={"discussion_id": discussion_id})
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )

@router.post("/figma_extraction")
async def figma_extraction(
    request: FigmaExtractionRequest,
    current_user=Depends(get_current_user)
):
    discussion_id = request.discussion_id
    user_request = request.user_request
    user_message = user_request.user_message
    file_attachments = user_request.file_attachments
    selected_frame = request.selected_frame
    # Use discussion_id as the task_id
    task_id = discussion_id
    
    discussion = Extraction(discussion_id)
    if file_attachments:
        discussion.set_file_attachments(file_attachments)
    discussion.set_current_user(current_user=current_user.get("cognito:username"))
    if selected_frame:
        discussion.set_selected_frame(selected_frame)
    await discussion._initialize()
    await discussion.load_figma_data()
    end_response = {"stop": True}
    
    # Create WebSocket session
    ws_client = create_websocket_session(task_id, metadata={
        'task_type': 'figma_extraction',
        'discussion_id': discussion_id,
        'user_id': current_user.get("cognito:username")
    })
    
    # Send initial connection message
    ws_client.send_message("connected", {
        "task_id": task_id,
        "status": "processing"
    })
    
    # This function will process the discussion and send via WebSocket
    async def process_discussion():
        try:
            async for llm_response in discussion.main_discussion(user_message, stream=True,ws_client=ws_client):
                # Send to WebSocket
                ws_client.send_message("data", llm_response)
        except Exception as e:
            import traceback
            traceback.print_exc()
            error_msg = f"Error: {str(e)}"
            print(error_msg)
            # Send error to WebSocket
            ws_client.send_message("error", {"message": error_msg})
        finally:
            # Send end response to WebSocket
            ws_client.send_message("end", {"end":True})
            print(f"DEBUG: End Messsage : {end_response}")
            # Clean up WebSocket session
            # Delay cleanup by 5 seconds
            await asyncio.sleep(5)
            cleanup_websocket_session(task_id)
    
    # Start the processing in a background task
    asyncio.create_task(process_discussion())
    
    # Return the task_id for client to connect to WebSocket
    return {
        "task_id": task_id,
        "status": "processing in background"
    }
@router.get("/register_agent/{session_id}")
def register_agent(session_id: str):
    try:
        # Initialize WebSocket client and reporter
        ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)
        reporter.initialize()
        
        return {
            "status": "success",
            "message": f"Agent successfully registered with session ID: {session_id}",
            "session_id": session_id
        }
        
    except ConnectionError as e:
        return {
            "status": "error",
            "message": f"Failed to establish WebSocket connection: {str(e)}",
            "session_id": session_id
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to register agent: {str(e)}",
            "session_id": session_id
        }

@router.get("/messages_history")
async def messages_history(discussion_id: str, current_user=Depends(get_current_user)):
    messages = Extraction.get_messages_history(discussion_id)
    return JSONResponse(content={"messages": messages})


@router.get("/past_discussions/{project_id}")
async def past_discussions(
    project_id: int,
    selected_design_id: str = None,
    current_user=Depends(get_current_user),
):
    print(f"Project ID: {project_id}")
    discussions = Extraction.get_past_discussions(project_id, selected_design_id)
    return JSONResponse(content={"discussions": discussions})


@router.get("/files/{discussion_id}")
async def files_content(discussion_id: str):
    """
    Returns a list of files generated for a specific Figma extraction discussion.

    Args:
        discussion_id (str): The unique identifier for the discussion

    Returns:
        JSONResponse: List of files with their paths and content
    """
    try:
        # Create the base directory path for this discussion
        base_dir = f"{BASE_PATH}/{discussion_id}"

        # Check if directory exists
        if not os.path.exists(base_dir):
            return JSONResponse(
                status_code=404,
                content={"message": f"No files found for discussion {discussion_id}"},
            )

        # Walk through the directory to find all files ignore logs folder and .log files
        file_list = []
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".log") or file.startswith("logs"):
                    continue
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, base_dir)

                # Get file stats
                stats = os.stat(file_path)

                # Read file content if it's not too large
                content = ""
                if stats.st_size < 1024 * 1024:  # Limit to files < 1MB
                    try:
                        with open(file_path, "r") as f:
                            content = f.read()
                    except UnicodeDecodeError:
                        # Handle binary files
                        content = "(Binary file content not shown)"
                else:
                    content = f"(File too large: {stats.st_size / 1024:.2f} KB)"
                # if its a design_file.html, then make it index.html
                # if file == "design_file.html":
                #     file = "index.html"
                file_list.append(
                    {
                        "name": file,
                        "path": relative_path,
                        "full_path": file_path,
                        "size": stats.st_size,
                        "modified": stats.st_mtime,
                        "content": content,
                    }
                )

        # Sort files by name for consistency
        file_list.sort(key=lambda x: x["path"])

        return JSONResponse(
            status_code=200,
            content={
                "discussion_id": discussion_id,
                "base_path": base_dir,
                "files": file_list,
                "file_count": len(file_list),
            },
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"message": f"Error retrieving files: {str(e)}"}
        )


IMAGE_TEMPLATE = {
    "figma_ext_id" : "",
    "added_by" : {
        "username" : "",
        "name" : "",
        "email" : ""
    },
    "images" : [],
    "completed_frames" : 0,
    "failed_frames" : 0,
    "file_key" : "",
    "name" : "",
    "project_id" : "",
    "status" : "",
    "tenant_id" : "",
    "time_created" : "",
    "time_updated" : "",
    "total_frames" : 0,
    "path" : "",
    "error_message" : ""
}


@router.get("/get_ext_images/{project_id}/{image_ext_id}")
async def get_ext_images(project_id: str, image_ext_id: str ):
    image = get_mongo_db().db["figma_ext_images"].find_one({"project_id": project_id, "figma_ext_id": image_ext_id})
    if image:
        image.pop("_id")
        return JSONResponse(content={"image": image})
    else:
        return JSONResponse(content={"image": None})
    
class MergeChangesRequest(BaseModel):
    project_id: str
    discussion_id: str
    figma_ext_id: str
    type_of : str
@router.post("/merge_changes")
async def merge_changes(
    request: MergeChangesRequest,
    current_user=Depends(get_current_user),
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Stores the file_path to the appropriate collection when a merge is performed.
    Also updates the project node in node_db with assets information.

    Args:
        request (MergeChangesRequest): The merge request data
        current_user: The authenticated user
        node_db: Database for node operations

    Returns:
        JSONResponse: Status of the merge operation
    """
    try:
        mongo_db = get_mongo_db().db
        if request.type_of == "image":
            collection = "figma_ext_images"
        elif request.type_of == "figma":
            collection = "figma_designs"
        else:
            return JSONResponse(
                status_code=400, 
                content={"message": "Type should be of image or figma"}
            )

        file_path = f"{BASE_PATH}/{request.discussion_id}/.assets"
        
        # Construct a query that uses the correct ID field based on type
        query = {"project_id": request.project_id}
        if request.type_of == "image":
            query["figma_ext_id"] = request.figma_ext_id
        else:  # figma type
            query["id"] = request.figma_ext_id
        
        # Find the existing record
        record = mongo_db[collection].find_one(query)
        
        if not record:
            return JSONResponse(
                status_code=404,
                content={"message": f"No record found for project {request.project_id} and ID {request.figma_ext_id}"}
            )
        
        # Create merged_by user info
        merged_by_info = {
            "username": current_user["sub"],
            "name": current_user["custom:Name"],
            "email": current_user["email"]
        }
        
        # Update data with the new merge information
        current_time = generate_timestamp()
        
        # If there are already merged files, add this one to the list
        if "merged_files" in record:
            # Add new file to the list of merged files
            update_data = {
                "time_updated": current_time,
                "$push": {
                    "merged_files": {
                        "extraction_path": file_path,
                        "file_path": file_path,
                        "discussion_id": request.discussion_id,
                        "merged_by": merged_by_info,
                        "merged_at": current_time
                    }
                }
            }
        else:
            # Create the initial merged_files array
            update_data = {
                "time_updated": current_time,
                "merged_files": [{
                    "extraction_path": file_path,
                    "file_path": file_path,
                    "discussion_id": request.discussion_id,
                    "merged_by": merged_by_info,
                    "merged_at": current_time
                }]
            }
        
        # Update the MongoDB record
        if "merged_files" in record:
            # Use update with $push operator when adding to existing array
            mongo_db[collection].update_one(
                query,
                {"$set": {"time_updated": current_time}, 
                 "$push": update_data["$push"]}
            )
        else:
            # Use regular update when creating the initial array
            mongo_db[collection].update_one(
                query,
                {"$set": update_data}
            )
        
        # Update project node in node_db with assets information
        import json
        
        # Try several ways to get the project node
        project_id = int(request.project_id)  # Ensure it's an integer
        
        # First try without specifying node type (more permissive)
        project_node = await node_db.get_node_by_id(project_id)
        
        # If that fails, try with a direct query that doesn't check is_active
        if not project_node:
            query = """
            MATCH (n) 
            WHERE ID(n) = $node_id 
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            query_result = await node_db.async_run(query, node_id=project_id)
            result = query_result.data()
            if result and len(result) > 0:
                project_node = result[0]
            else:
                return JSONResponse(
                    status_code=404,
                    content={"message": f"No project node found for project {request.project_id}"}
                )
        
        # Get current assets or initialize empty dict
        assets = {}
        if "assets" in project_node["properties"] and project_node["properties"]["assets"]:
            try:
                assets = json.loads(project_node["properties"]["assets"])
            except json.JSONDecodeError:
                # If assets data is not valid JSON, start with empty dict
                assets = {}
        
        # Update assets with the new design information
        design_id = request.figma_ext_id
        assets[design_id] = {"extracted_assets_path": file_path}
        
        # Update the project node with the new assets information
        project_properties = project_node["properties"].copy()
        project_properties["assets"] = json.dumps(assets)
        
        # Update the node in the database
        update_result = await node_db.update_node_by_id(project_id, project_properties)
        
        # If update fails, log it but don't fail the whole request
        if not update_result:
            print(f"Warning: Failed to update assets in project node {project_id}")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"File {file_path} merged successfully",
                "project_id": request.project_id,
                "discussion_id": request.discussion_id,
                "figma_ext_id": request.figma_ext_id,
                "file_path": file_path,
                "extraction_path": file_path,
            }
        )
        
    except Exception as e:
        traceback.print_exc()
        print(f"Error in merge_changes: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"message": f"An error occurred while merging changes: {str(e)}"}
        )
#################################################################################
@router.post("/create_ext_images")
async def add_ext_images(
    project_id: str,
    group_name: str,
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user),
):
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        id = str(uuid.uuid4())[:8]    
        #create a fresh copy with deepcopy of IMAGE_TEMPLATE    
        tmp_image = deepcopy(IMAGE_TEMPLATE)
        
        tmp_image["added_by"]["username"] = current_user["sub"]
        tmp_image["added_by"]["name"] = current_user["custom:Name"]
        tmp_image["added_by"]["email"] = current_user["email"]
        tmp_image["project_id"] = project_id
        tmp_image["tenant_id"] = get_tenant_id()
        tmp_image["time_created"] = generate_timestamp()
        tmp_image["time_updated"] = generate_timestamp()
        
        tmp_image["figma_ext_id"] = id
        tmp_image["name"] = group_name
        tmp_image["status"] = "pending"
        tmp_image["total_frames"] = len(files)
        tmp_image["completed_frames"] = 0
        tmp_image["failed_frames"] = 0
        tenant_id = get_tenant_id()

        results = []
        path_extends = f"{BASE_PATH}/{tenant_id}/{project_id}/{id}/"
        os.makedirs(path_extends, exist_ok=True)
        tmp_image["images"] = []
        for file in files:
            try:
                # Save the uploaded file to the mongodb in base64url format
                file_content = await file.read()
                # Compress the image to 50%
                compressed_content, content_type, compression_ratio = compress_image(
                    file_content, target_ratio=0.5
                )
                
                base64_content = base64.b64encode(compressed_content).decode('utf-8')
                base64url = f"data:{file.content_type};base64,{base64_content}"
                image = ImageTemplate(
                    filename=file.filename, 
                    size=file.size,
                    file_type=file.content_type,
                    base64url=base64url,
                    error_message=""
                )
                tmp_image["images"].append(image.model_dump())

                tmp_image["completed_frames"] += 1
                tmp_image["time_updated"] = generate_timestamp()

                results.append({
                    "filename": file.filename,
                    "status": "success",
                    "base64url": base64url,
                    "compression_ratio": compression_ratio,
                    "original_size": len(file_content),
                    "compressed_size": len(compressed_content)
                })

            except Exception as e:
                tmp_image["failed_frames"] += 1
                tmp_image["time_updated"] = generate_timestamp()
                tmp_image["error_message"] = str(e)

                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })
                
        tmp_image["path"] = path_extends
        tmp_image["status"] = "completed" if tmp_image["failed_frames"] == 0 else "failed"
        tmp_image["time_updated"] = generate_timestamp()

        
        mongo_db[collection].update_one(
            {"figma_ext_id": id},
            {"$set": tmp_image},
            upsert=True
        )

        return JSONResponse(
            status_code=200, 
            content={
                "message": f"Processed {len(files)} files", 
                "results": results
            }
        )

    except Exception as e:
        #traceback
        traceback.print_exc()
        print(f"Error in add_ext_images: {str(e)}")  # Fixed the debug print message
        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )
    
    

    
@router.post("/generate_assets")
async def generate_assets(
    project_id: int,
    selected_design_id: str,
    discussion_id: str,
    current_user=Depends(get_current_user)
):
    try:
        pass
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred: {str(e)}"
        )
    
@router.delete("/delete_ext_images/{figma_ext_id}")
async def delete_ext_images(
    figma_ext_id: str,
    current_user=Depends(get_current_user),
):
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # First, fetch the document to get necessary info
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission to delete
        # Option 1: Only creator can delete
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to delete this resource"}
            )
        
        # Delete associated files from the filesystem
        # path_extends = image_doc["path"]
        # if os.path.exists(path_extends):
        #     try:
        #         shutil.rmtree(path_extends)
        #     except Exception as e:
        #         print(f"Warning: Could not delete directory {path_extends}: {str(e)}")
        
        # Delete from MongoDB
        delete_result = mongo_db[collection].delete_one({"figma_ext_id": figma_ext_id})
        
        if delete_result.deleted_count == 1:
            return JSONResponse(
                status_code=200,
                content={"message": f"Successfully deleted image group with ID {figma_ext_id}"}
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"message": f"Failed to delete image group with ID {figma_ext_id}"}
            )
            
    except Exception as e:
        traceback.print_exc()
        print(f"Error in delete_ext_images: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"message": f"An error occurred: {str(e)}"}
        )
    
@router.post("/add_more_images/{project_id}/{figma_ext_id}")
async def add_more_images(
    project_id: str,
    figma_ext_id: str,
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user),
):
    """Add more images to an existing image group
    
    Args:
        project_id (str): Project identifier
        figma_ext_id (str): Existing image group identifier
        files (List[UploadFile]): List of files to add
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({
            "figma_ext_id": figma_ext_id,
            "project_id": project_id
        })
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        results = []
        for file in files:
            try:
                # Convert file to base64url format
                file_content = await file.read()
                base64_content = base64.b64encode(file_content).decode('utf-8')
                base64url = f"data:{file.content_type};base64,{base64_content}"
                
                # Create image template
                image = ImageTemplate(
                    filename=file.filename,
                    size=file.size,
                    file_type=file.content_type,
                    base64url=base64url,
                    error_message=""
                )
                
                # Add to MongoDB array
                mongo_db[collection].update_one(
                    {"figma_ext_id": figma_ext_id},
                    {
                        "$push": {"images": image.model_dump()},
                        "$inc": {
                            "total_frames": 1,
                            "completed_frames": 1
                        },
                        "$set": {
                            "time_updated": generate_timestamp(),
                            "status": "completed"
                        }
                    }
                )

                results.append({
                    "filename": file.filename,
                    "status": "success",
                    "base64url": base64url
                })

            except Exception as e:
                # Update error count and status
                mongo_db[collection].update_one(
                    {"figma_ext_id": figma_ext_id},
                    {
                        "$inc": {
                            "total_frames": 1,
                            "failed_frames": 1
                        },
                        "$set": {
                            "time_updated": generate_timestamp(),
                            "status": "failed",
                            "error_message": str(e)
                        }
                    }
                )
                
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })

        # Get updated document
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if updated_doc:
            updated_doc.pop("_id")  # Remove MongoDB _id before sending response
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Added {len(files)} files to image group {figma_ext_id}",
                "results": results,
                "updated_document": updated_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in add_more_images: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )
@router.put("/rename_ext_image/{figma_ext_id}/{file_id}")
async def rename_ext_image(
    figma_ext_id: str,
    file_id: str,
    new_filename: str,
    current_user=Depends(get_current_user),
):
    """Rename a specific image in an image group
    
    Args:
        figma_ext_id (str): Image group identifier
        file_id (str): Image file identifier
        new_filename (str): New name for the file
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        # Find and update the specific image
        updated = mongo_db[collection].update_one(
            {
                "figma_ext_id": figma_ext_id,
                "images.file_id": file_id
            },
            {
                "$set": {
                    "images.$.filename": new_filename,
                    "time_updated": generate_timestamp()
                }
            }
        )
        
        if updated.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with filename {file_id} not found in group"}
            )
            
        # Get updated document
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if updated_doc:
            updated_doc.pop("_id")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully renamed image {file_id} to {new_filename}",
                "updated_document": updated_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in rename_ext_image: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )

@router.delete("/delete_ext_image/{figma_ext_id}/{file_id}")
async def delete_ext_image(
    figma_ext_id: str,
    file_id: str,
    current_user=Depends(get_current_user),
):
    """Delete a specific image from an image group
    
    Args:
        figma_ext_id (str): Image group identifier
        file_id (str): Image file identifier
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        # Remove the specific image and update counters
        updated = mongo_db[collection].update_one(
            {"figma_ext_id": figma_ext_id},
            {
                "$pull": {"images": {"file_id": file_id}},
                "$inc": {"total_frames": -1},
                "$set": {"time_updated": generate_timestamp()}
            }
        )
        
        if updated.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with filename {file_id} not found in group"}
            )
        
        # Recalculate completed_frames and failed_frames
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        completed_frames = sum(1 for img in updated_doc["images"] if not img.get("error_message"))
        failed_frames = sum(1 for img in updated_doc["images"] if img.get("error_message"))
        
        # Update the counts
        mongo_db[collection].update_one(
            {"figma_ext_id": figma_ext_id},
            {
                "$set": {
                    "completed_frames": completed_frames,
                    "failed_frames": failed_frames,
                    "status": "completed" if failed_frames == 0 else "failed"
                }
            }
        )
        
        # Get final updated document
        final_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if final_doc:
            final_doc.pop("_id")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully deleted image {file_id}",
                "updated_document": final_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in delete_ext_image: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )    
@router.get("/download_figma_code/{discussion_id}")
async def download_code(discussion_id: str):
    """
    Download all Code for a specific figma extraction as a zip file.
    Provides a browser download experience similar to downloading images from Google.
    
    Args:
        discussion_id: discussion ID of the figma extraction
        
    Returns:
        Streaming response with zip file
    """
    try:
        
        code_dir = f"{FIGMA_BASE_PATH}/{discussion_id}/.assets"
        
        # Check if directory exists
        if not os.path.exists(code_dir):
            raise HTTPException(status_code=404, detail=f"No codes file found for {discussion_id}")
        
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"figma_extraction_{discussion_id}_{timestamp}.zip"
        
        # Create zip file in memory
        zip_io = io.BytesIO()
        with zipfile.ZipFile(zip_io, mode='w', compression=zipfile.ZIP_DEFLATED) as zipf:
            # Walk through the directory and add all files
            for root, _, files in os.walk(code_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Add file to zip with relative path
                    arcname = os.path.relpath(file_path, code_dir)
                    zipf.write(file_path, arcname)
        
        # Reset the pointer to the beginning of the BytesIO object
        zip_io.seek(0)
        
        # Return streaming response with appropriate headers for download
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        return StreamingResponse(
            zip_io, 
            media_type="application/zip",
            headers=headers
        )
        
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        import traceback
        print(f"Error creating Code's zip file: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to create Code's File archive: {str(e)}")


async def background_process_figma_file_CGA(
    project_id: str,
    figma_link: str,
    task_id: str,
    tenant_id: str,
    figma_api_key: str,
    is_new_file: bool = True,
):
    """Background task to process Figma file with frame status tracking"""
    print("Background task started")
    file_logger = get_logger(__name__)
    completed_count = 0
    failed_count = 0
    total_frames = 0
    data = None

    # Setup local storage path
    base_path = "/app/data"
    if os.environ.get("LOCAL_DEBUG"):
        base_path = "/tmp"
    
    attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json"
    
    # Create directory if it doesn't exist
    os.makedirs(attachment_path, exist_ok=True)
    ws_client = WebSocketClient(f"{task_id}", uri=settings.WEBSOCKET_URI)
    ws_client.connect()

    try:
        figma_access_token.set(figma_api_key)
        file_key = extract_file_key(figma_link)
        figma_id = f"{tenant_id}-{project_id}-{task_id}-{file_key}"

        # Use async client for HTTP requests
        async with httpx.AsyncClient() as client:
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link, figma_api_key, mb_limit=100
            )
            frames = extract_all_node_data(data)
            total_frames = len(frames)

            # Build frame hierarchy map to find children relationships
            frame_children_map = build_frame_children_map(data)

            # Update FigmaModel with initial frame count and status
            update_data = {
                "total_frames": total_frames,
                "completed_frames": 0,
                "failed_frames": 0,
                "completed_frame_ids": [],  # Initialize empty list for tracking completed frames
                "failed_frame_ids": [],     # Initialize empty list for tracking failed frames
                "sizes": FigmaSizesModel(**sizes).dict(),
                "time_updated": generate_timestamp(),
            }
            await FigmaModel.update_CGA(figma_id, update_data)

            # Process frames and store each individually
            frame_ids = [frame["id"] for frame in frames]
            image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
            has_errors = False
            completed_frame_ids = []  # Track completed frame IDs locally
            failed_frame_ids = []     # Track failed frame IDs locally

            # Determine initial status based on file size
            status = (
                ProcessingStatus.FAILED 
                if FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000 
                else ProcessingStatus.PROCESSING
            )

            # Process each frame
            for i, frame in enumerate(frames):
                try:
                    frame_model = await process_frame(frame, file_key, image_urls)
                    frame_data = create_frame_data(frame, frame_model, frame_children_map)
                    
                    # Store individual frame locally
                    frame_file_path = os.path.join(attachment_path, f"figma_{frame['id']}.json")
                    await write_json_file(frame_file_path, frame_data)

                    # Update counts
                    if frame_model.status == ProcessingStatus.COMPLETED:
                        completed_count += 1
                    elif frame_model.status == ProcessingStatus.FAILED:
                        failed_count += 1
                        has_errors = True

                    # Send WebSocket update every 10 frames or at the end
                    if (i + 1) % 10 == 0 or i == len(frames) - 1:
                        update_data = {
                            "total_frames": total_frames,
                            "completed_frames": completed_count,
                            "failed_frames": failed_count,
                            "time_updated": generate_timestamp(),
                            "sizes": FigmaSizesModel(**sizes).dict(),
                            "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
                            "progress": round(((i + 1) / total_frames) * 100, 2),
                            "current_frame": i + 1,
                            "message": f"Processed {i + 1}/{total_frames} frames" + (f" ({failed_count} failed)" if failed_count > 0 else "")
                        }
                        
                        # Send WebSocket update
                        ws_client.send_message(
                            "figma_update",
                            {"figma_id": figma_id, "update_data": update_data}
                        )
                except Exception as e:
                    print(f"Error processing frame {frame['id']}: {str(e)}")
                    failed_count += 1
                    has_errors = True

                    # Store error frame data
                    error_frame_data = create_error_frame_data(frame, frame_children_map, str(e))
                    
                    try:
                        frame_file_path = os.path.join(attachment_path, f"figma_{frame['id']}.json")
                        await write_json_file(frame_file_path, error_frame_data)
                    except Exception as file_error:
                        print(f"Error storing failed frame to file: {str(file_error)}")
                        if (i + 1) % 10 == 0 or i == len(frames) - 1:
                            update_data = {
                                "total_frames": total_frames,
                                "completed_frames": completed_count,
                                "failed_frames": failed_count,
                                "time_updated": generate_timestamp(),
                                "sizes": FigmaSizesModel(**sizes).dict(),
                                "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
                                "progress": round(((i + 1) / total_frames) * 100, 2),
                                "current_frame": i + 1,
                                "message": f"Processed {i + 1}/{total_frames} frames ({failed_count} failed)"
                            }
                            
                            # Send WebSocket update
                            ws_client.send_message(
                                "figma_update",
                                {"figma_id": figma_id, "update_data": update_data}
                            )

            # Store metadata file
            metadata = create_metadata(file_key, sizes, total_frames, completed_count, failed_count)
            metadata_file_path = os.path.join(attachment_path, "metadata.json")
            await write_json_file(metadata_file_path, metadata)

        # Final status update
        final_status = (
            ProcessingStatus.COMPLETED
            if failed_count == 0
            else ProcessingStatus.PARTIALLY_COMPLETED
        )
        update_data = {
                        "total_frames": total_frames,
                        "completed_frames": completed_count,
                        "failed_frames": failed_count,
                        "time_updated": generate_timestamp(),
                        "sizes": FigmaSizesModel(**sizes).dict(),
                        "status": ProcessingStatus.PARTIALLY_COMPLETED if has_errors else status,
                    }
                    
        await FigmaModel.update_CGA(figma_id, update_data)
        
        final_update = {
            "status": final_status,
            "error_message": (
                f"{failed_count} frames failed to process" if failed_count > 0 else None
            ),
            "time_updated": generate_timestamp(),
        }
        
        await FigmaModel.update_CGA(figma_id, final_update)
        
        # Success message
        success_msg = f"Figma file processing completed successfully! Processed {completed_count}/{total_frames} frames."
        if failed_count > 0:
            success_msg += f" {failed_count} frames failed to process."
        
        print(success_msg)
        return {"status": "success", "message": success_msg}

    except Exception as e:
        print(f"Error in background task: {str(e)}")
        
        # Store error metadata if possible
        if 'figma_id' in locals():
            try:
                error_metadata = create_error_metadata(
                    locals().get('file_key'),
                    locals().get('sizes', {}),
                    total_frames,
                    completed_count,
                    failed_count,
                    str(e)
                )
                
                metadata_file_path = os.path.join(attachment_path, "metadata.json")
                await write_json_file(metadata_file_path, error_metadata)
            except Exception as file_error:
                print(f"Error storing error metadata to file: {str(file_error)}")

            # Update status based on progress
            error_status = (
                ProcessingStatus.PARTIALLY_COMPLETED 
                if completed_count > 0 
                else ProcessingStatus.FAILED
            )
            
            error_update = {
                "status": error_status,
                "error_message": str(e),
                "time_updated": generate_timestamp(),
            }
            
            await FigmaModel.update_CGA(figma_id, error_update)
        
        return {"status": "error", "message": f"Processing failed: {str(e)}"}


def build_frame_children_map(data: Dict[str, Any]) -> Dict[str, list]:
    """Build a map of all node children relationships"""
    frame_children_map = {}
    
    def build_children_map(node: Dict[str, Any], parent_id: str = None):
        current_node_id = node["id"]
        frame_children_map[current_node_id] = []
        
        if "children" in node:
            for child in node["children"]:
                frame_children_map[current_node_id].append(child["id"])
                build_children_map(child, current_node_id)
    
    if data.get("document"):
        build_children_map(data["document"])
    
    return frame_children_map


def create_frame_data(frame: Dict[str, Any], frame_model, frame_children_map: Dict[str, list]) -> Dict[str, Any]:
    """Create comprehensive frame data with all available details"""
    bounding_box = frame.get("absoluteBoundingBox", {})
    if not bounding_box:
        bounding_box = {"x": 0, "y": 0, "width": 800, "height": 600}

    children_frame_ids = frame_children_map.get(frame["id"], [])

    frame_data = {
        "id": frame["id"],
        "name": frame["name"],
        "type": frame["type"],
        "absoluteBoundingBox": bounding_box,
        "children": children_frame_ids,
        "imageUrl": (
            frame_model.imageUrl
            if frame_model.status == ProcessingStatus.COMPLETED
            else None
        ),
        "status": frame_model.status,
        "error_message": (
            frame_model.error_message
            if hasattr(frame_model, "error_message")
            else None
        ),
        "time_updated": frame_model.time_updated,
        "dimensions": {
            "width": round(bounding_box.get("width", 800)),
            "height": round(bounding_box.get("height", 600)),
        },
        # Include all other available frame details
        **{k: v for k, v in {
            "visible": frame.get("visible", True),
            "locked": frame.get("locked", False),
            "backgroundColor": frame.get("backgroundColor", ""),
            "exportSettings": frame.get("exportSettings", []),
            "blendMode": frame.get("blendMode", ""),
            "preserveRatio": frame.get("preserveRatio", ""),
            "layoutAlign": frame.get("layoutAlign", ""),
            "layoutGrow": frame.get("layoutGrow", ""),
            "layoutSizingHorizontal": frame.get("layoutSizingHorizontal", ""),
            "layoutSizingVertical": frame.get("layoutSizingVertical", ""),
            "effects": frame.get("effects", []),
            "opacity": frame.get("opacity", ""),
            "relativeTransform": frame.get("relativeTransform", ""),
            "size": frame.get("size", ""),
            "constraintsHorizontal": frame.get("constraintsHorizontal", ""),
            "constraintsVertical": frame.get("constraintsVertical", ""),
            "fills": frame.get("fills", []),
            "strokes": frame.get("strokes", []),
            "strokeWeight": frame.get("strokeWeight", ""),
            "strokeAlign": frame.get("strokeAlign", ""),
            "strokeCap": frame.get("strokeCap", ""),
            "strokeJoin": frame.get("strokeJoin", ""),
            "strokeDashes": frame.get("strokeDashes", ""),
            "strokeMiterLimit": frame.get("strokeMiterLimit", ""),
            "cornerRadius": frame.get("cornerRadius", ""),
            "rectangleCornerRadii": frame.get("rectangleCornerRadii", ""),
            "clipsContent": frame.get("clipsContent", ""),
            "background": frame.get("background", ""),
            "layoutMode": frame.get("layoutMode", ""),
            "itemSpacing": frame.get("itemSpacing", ""),
            "paddingLeft": frame.get("paddingLeft", ""),
            "paddingTop": frame.get("paddingTop", ""),
            "paddingRight": frame.get("paddingRight", ""),
            "paddingBottom": frame.get("paddingBottom", ""),
            "horizontalPadding": frame.get("horizontalPadding", ""),
            "verticalPadding": frame.get("verticalPadding", ""),
            "itemReverseZIndex": frame.get("itemReverseZIndex", ""),
            "strokesIncludedInLayout": frame.get("strokesIncludedInLayout", ""),
            "primaryAxisAlignItems": frame.get("primaryAxisAlignItems", ""),
            "counterAxisAlignItems": frame.get("counterAxisAlignItems", ""),
            "primaryAxisSizingMode": frame.get("primaryAxisSizingMode", ""),
            "counterAxisSizingMode": frame.get("counterAxisSizingMode", ""),
            "layoutWrap": frame.get("layoutWrap", ""),
            "layoutGrids": frame.get("layoutGrids", []),
            "overflowDirection": frame.get("overflowDirection", ""),
            "numberOfFixedChildren": frame.get("numberOfFixedChildren", ""),
            "overlayPositionType": frame.get("overlayPositionType", ""),
            "overlayBackground": frame.get("overlayBackground", ""),
            "overlayBackgroundInteraction": frame.get("overlayBackgroundInteraction", ""),
            "prototypeStartNodeID": frame.get("prototypeStartNodeID", ""),
            "prototypeDevice": frame.get("prototypeDevice", ""),
            "flowStartingPoints": frame.get("flowStartingPoints", []),
            "reactions": frame.get("reactions", []),
            "transitionNodeID": frame.get("transitionNodeID", ""),
            "transitionDuration": frame.get("transitionDuration", ""),
            "transitionEasing": frame.get("transitionEasing", ""),
            "rotation": frame.get("rotation", ""),
            "componentPropertyReferences": frame.get("componentPropertyReferences", ""),
            "boundVariables": frame.get("boundVariables", ""),
            "componentPropertyDefinitions": frame.get("componentPropertyDefinitions", ""),
            "scrollBehavior": frame.get("scrollBehavior", ""),
            "guides": frame.get("guides", []),
            "selection": frame.get("selection", ""),
            "prototypeBackgroundColor": frame.get("prototypeBackgroundColor", ""),
        }.items() if v not in [None, "", []]}
    }

    return {k: v for k, v in frame_data.items() if v is not None}


def create_error_frame_data(frame: Dict[str, Any], frame_children_map: Dict[str, list], error_msg: str) -> Dict[str, Any]:
    """Create error frame data with available details"""
    children_frame_ids = frame_children_map.get(frame["id"], [])
    bounding_box = frame.get("absoluteBoundingBox", {})
    
    error_frame_data = {
        "id": frame["id"],
        "name": frame.get("name", "Unknown"),
        "type": frame.get("type", "FRAME"),
        "children": children_frame_ids,
        "status": ProcessingStatus.FAILED,
        "error_message": error_msg,
        "time_updated": generate_timestamp(),
        "absoluteBoundingBox": bounding_box,
        "visible": frame.get("visible", True),
        "locked": frame.get("locked", False),
        "backgroundColor": frame.get("backgroundColor", ""),
        "dimensions": {
            "width": round(bounding_box.get("width", 800)),
            "height": round(bounding_box.get("height", 600)),
        },
    }
    
    return {k: v for k, v in error_frame_data.items() if v is not None}


def create_metadata(file_key: str, sizes: Dict, total_frames: int, completed_count: int, failed_count: int) -> Dict[str, Any]:
    """Create metadata dictionary"""
    return {
        "fileKey": file_key,
        "sizes": sizes,
        "progress": {
            "total": total_frames,
            "completed": completed_count,
            "failed": failed_count,
        },
        "time_updated": generate_timestamp(),
    }


def create_error_metadata(file_key: str, sizes: Dict, total_frames: int, completed_count: int, failed_count: int, error_msg: str) -> Dict[str, Any]:
    """Create error metadata dictionary"""
    return {
        "fileKey": file_key,
        "sizes": sizes,
        "progress": {
            "total": total_frames,
            "completed": completed_count,
            "failed": failed_count,
        },
        "error": error_msg,
        "time_updated": generate_timestamp(),
    }


async def write_json_file(file_path: str, data: Dict[str, Any]) -> None:
    """Write JSON data to file asynchronously"""
    import aiofiles
    
    async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
        await f.write(json.dumps(data, indent=2, ensure_ascii=False))


@router.post("/add_figma_file_CGA")
async def add_figma_file_CGA(
    background_tasks: BackgroundTasks,
    project_id: str,
    task_id: str,
    figma_link: str,
    current_user=Depends(get_current_user),
):
    """Add Figma file for processing with individual frame storage"""
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link)

        if not file_key:
            return JSONResponse(
                status_code=400, content={"message": "Invalid Figma link"}
            )

        # Create unique figma_id
        figma_id = f"{tenant_id}-{project_id}-{task_id}-{file_key}"

        # Check if design already exists
        existing_design = await FigmaModel.get_one_CGA(figma_id)
        if existing_design:
            return JSONResponse(
                status_code=400, content={"message": "Design already exists"}
            )

        # Get Figma API key from settings
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )
        

        if not figma_api_key:
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )

        # Get file data and sizes
        async with httpx.AsyncClient() as client:
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link, figma_api_key, mb_limit=100
            )

        # Determine initial status based on file size
        status = (
            ProcessingStatus.PENDING 
            if sizes["size_kb"] < 6000 
            else ProcessingStatus.FAILED
        )

        # Create user model
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )

        # Prepare figma data
        figma_data = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "task_id": task_id,
            "file_key": file_key,
            "id": figma_id,
            "url": figma_link,
            "added_by": user_model.dict(),
            "status": status,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "time_created": generate_timestamp(),
            "time_updated": generate_timestamp(),
            "sizes": FigmaSizesModel(**sizes).dict(),
        }

        # Add to project first
        success = await ProjectModel.add_design_id(project_id, figma_id)
        if not success:
            return JSONResponse(
                status_code=500, content={"message": "Failed to add design to project"}
            )

        # Create design document
        created_design = await FigmaModel.create_CGA(figma_data)
        if not created_design:
            # Cleanup project if design creation fails
            await ProjectModel.remove_design_id(project_id, figma_id)
            return JSONResponse(
                status_code=500, content={"message": "Failed to create design"}
            )

        # Start background processing if file size is acceptable
        if figma_api_key and status != ProcessingStatus.FAILED:
            try:
                background_tasks.add_task(
                    background_process_figma_file_CGA,
                    project_id,
                    figma_link,
                    task_id,
                    tenant_id,
                    figma_api_key,
                    True,
                )
            except Exception as e:
                print(f"Error setting up background task: {str(e)}")

        return JSONResponse(
            status_code=202,
            content={
                "message": "Figma file processing started",
                "status": status,
                "id": figma_id,
            },
        )

    except Exception as e:
        status_code = classify_exception_status_code(e)
        print(f"Error in add_figma_file_CGA: {str(e)}")
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )
    
import glob

async def get_figma_frame_data(figma_id: str):
    try:
        tenant_id = get_tenant_id()
        s3_handler = S3Handler(tenant_id)
        file_name = f"{figma_id}.json"
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))
        frames = data.get('frames', [])
        result = []
        for frame in frames:
            frame_id = frame.get('id')
            frame_name = frame.get('name')
            image_url = frame.get('imageUrl')
            if frame_id and image_url:
                result.append({'frame_id': frame_id, 'image_url': image_url, "frame_name": frame_name})
        return result
    except Exception as e:
        return {'error': str(e)}


@router.get("/list_figma_files_CGA")
async def list_figma_files(
    background_tasks: BackgroundTasks,
    project_id: str,
    file_key: str,
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        base_path = "/app/data"
        if os.environ.get("LOCAL_DEBUG"):
            base_path = "/tmp"
        figma_id = f"{tenant_id}-{project_id}-{file_key}"
        attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"

        # Get frame data
        frame_data_list = await get_figma_frame_data(figma_id=figma_id)
        
        # Handle error case
        if isinstance(frame_data_list, dict) and 'error' in frame_data_list:
            return JSONResponse(
                status_code=500,
                content={"message": f"Error getting frame data: {frame_data_list['error']}"}
            )
        
        # Build lookups for frame_id to image_url and frame_name, and get valid frame_ids
        frame_id_to_image_url = {item['frame_id']: item['image_url'] for item in frame_data_list}
        frame_id_to_frame_name = {item['frame_id']: item['frame_name'] for item in frame_data_list}
        valid_frame_ids = set(frame_id_to_image_url.keys())

        # List JSON files
        json_pattern = os.path.join(attachment_path, "*.json")
        json_files = glob.glob(json_pattern)

        files_with_info = []
        for file_path in json_files:
            filename = os.path.basename(file_path)
            if filename == "metadata.json":
                continue
            
            # Extract frame_id from filename: e.g., figma_{frame_id}.json
            if filename.startswith("figma_") and filename.endswith(".json"):
                frame_id = filename[len("figma_"):-len(".json")]
                
                # Only include files with frame_ids that exist in the frame data
                if frame_id in valid_frame_ids:
                    image_url = frame_id_to_image_url.get(frame_id)
                    frame_name = frame_id_to_frame_name.get(frame_id)
                    files_with_info.append({
                        'filename': filename,
                        'path': file_path,
                        'relative_path': os.path.relpath(file_path, base_path),
                        'frame_id': frame_id,
                        'image_url': image_url,
                        'frame_name': frame_name
                    })

        files_with_info.sort(key=lambda x: x['filename'])
        # try:
        #     background_tasks.add_task(
        #             copy_figma_json_to_attachments,
        #             tenant_id,
        #             project_id,
        #             figma_id
        #         )
        # except Exception as e:
        #     print(f"Error setting up background task: {str(e)}")
        return JSONResponse(
            status_code=200,
            content={
                'files': files_with_info,
                'total_files': len(files_with_info),
                'directory_path': attachment_path,
                'valid_frame_ids': list(valid_frame_ids),  # Optional: for debugging
                'total_valid_frames': len(valid_frame_ids)  # Optional: for debugging
            }
        )
    except Exception as e:
        status_code = classify_exception_status_code(e)
        return JSONResponse(
            status_code=status_code,
            content={"message": f"An error occurred: {str(e)}"}
        )

from app.utils.code_generation_utils import get_codegeneration_path
import shutil

async def copy_figma_json_to_attachments(tenant_id, project_id, figma_id):
    """Copy Figma JSON file from figma_json to attachments directory"""
    base_path = "/efs"
    if os.environ.get("LOCAL_DEBUG"):
        base_path = "/tmp"
    source_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"
    code_gen_path = get_codegeneration_path()
    dest_path = f"{code_gen_path}/attachments"
    try:
        if os.path.exists(source_path):
            if not os.path.exists(dest_path):
                os.makedirs(dest_path)
            for file_name in os.listdir(source_path):
                if file_name.endswith(".json"):
                    source_file = os.path.join(source_path, file_name)
                    dest_file = os.path.join(dest_path, file_name)
                    shutil.copy2(source_file, dest_file)
            return True
        else:
            return False
    except Exception as e:
        print(f"Error while copying Figma JSON: {str(e)}")
        return False
from fastapi.responses import FileResponse
@router.get("/download_logs_figma_add/{tenant_id}/{project_id}/{figma_id}")
async def download_logs_figma_add(tenant_id: str, project_id: int, figma_id: str):
    """
    Download the single log file for a specific task.
    Provides a browser download experience for one .log file.
    
    Args:
        tenant_id: Tenant identifier
        project_id: Project ID
        figma_id: figma_id
        
    Returns:
        FileResponse with log file
    """
    try:
        print(f"Tenant_id: {tenant_id}, project_id: {project_id}, figma_id: {figma_id}")

        if os.getenv("LOCAL_DEBUG"):
            log_file = f"/tmp/{tenant_id}/{project_id}/logs/{figma_id}.log"
        else:
            log_file = f"/app/data/{tenant_id}/{project_id}/{figma_id}.log"
        
        # Check if file exists
        if not os.path.exists(log_file):
            raise HTTPException(status_code=404, detail=f"No log file found for task {figma_id}")

        # Create a filename for download
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs_task_{figma_id}_{timestamp}.log"

        return FileResponse(
            path=log_file,
            filename=filename,
            media_type="application/octet-stream",
            headers={'Content-Disposition': f'attachment; filename="{filename}"'}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error serving log file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to serve log file: {str(e)}")