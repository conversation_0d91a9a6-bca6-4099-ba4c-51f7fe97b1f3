from fastapi.responses import StreamingResponse , JSONResponse, Response
from bson.json_util import dumps, loads
from app.agents.supervisor import Supervisor
from fastapi import BackgroundTasks, File, UploadFile
from app.connection.establish_db_connection import get_node_db, connect_mongo_db, NodeDB, get_mongo_db
from app.utils.auth_utils import get_current_user
from app.core.Settings import Settings
from openai import OpenAIError, OpenAI
from fastapi.middleware.cors import CORSMiddleware
from app.models.notification_model import (
    NotificationModel, DiscussionNotificationData , ApprovalNotificationData
)

from fastapi import APIRouter, Depends, Body , Query, Request, responses, HTTPException, status , UploadFile , File
from app.utils.node_utils import get_node_type
from typing import List
from app.models.user_model import AddRemoveUserDTO 
from app.models.discussion_model import ExecuteStepRequest, RepeatStepRequest, CommentDTO, ModificationStatus, ModificationFeedbackDTO, StartProjectInit
from datetime import datetime
import uuid
import asyncio
import pandas as pd
import io

import json
import logging
from pydantic import BaseModel
from app.utils.auth_utils import get_current_user
from app.utils.user_utils import get_initials_from_name, send_notification 
import pkgutil
import importlib
import traceback
from app.utils.node_version_manager import NodeVersionManager
from app.utils.datetime_utils import generate_timestamp
from app.discussions.discussion_factory import DiscussionFactory, Discussion
from app.telemetry.logger_config import get_logger,setup_logging
import datetime
import asyncio

setup_logging()


from app.discussions.discussion import Discussion as mainDis
from pydantic import BaseModel, Field
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from app.models.user_model import LLMModel
from litellm import get_supported_openai_params
from litellm import supports_response_schema
from app.core.constants import NodeType, NodeLabel
from app.utils.datetime_utils import generate_timestamp

logger = get_logger(__name__)

# Discover all classes in the 'app.discussions.types' package
# Import the package correctly. Assuming 'discussions.types' is within a parent package 'discussions'.
package = importlib.import_module("app.discussions.types")

for loader, module_name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + '.'):
    # Ensure the imported modules are correctly referenced with their full package names
    importlib.import_module(module_name)
# Discover and register all classes in the 'app.discussions.types' package


def find_step_configuration(steps, step_name):
    for step in steps:
        if step['name'] == step_name:
            return step
    return None

tasks = {}

_SHOW_NAME = "discussion"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

@router.post('/start/{node_type}/')
async def start_discussion(
    node_type: str,
    node_id: int,
    discussion_type = None,
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    # node_id = json_data.get('node_id')
    if not node_id:
        raise HTTPException(status_code=400, detail='Node id not provided')
    
    discussion_type = discussion_type or 'configuration'

    logger.info(
        "Starting new discussion",
        extra={
            "user_id": current_user.get('cognito:username'),
            "user_email": current_user.get('email'),
            "node_type": node_type,
            "node_id": node_id,
            "discussion_type": discussion_type
        }
    )

    configuration = await DiscussionFactory.create_discussion(
        discussion_type=discussion_type,
        node_type=get_node_type(node_type),
        node_id=node_id,
        current_user=current_user.get('cognito:username'),
        title=f'{node_type} Discussion on {generate_timestamp()}' #FIXME: Add title to request
        
    )
    
    # await configuration.async_initialize()
    if not configuration:
        raise HTTPException(status_code=400, detail='Invalid node type')
    discussion_id = configuration.discussion_id
    await db.add_user_to_discussion(current_user.get('cognito:username'), discussion_id)
    steps = configuration.get_visible_steps()
    # await configuration.execute_to_step('retrieve_info')
    logger.info(
        "Discussion started successfully",
        extra={
            "discussion_id": discussion_id,
            "user_id": current_user.get('cognito:username'),
            "node_type": node_type,
            "node_id": node_id
        }
    )
    return {'success': 'discussion started', 'discussion_type': discussion_type, 'discussion_id': discussion_id, 'steps': steps}

@router.post('/execute/')
async def execute_to_step(
    request: ExecuteStepRequest,
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    logger.info(
        "Executing step in discussion",
        extra={
            "user_id": current_user.get('cognito:username'),
            "user_email": current_user.get('email'),
            "node_type": request.node_type,
            "node_id": request.node_id,
            "discussion_id": request.discussion_id,
            "step_name": request.step_name
        }
    )

    discussion_node = await db.get_node_by_id(request.discussion_id)
    discussion_type = request.discussion_type or discussion_node.get('properties', {}).get('discussion_type') or 'configuration'
    
    logger.info(
        "Discussion type determined",
        extra={
            "discussion_type": discussion_type,
            "discussion_id": request.discussion_id
        }
    )

    configuration = await DiscussionFactory.create_discussion(
        discussion_type=discussion_type,
        node_type=get_node_type(request.node_type),
        node_id=request.node_id,
        discussion_node_id=request.discussion_id,
        invocation_type="interactive_config",
        current_user=current_user.get('cognito:username')
    )

    if not configuration:
        raise HTTPException(status_code=400, detail='Invalid node type')
    
    steps = configuration.get_visible_steps()
    step_configuration = find_step_configuration(steps, request.step_name)
    if not step_configuration:
        raise HTTPException(status_code=400, detail='Invalid step name')

    if step_configuration['response']['is_streaming']:
        return StreamingResponse(
            await configuration.execute_to_step(request.step_name, streaming=True),
            media_type='text/event-stream'
        )
    else:
        return await configuration.execute_to_step(request.step_name)

@router.post('/repeat/')
async def repeat_step(
    request: RepeatStepRequest,
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    logger.info(
        "Repeating step in discussion",
        extra={
            "user_id": current_user.get('cognito:username'),
            "user_email": current_user.get('email'),
            "node_type": request.node_type,
            "node_id": request.node_id,
            "discussion_id": request.discussion_id,
            "step_name": request.step_name,
            "modification_index": request.modification_index
        }
    )
    
    discussion_node = await db.get_node_by_id(request.discussion_id)
    discussion_type = request.discussion_type or discussion_node.get('properties', {}).get('discussion_type') or 'configuration'

    configuration: Discussion = await DiscussionFactory.create_discussion(
        discussion_type=discussion_type,
        node_type=get_node_type(request.node_type),
        node_id=request.node_id,
        discussion_node_id=request.discussion_id,
        invocation_type="interactive_config",
        current_user=current_user.get('cognito:username')
    )

    if not configuration:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail='Invalid node type')
    
    steps = configuration.get_visible_steps()
    step_configuration = find_step_configuration(steps, request.step_name)
    if not step_configuration:
        raise HTTPException(status_code=400, detail='Invalid step name')

    if request.modification_index is not None and configuration.modifications_history:
        configuration.modifications = configuration.modifications_history[request.modification_index]

    # Construct the new user comment with file information
    
    new_user_comment = f"{request.usercomment}"
    if request.file_attachments:
        configuration.set_file_attachments( request.file_attachments )

    if step_configuration['response']['is_streaming']:
        return StreamingResponse(
            await configuration.repeat_step(request.step_name, new_user_comment, streaming=True),
            media_type='text/event-stream'
        )
    else:
        return await configuration.repeat_step(request.step_name, new_user_comment if request.usercomment else None, streaming=False)
    
@router.get('/{node_id}/')
async def get_discussions(
    node_id: int,
    status: str = None,
    db: NodeDB = Depends(get_node_db)
):
    try:
        discussions = await db.get_discussions(node_id, status)
        return discussions
    except Exception as e:
        logger.error(
            "Error getting discussions",
            extra={
                "node_id": node_id,
                "status": status,
                "error": str(e)
            }
        )
        raise

@router.get('/{discussion_id}/get_users')
async def get_participants(
    discussion_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        participants = await db.get_participants_in_discussion(discussion_id)
        return participants
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get('/{discussion_id}/get_users_to_add')
async def get_participants_to_add(
    discussion_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        participants = await db.get_participants_in_discussion_to_add(discussion_id)
        return participants
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/{discussion_id}/get_discussion')
async def get_discussion(
    discussion_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        discussion = await db.get_discussion(discussion_id)
        parent_node_id = discussion.get('parent_node_id')
        node_type = discussion.get('parent_node_type')
        discussion_type = discussion.get('properties', {}).get('discussion_type')
        discussion_type = discussion_type or 'configuration'
        if parent_node_id and node_type:
            configuration = await DiscussionFactory.create_discussion(
                discussion_type=discussion_type,
                node_type=get_node_type(node_type),
                node_id=parent_node_id,
                discussion_node_id=discussion_id,
            )
            steps = configuration.get_visible_steps()
            discussion['steps'] = steps
            discussion['node_id'] = parent_node_id
            discussion['node_type'] = node_type
            return discussion
        else:
            JSONResponse(content={"message": "Discussion not found"}, status_code=404)
    except Exception as e:
        logger.error(
            "Error retrieving discussion",
            extra={
                "discussion_id": discussion_id,
                "error": str(e),
                "error_trace": traceback.format_exc()
            }
        )
        raise HTTPException(status_code=500, detail=str(e))
    

@router.post("/{discussion_id}/add_users", response_description="Add multiple participants to the discussion")
async def add_participants_to_discussion(
    discussion_id: int,
    users: List[AddRemoveUserDTO] = Body(...),  # Accept a list of user IDs
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    try:
        root_node = await db.get_root_node(discussion_id)
        if not root_node:
            raise HTTPException(status_code=404, detail="Discussion not found")
        for user in users:
            user = user.model_dump()
            user_id = user.get('user_id')
            await db.add_user_to_discussion(user.get('user_id'), discussion_id)
            
            project_title = root_node.get('properties', {}).get('Title', root_node.get('properties', {}).get('Name'))
            notification = NotificationModel(
                receiver_id=user_id,
                type="discussion",
                action="add",
                data=DiscussionNotificationData(
                    message=f"You've been added to a discussion by {current_user.get('email','admin')} on {project_title}.",
                    assigner_id=current_user.get('cognito:username','admin'),
                    discussion_id=discussion_id,
                    project_id=root_node.get("id"),  # Assuming project_id is available
                    link = f"/project/{root_node.get('id')}"
                )
            )

            await send_notification(notification)
        
        return JSONResponse(content={"message": "Users added to discussion successfully"}, status_code=200)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{discussion_id}/modifications")
async def get_modifications(
    discussion_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        discussion = await db.get_discussion(discussion_id)
        if not discussion:
            raise HTTPException(status_code=404, detail="Discussion not found")
        
        discussion = discussion.get('properties')
        modifications_history = discussion.get('modifications_history')
        if not modifications_history:
           return {"message": "No modifications found",
                    "modifications": []}
        
        modifications_history = json.loads(modifications_history)
        return JSONResponse({"modifications": modifications_history }, status_code=200)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/{discussion_id}/merge_request")
async def merge_request(
    discussion_id: int,
    modification_index: int,
    type: str = Query("request", description="Type of merge request"),
    approver_id: str = Query(None, description="User ID of the approver"),
    current_user=Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: NodeDB = Depends(get_node_db)
):
   try:
        discussion_node = await db.get_discussion(discussion_id)
        root_data = await db.get_root_node(discussion_id)
        if not discussion_node:
            raise HTTPException(status_code=404, detail="Discussion not found")
        
        discussion = discussion_node.get('properties')
        if modification_index is not None and discussion.get('modifications_history'):
            modifications_history = discussion.get('modifications_history')
            if modifications_history:
                modifications_history = json.loads(modifications_history)
            else:
                return JSONResponse(content={"message": "No modifications found"}, status_code=404)
            modification_feedback = ModificationFeedbackDTO(
                status=ModificationStatus.IDLE,
                modified_by=current_user.get('cognito:username'),
                approver_id=None,
            ).model_dump()
        

            if type == "request":
                modification_feedback["status"] = ModificationStatus.PENDING
                modification_feedback["approver_id"] = approver_id
                notification = NotificationModel(
                    receiver_id=approver_id,
                    type="discussion",
                    action="merge",
                    data=ApprovalNotificationData(
                        message=f"Merge request for a discussion by {current_user.get('email','admin')}",
                        project_id=root_data.get("id"),
                        initiator_id=current_user.get('cognito:username','admin'),
                        approver_id=approver_id,
                        discussion_id=discussion_id,
                        link=f"/project/{root_data.get('id')}"
                    )
                )
                await send_notification(notification)
                    
            elif type == "approve":
                repeat_request = RepeatStepRequest(
                    node_id=discussion_node.get('parent_node_id'),
                    node_type=discussion_node.get('parent_node_type'),
                    step_name="merge_captured_items",
                    discussion_id=discussion_id,
                    modification_index=modification_index,
                    usercomment=None,
                    file_attachments=None  # Add file attachments if needed
                )
                await repeat_step(repeat_request, current_user=current_user)
                modification_feedback["status"] = ModificationStatus.MERGED
            elif type == "reject":
                modification_feedback["status"] = ModificationStatus.REJECTED
            else:
                return JSONResponse(content={"message": "Invalid merge request type"}, status_code=400)
        modifications_history[modification_index]["modification_feedback"] = modification_feedback
        await db.update_property_by_id(discussion_id, "modifications_history", json.dumps(modifications_history))
        return JSONResponse(content={"message": "Merge request updated successfully"}, status_code=200)
   except Exception as e:
        logger.error(
            "Error processing merge request",
            extra={
                "discussion_id": discussion_id,
                "modification_index": modification_index,
                "type": type,
                "user_id": current_user.get('cognito:username'),
                "error": str(e),
                "error_trace": traceback.format_exc()
            }
        )
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{discussion_id}/comments/")
async def add_or_edit_comment_to_discussion(
    discussion_id: int,
    modification_index: int,
    comment: str = Body(...),
    comment_index: int = None,
    user_id: str = None,
    current_user=Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: NodeDB = Depends(get_node_db)
):
    try:
        if not user_id:
            user_id = current_user.get('cognito:username')
            
        discussion_node = await db.get_discussion(discussion_id)
    
        if not discussion_node:
            raise HTTPException(status_code=404, detail="Discussion not found")
        
        discussion = discussion_node.get('properties')
        if modification_index is not None and discussion.get('modifications_history'):
            modifications_history = discussion.get('modifications_history')
            if modifications_history:
                modifications_history = json.loads(modifications_history)
            else:
                return JSONResponse(content={"message": "No modifications found"}, status_code=404)
            
            if "comments" not in modifications_history[modification_index]:
                modifications_history[modification_index]["comments"] = []
            
            comments = modifications_history[modification_index]["comments"]
            
            if comment_index is not None:
                # Editing an existing comment
                if comment_index < 0 or comment_index >= len(comments):
                    return JSONResponse(content={"message": "Invalid comment index"}, status_code=400)
                
                if comments[comment_index]["user_id"] != user_id:
                    return JSONResponse(content={"message": "You are not authorized to edit this comment"}, status_code=403)
                
                comments[comment_index]["comment"] = comment
                comments[comment_index]["edited"] = True
                comments[comment_index]["edited_at"] = generate_timestamp()
                message = "Comment edited successfully"
            else:
                new_comment = CommentDTO(
                    comment=comment, 
                    user_id=user_id, 
                    created_at=generate_timestamp(),
                    edited=False
                ).model_dump()
                comments.append(new_comment)
                message = "Comment added successfully"
                participants = await db.get_participants_in_discussion(discussion_id)
                for participant in participants:
                    if participant.get("username") != user_id:
                        notification = NotificationModel(
                            receiver_id=participant.get("username"),
                            type="discussion",
                            action="comment",
                            data=DiscussionNotificationData(
                                message=f"A new comment has been added to a discussion by {current_user.get('email','admin')}",
                                assigner_id=current_user.get('cognito:username','admin'),
                                discussion_id=discussion_id,
                                project_id=discussion_node.get("parent_node_id"),  # Assuming project_id is available
                                link = f"/project/{discussion_node.get('parent_node_id')}"
                            )
                        )
                        background_tasks.add_task(send_notification, notification)
            
            modifications_history[modification_index]["comments"] = comments
            await db.update_property_by_id(discussion_id, "modifications_history", json.dumps(modifications_history))
        
        return JSONResponse({ "message": message }, status_code=200)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
      
    
@router.get('/{discussion_id}/users')
async def get_participants(
    discussion_id: int,
    participant_type: str  = Query("all", description="Type of participants to retrieve"),
    db: NodeDB = Depends(get_node_db)
):
    try:
        if participant_type == "existing":
            participants = await db.get_participants_in_discussion(discussion_id)
        elif participant_type == "available":
            participants = await db.get_participants_in_discussion_to_add(discussion_id)
        else:  # ALL
            current_participants = await db.get_participants_in_discussion(discussion_id)
            participants_to_add = await db.get_participants_in_discussion_to_add(discussion_id)
            
            participants_to_add = [participant for participant in participants_to_add if participant not in current_participants]
            participants = {
                "current_participants": current_participants,
                "participants_to_add": participants_to_add
            }
        
        return participants
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
 
@router.get("/{discussion_id}/comments")
async def get_comments_for_discussion(
    discussion_id: int,
    modification_index: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        discussion = await db.get_discussion(discussion_id)
        if not discussion:
            raise HTTPException(status_code=404, detail="Discussion not found")
        
        discussion = discussion.get('properties')
        if modification_index is not None and discussion.get('modifications_history'):
            modifications_history = discussion.get('modifications_history')
            if not modifications_history:
                return JSONResponse(content={"message": "No modifications found"}, status_code=404)
            
            modifications_history = json.loads(modifications_history)
            comments = modifications_history[modification_index].get("comments", [])
            
            cached_users = {}
            processed_comments = []
            
            for comment in comments:
                user_id = comment.get("user_id")
                if user_id not in cached_users:
                    user = await db.get_user_by_id(user_id)
                    user_info = {
                        "name": user.get("properties").get("Name", "Unknown"),
                        "email": user.get("properties").get("Email", "Unknown"),
                        "initials": get_initials_from_name(user.get("properties").get("Name", "Unknown"))
                    }
                    cached_users[user_id] = user_info
                
                comment_with_user = comment.copy()
                comment_with_user = {**comment_with_user,
                                     **cached_users[user_id]}
                processed_comments.append(comment_with_user)
            
            return JSONResponse({"comments": processed_comments }, status_code=200)
        
        return JSONResponse(content={"message": "No modifications found"}, status_code=404)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    
@router.delete("/{discussion_id}/comments")
async def delete_comment_for_discussion(
    discussion_id: int,
    modification_index: int,
    comment_index: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        discussion = await db.get_discussion(discussion_id)
        if not discussion:
            raise HTTPException(status_code=404, detail="Discussion not found")
        
        discussion = discussion.get('properties')
        if modification_index is not None and discussion.get('modifications_history'):
            modifications_history = discussion.get('modifications_history')
            if not modifications_history:
                return JSONResponse(content={"message": "No modifications found"}, status_code=404)
            
            modifications_history = json.loads(modifications_history)
            comments = modifications_history[modification_index].get("comments", [])
            if comment_index < 0 or comment_index >= len(comments):
                return JSONResponse(content={"message": "Invalid comment index"}, status_code=400)
            
            del comments[comment_index]
            modifications_history[modification_index]["comments"] = comments
            await db.update_property_by_id(discussion_id, "modifications_history", json.dumps(modifications_history))
        
        return JSONResponse({ "message": "Deleted successfully" }, status_code=200)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get('/{node_id}/versions/{node_type}')
async def get_node_versions(
    node_id: int,
    project_id: int,
    node_type: str,
    db: NodeDB = Depends(get_node_db)
):
    try:
        version_manager = NodeVersionManager()
        versions = await version_manager.get_node_properties_versions(project_id,node_id,node_type)
        return versions
    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        logger.error(f"Exception in getting node versions: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/get-reconfig-status/{project_id}")
async def get_reconfig_status_endpoint(project_id: int):
    try:
        version_manager = NodeVersionManager()
        versions = await version_manager.get_affected_nodes_details(project_id)
        return versions
    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        logger.error(f"Exception in getting affected node details: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/get-reconfig-section-flag-status/{project_id}")
async def get_reconfig_section_flag_status_endpoint(project_id: int):
    try:
        version_manager = NodeVersionManager()
        versions = await version_manager.get_section_reconfig_flags(project_id)
        return versions
    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        logger.error(f"Exception in getting section reconfig flags: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get('/reconfig/{project_id}')
async def get_reconfig_status(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    """
    Retrieve the reconfig tracker document for a specific project.
    
    Args:
        project_id (int): The ID of the project to retrieve reconfig info for
        
    Returns:
        The reconfig tracker document without the _id field or appropriate error response
    """
    try:
        # Get MongoDB connection
        mongo_db = get_mongo_db()
        
        # Retrieve the reconfig tracker document
        reconfig_doc = await mongo_db.get_one(
            {"project_id": project_id},
            mongo_db.db["reconfig_tracker"]
        )
        
        # Check if document exists
        if not reconfig_doc:
            return Response(content='{}', media_type="application/json")
        
        # Remove the _id field from the document
        if '_id' in reconfig_doc:
            del reconfig_doc['_id']
        
        # Convert the MongoDB document to JSON string using bson.json_util
        json_data = dumps(reconfig_doc)
        
        # Return raw JSON response
        return Response(content=json_data, media_type="application/json")
        
    except Exception as e:
        logger.error(f"Error retrieving reconfig tracker: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving reconfig tracker: {str(e)}")
    




@router.post('/approve_reconfiguration/{project_id}')
async def approve_reconfiguration(
    project_id: int,
    current_task_id: str = Query(..., alias='task_id'),
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    """
    Approve the reconfiguration for a project.
    
    Args:
        project_id (int): The ID of the project to approve reconfiguration for
        
    Returns:
        Success message if the reconfiguration is approved
    """
    try:
        # Import Supervisor clas
        supervisor = Supervisor(
            name="approval_supervisor",
            agents={},
            architecture_level=0,
            user_level=0,
            start_config="",
            config_types=[]
        )
        
        # Set the project ID and ensure it's set as an integer
        supervisor.project_id = int(project_id)
        
        # Get MongoDB connection to check reconfig status
        mongo_db = get_mongo_db()
        reconfig_doc = await mongo_db.get_one(
            {"project_id": project_id},
            mongo_db.db["reconfig_tracker"]
        )
        
        if not reconfig_doc:
            raise HTTPException(status_code=404, detail="Reconfig tracker not found for this project")
        
        # Determine if this is the first config based on last_successful
        supervisor.is_first_config = False  # Set to False for approval (assuming we're approving an update)
        
        # Mark auto_config_completed as True to enable approval
        supervisor.auto_config_completed = True
        
        # Call the approve_reconfiguration method
        await supervisor.approve_reconfiguration()

        mongo_db = get_mongo_db(collection_name="tf_tasks")
        await mongo_db.update_one(filter={'task_id': current_task_id}, element={'approved_or_rejected': True}, db=mongo_db.db)
        
        # Return success response
        return JSONResponse(
            content={
                "message": "Reconfiguration approved successfully",
                "project_id": project_id
            },
            status_code=200
        )
    
        
    except Exception as e:
        logger.error(f"Error approving reconfiguration: {e}")
        raise HTTPException(status_code=500, detail=f"Error approving reconfiguration: {str(e)}")

@router.get("/export_test_cases/{project_id}")
async def export_test_cases(project_id: int, db: NodeDB = Depends(get_node_db)):
    """
    Export test cases for a project in Excel format.
    """
    try:
        # Get project details
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Get test cases
        test_cases = await db.get_test_cases(project_id)
        if not test_cases:
            raise HTTPException(status_code=404, detail="No test cases found for this project")
        
        # Prepare data for Excel
        test_case_data = []
        for tc in test_cases:
            properties = tc.get('properties', {})
            test_case_data.append({
                'Title': properties.get('Title', ''),
                'Description': properties.get('Description', ''),
                'Type': properties.get('Type', ''),
                'TestLevel': properties.get('TestLevel', ''),
                'Category': properties.get('Category', ''),
                'ExpectedResult': properties.get('ExpectedResult', ''),
                'CanBeAutomated': properties.get('CanBeAutomated', False),
                'Status': properties.get('Status', ''),
                'Priority': properties.get('Priority', ''),
                'CreatedAt': properties.get('CreatedAt', '')
            })
        
        # Create DataFrame and Excel file
        df = pd.DataFrame(test_case_data)
        
        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Test Cases')
        output.seek(0)
        
        # Prepare filename
        project_name = project_details.get('Title', 'project').replace(' ', '_')
        filename = f"{project_name}_test_cases.xlsx"
        
        # Return streaming response
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        
        return StreamingResponse(output, headers=headers)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/system-context/overview/{node_id}')
async def start_system_context_overview(
    node_id: int,
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    """Start a system context overview discussion focusing on users and external systems."""
    try:
        logger.info(
            "Starting system context overview discussion",
            extra={
                "node_id": node_id,
                "user_id": current_user.get('cognito:username'),
                "user_email": current_user.get('email')
            }
        )

        configuration = await DiscussionFactory.create_discussion(
            discussion_type='system_context_overview',
            node_type='SystemContext',
            node_id=node_id,
            current_user=current_user.get('cognito:username'),
            title=f'System Context Overview Discussion on {generate_timestamp()}'
        )
        
        if not configuration:
            raise HTTPException(status_code=400, detail='Invalid system context node')
            
        discussion_id = configuration.discussion_id
        await db.add_user_to_discussion(current_user.get('cognito:username'), discussion_id)
        steps = configuration.get_visible_steps()
        
        logger.info(
            "System context overview discussion started successfully",
            extra={
                "discussion_id": discussion_id,
                "node_id": node_id,
                "user_id": current_user.get('cognito:username')
            }
        )

        return {
            'success': True,
            'discussion_type': 'system_context_overview',
            'discussion_id': discussion_id,
            'steps': steps
        }
    except Exception as e:
        logger.error(
            "Error starting system context overview discussion",
            extra={
                "node_id": node_id,
                "user_id": current_user.get('cognito:username'),
                "error": str(e)
            }
        )
        raise

@router.post('/system-context/containers/{node_id}')
async def start_system_context_containers(
    node_id: int,
    current_user = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    """Start a system context containers discussion focusing on internal containers and interfaces."""
    try:
        logger.info(
            "Starting system context containers discussion",
            extra={
                "node_id": node_id,
                "user_id": current_user.get('cognito:username'),
                "user_email": current_user.get('email')
            }
        )

        configuration = await DiscussionFactory.create_discussion(
            discussion_type='system_context_containers',
            node_type='SystemContext',
            node_id=node_id,
            current_user=current_user.get('cognito:username'),
            title=f'System Context Containers Discussion on {generate_timestamp()}'
        )
        
        if not configuration:
            raise HTTPException(status_code=400, detail='Invalid system context node')
            
        discussion_id = configuration.discussion_id
        await db.add_user_to_discussion(current_user.get('cognito:username'), discussion_id)
        steps = configuration.get_visible_steps()
        
        logger.info(
            "System context containers discussion started successfully",
            extra={
                "discussion_id": discussion_id,
                "node_id": node_id,
                "user_id": current_user.get('cognito:username')
            }
        )

        return {
            'success': True,
            'discussion_type': 'system_context_containers',
            'discussion_id': discussion_id,
            'steps': steps
        }
    except Exception as e:
        logger.error(
            "Error starting system context containers discussion",
            extra={
                "node_id": node_id,
                "user_id": current_user.get('cognito:username'),
                "error": str(e)
            }
        )
        raise

@router.get('/system-context/discussions/{node_id}')
async def get_system_context_discussions(
    node_id: int,
    discussion_type: str = Query(None, description="Filter by discussion type: 'overview' or 'containers'"),
    status: str = Query(None, description="Filter by status"),
    db: NodeDB = Depends(get_node_db)
):
    """Get all system context discussions for a node, optionally filtered by type and status."""
    try:
        discussions = await db.get_discussions(node_id, status)
        
        if discussion_type:
            # Filter discussions by type if specified
            if discussion_type == 'overview':
                discussions = [d for d in discussions if d.get('properties', {}).get('discussion_type') == 'system_context_overview']
            elif discussion_type == 'containers':
                discussions = [d for d in discussions if d.get('properties', {}).get('discussion_type') == 'system_context_containers']
                
        return discussions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/export_requirements/{project_id}")
async def export_requirements(project_id: int, db: NodeDB = Depends(get_node_db)):
    """
    Export requirements for a project in Excel format.
    """
    try:
        # Get project details
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        if not project_details or not project_details.get("Type") or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Get requirements
        requirements = await db.get_requirements(project_id)
       
        # Prepare data for Excel
        requirement_data = []
        for req in requirements:
            # Comprehensive mapping: include all available columns
            requirement_data.append({
                # Project columns
                'ProjectName': req.get('ProjectName', ''),
                'RequirementRootName': req.get('RequirementRootName', ''),
                
                # Epic columns
                'EpicId': req.get('EpicId', ''),
                'Epic': req.get('Epic', ''),
                'EpicPriority': req.get('EpicPriority', ''),
                'EpicDescription': req.get('EpicDescription', ''),
                
                # UserStory columns
                'UserStoryId': req.get('UserStoryId', ''),
                'UserStory': req.get('UserStory', ''),
                'UserStoryPriority': req.get('UserStoryPriority', ''),
                'UserStoryDescription': req.get('UserStoryDescription', ''),
                
                # Test columns
                'TestCase': req.get('TestName', req.get('TestCase', '')),
                'TestName': req.get('TestName', ''),
                'TestPriority': req.get('TestPriority', ''),
                'TestCategory': req.get('TestCategory', ''),
                'TestDescription': req.get('TestDescription', ''),
                'TestAutomated': req.get('TestAutomated', ''),
                'TestType': req.get('TestType', ''),
                'TestLevel': req.get('TestLevel', ''),
                
                # Test details columns
                'AcceptanceCriteria': req.get('AcceptanceCriteria', ''),
                'ExpectedResults': req.get('ExpectedResults', ''),
                'Steps': req.get('Steps', ''),
                'PreConditions': req.get('PreConditions', ''),
                'Tags': req.get('Tags', ''),
            })
        
        if requirement_data == []:
            # Create empty template with all columns
            empty_template = {
                # Project columns
                'ProjectName': '', 'RequirementRootName': '',
                # Epic columns
                'EpicId': '', 'Epic': '', 'EpicPriority': '', 'EpicDescription': '',
                # UserStory columns
                'UserStoryId': '', 'UserStory': '', 'UserStoryPriority': '', 'UserStoryDescription': '',
                # Test columns
                'TestCase': '', 'TestName': '', 'TestPriority': '', 'TestCategory': '', 'TestDescription': '', 'TestAutomated': '',
                'TestType': '', 'TestLevel': '',
                # Test details columns
                'AcceptanceCriteria': '', 'ExpectedResults': '', 'Steps': '', 'PreConditions': '', 'Tags': '',
            }
            requirement_data = [empty_template]

        # Create DataFrame and Excel file
        df = pd.DataFrame(requirement_data)
        
        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Requirements')
        output.seek(0)
        
        # Prepare filename
        project_name = project_details.get('Title', 'project').replace(' ', '_')
        filename = f"{project_name}_requirements.xlsx"
        
        # Return streaming response
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        
        return StreamingResponse(output, headers=headers)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    
@router.post("/import_requirements/{project_id}")
async def import_requirements(
    project_id: int, 
    file: UploadFile = File(...),
    db: NodeDB = Depends(get_node_db)
):
    """
    Import requirements for a project from Excel format with duplicate prevention.
    """
    try:
        # Validate file type
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="File must be Excel format (.xlsx or .xls)")
        
        # Validate project exists
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Read Excel file
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        df.fillna("", inplace=True)
        
        # Updated required columns to match your Excel file structure
        required_columns = {
            'ProjectName', 'Epic', 'UserStory'
        }
        
        # Optional columns that may be present
        optional_columns = {
            # Epic columns
            'EpicId', 'EpicPriority', 'EpicDescription',
            # UserStory columns
            'UserStoryId', 'UserStoryPriority', 'UserStoryDescription',
            # Test columns
            'TestPriority', 'TestCategory', 'TestDescription', 'TestAutomated', 
            'AcceptanceCriteria', 'ExpectedResults', 'Steps', 'PreConditions', 'Tags', 'TestType',
            'TestLevel', 
            'TestName',
            # General columns
            'RequirementRootName', 'TestCase'
        }
        
        # Check for required columns
        if not required_columns.issubset(set(df.columns)):
            missing_cols = required_columns - set(df.columns)
            raise HTTPException(
                status_code=400, 
                detail=f"Missing required columns: {', '.join(missing_cols)}"
            )
        
        # Log available columns for debugging
        print(f"Available columns: {list(df.columns)}")
        print(f"Required columns found: {required_columns.intersection(set(df.columns))}")
        print(f"Optional columns found: {optional_columns.intersection(set(df.columns))}")
        
        # Convert DataFrame to records
        records = df.to_dict('records')
        
        # Process the import
        result = await db.import_requirements(project_id, records)
        
        return {
            "message": "Requirements imported successfully",
            "imported_count": result.get("imported_count", 0),
            "skipped_count": result.get("skipped_count", 0),
            "duplicate_count": result.get("duplicate_count", 0),
            "total_rows_processed": len(records)
        }
        
    except pd.errors.EmptyDataError:
        raise HTTPException(status_code=400, detail="Excel file is empty")
    except Exception as e:
        print(f"Import error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Import failed: {str(e)}")