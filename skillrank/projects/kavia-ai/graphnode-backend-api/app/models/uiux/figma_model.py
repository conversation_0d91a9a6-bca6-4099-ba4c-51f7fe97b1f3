# app/models/uiux/figma_model.py
from enum import Enum
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.connection.establish_db_connection import get_mongo_db
from app.utils.datetime_utils import generate_timestamp
class ProcessingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIALLY_COMPLETED = "partially_completed"
    PROCESSING_JSON = "processing_wait"

class UserModel(BaseModel):
    username: str
    name: str
    email: str

class FigmaSizesModel(BaseModel):
    size_kb: float
    size_mb: float
    byte_limit: Optional[int] = None
    mb_limit: Optional[int] = None

class FigmaRequestModel(BaseModel):
    name: str = Field(default="")
    url: str = Field(default="")

class FigmaFrameModel(BaseModel):
    """Model for individual Figma frame data"""
    id: str
    name: str
    status: ProcessingStatus = Field(default=ProcessingStatus.PENDING)
    imageUrl: Optional[str] = None
    error_message: Optional[str] = None
    time_updated: str = Field(default_factory=lambda: generate_timestamp())
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
        
    @validator('name')
    def validate_name(cls, v):
        """Ensure name is not empty"""
        if not v or not v.strip():
            return "Untitled Frame"
        return v.strip()
    
    @validator('metadata')
    def validate_metadata(cls, v):
        """Ensure metadata is a dict"""
        if v is None:
            return {}
        return v

class FigmaModel(BaseModel):
    id: str  # Will be constructed as f'{tenant_id}-{project_id}-{file_key}'
    project_id: str
    tenant_id: str
    file_key: str
    name: str = Field(default="")
    url: str = Field(default="")
    added_by: UserModel
    sizes: Optional[FigmaSizesModel] = None
    status: ProcessingStatus = Field(default=ProcessingStatus.PENDING)
    error_message: Optional[str] = None
    total_frames: int = Field(default=0)
    completed_frames: int = Field(default=0)
    failed_frames: int = Field(default=0)
    completed_frame_ids: List[str] = Field(default_factory=list)  # Track which specific frames are completed
    failed_frame_ids: List[str] = Field(default_factory=list)     # Track which specific frames failed
    time_created: str = Field(default_factory=lambda: generate_timestamp())
    time_updated: str = Field(default_factory=lambda: generate_timestamp())

    @validator('id', pre=True, always=True)
    def construct_id(cls, v, values):
        """Construct ID from tenant_id, project_id and file_key"""
        if 'tenant_id' in values and 'project_id' in values and 'file_key' in values:
            return f"{values['tenant_id']}-{values['project_id']}-{values['file_key']}"
        return v

    @classmethod
    def _get_collection(cls):
        """Get MongoDB collection for figma designs"""
        return get_mongo_db(collection_name="figma_designs")
    @classmethod
    def _get_collection_CGA(cls):
        """Get MongoDB collection for figma designs"""
        return get_mongo_db(collection_name="figma_designs_CGA")
    
    @classmethod
    async def create(cls, figma_data: dict) -> Optional[Dict[str, Any]]:
        """Create a new figma design document"""
        try:
            handler = cls._get_collection()
            
            # Convert Pydantic model to dict if needed
            if isinstance(figma_data, BaseModel):
                figma_data = figma_data.dict()
                
            # Ensure added_by is a dict
            if isinstance(figma_data.get('added_by'), UserModel):
                figma_data['added_by'] = figma_data['added_by'].dict()
            
            # Add timestamps if not present
            if 'time_created' not in figma_data:
                figma_data['time_created'] = generate_timestamp()
            if 'time_updated' not in figma_data:
                figma_data['time_updated'] = generate_timestamp()
            
            # Insert the document
            result = await handler.update_one(
                filter={"id": figma_data["id"]},
                element=figma_data,
                upsert=True,
                db=handler.db
            )
            
            # Return the created document
            return figma_data
            
        except Exception as e:
            print(f"Error creating figma design: {str(e)}")  # Debug print
            return None
    @classmethod
    async def create_CGA(cls, figma_data: dict) -> Optional[Dict[str, Any]]:
        """Create a new figma design document"""
        try:
            handler = cls._get_collection_CGA()
            
            # Convert Pydantic model to dict if needed
            if isinstance(figma_data, BaseModel):
                figma_data = figma_data.dict()
                
            # Ensure added_by is a dict
            if isinstance(figma_data.get('added_by'), UserModel):
                figma_data['added_by'] = figma_data['added_by'].dict()
            
            # Add timestamps if not present
            if 'time_created' not in figma_data:
                figma_data['time_created'] = generate_timestamp()
            if 'time_updated' not in figma_data:
                figma_data['time_updated'] = generate_timestamp()
            
            # Insert the document
            result = await handler.update_one(
                filter={"id": figma_data["id"]},
                element=figma_data,
                upsert=True,
                db=handler.db
            )
            
            # Return the created document
            return figma_data
            
        except Exception as e:
            print(f"Error creating figma design: {str(e)}")  # Debug print
            return None
    
    @classmethod
    async def get_one(cls, id: str) -> Optional[Dict[str, Any]]:
        """Get a figma design by ID"""
        try:
            handler = cls._get_collection()
            result = await handler.get_one({"id": id}, handler.db)
            return result
        except Exception as e:
            print(f"Error fetching figma design: {str(e)}")  # Debug print
            return None
    @classmethod
    async def get_one_CGA(cls, id: str) -> Optional[Dict[str, Any]]:
        """Get a figma design by ID"""
        try:
            handler = cls._get_collection_CGA()
            result = await handler.get_one({"id": id}, handler.db)
            return result
        except Exception as e:
            print(f"Error fetching figma design: {str(e)}")  # Debug print
            return None
    
    def _serialize_documents(cls, cursor):
        """Helper method to serialize MongoDB documents"""
        documents = []
        for doc in cursor:
            if '_id' in doc:
                doc['_id'] = str(doc['_id'])
            documents.append(doc)
        return documents

    @classmethod
    async def get_by_project(cls, project_id: str, include_images: bool = False) -> List[Dict[str, Any]]:
        """Get all figma designs for a project"""
        handler = cls._get_collection()
        to_return = {
            "designs":[],
            "images":[]
        }
        try:
            cursor = handler.db[handler.collection].find({"project_id": project_id})
            if include_images:
                projection = {
                    "images":0,
                    "_id":0
                }
                image_group = handler.db["figma_ext_images"].find({"project_id": project_id}, projection=projection)
                images = cls._serialize_documents(cls,image_group)
                for image in images:
                    to_return["images"].append(image)


            designs = cls._serialize_documents(cls,cursor)
            print(f"Designs: {designs}")
            for design in designs:
                to_return["designs"].append(design)

            if include_images:
                return to_return["designs"], to_return["images"]
            return to_return["designs"]
        except Exception as e:
            print(f"Error fetching figma designs: {str(e)}")
            return []

    @classmethod
    async def get_by_project_CGA(cls, project_id: str, include_images: bool = False) -> List[Dict[str, Any]]:
        """Get all figma designs for a project from CGA collection (includes processing info)"""
        print(f"get_by_project_CGA called with project_id: {project_id}, include_images: {include_images}")
        handler = cls._get_collection_CGA()
        to_return = {
            "designs":[],
            "images":[]
        }
        try:
            cursor = handler.db[handler.collection].find({"project_id": project_id})
            if include_images:
                projection = {
                    "_id":0
                }
                image_group = handler.db["figma_ext_images"].find({"project_id": project_id}, projection)
                images = cls._serialize_documents(cls,image_group)
                print(f"CGA Images found: {len(images)}")
                for image in images:
                    to_return["images"].append(image)


            designs = cls._serialize_documents(cls,cursor)
            print(f"CGA Designs found: {len(designs)} - {designs}")
            for design in designs:
                to_return["designs"].append(design)

            if include_images:
                print(f"Returning designs: {len(to_return['designs'])}, images: {len(to_return['images'])}")
                return to_return["designs"], to_return["images"]
            return to_return["designs"]
        except Exception as e:
            print(f"Error fetching CGA figma designs: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    @classmethod
    async def update(cls, id: str, update_data: dict):
        """Update a figma design"""
        try:
            handler = cls._get_collection()
            # Add time_updated to the update data
            update_data["time_updated"] = generate_timestamp()
            result = await handler.update_one({"id": id}, update_data, db=handler.db)
            if result and hasattr(result, 'modified_count') and result.modified_count > 0:
                return await cls.get_one(id)
            return None
        except Exception as e:
            print(f"Error updating figma design: {str(e)}")  # Debug print
            return None
    
    @classmethod
    async def update_CGA(cls, id: str, update_data: dict):
        """Update a figma design"""
        try:
            handler = cls._get_collection_CGA()
            # Add time_updated to the update data
            update_data["time_updated"] = generate_timestamp()
            result = await handler.update_one({"id": id}, update_data, db=handler.db)
            if result and hasattr(result, 'modified_count') and result.modified_count > 0:
                return await cls.get_one(id)
            return None
        except Exception as e:
            print(f"Error updating figma design: {str(e)}")  # Debug print
            return None
    
    @classmethod
    async def delete(cls, id: str):
        """Delete a figma design"""
        try:
            handler = cls._get_collection()
            result = await handler.delete_by_filter({"id": id}, handler.db)
            return result and hasattr(result, 'deleted_count') and result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting figma design: {str(e)}")  # Debug print
            return False