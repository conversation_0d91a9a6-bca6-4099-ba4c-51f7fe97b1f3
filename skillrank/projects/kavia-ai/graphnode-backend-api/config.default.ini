# file: config.default.ini
# Default configurations for the code generation agen.
# DO NOT EDIT THIS FILE DIRECTLY TO CHANGE CONFIG VALUES
# Crete a config.ini that overloads the configs defined in this file

# Example usage:
#
# from code_generation_core_agent.config import config
# max_tool_calls = config.get('LLM', 'max_tool_calls')

# System configuration
[SYSTEM]

# name: enable_shelly
# type: bool
# default: True
# Description:
#  Enable shelly shell for AI error handling
enable_shelly=False

# name: workspace_dir
# type: string
# default: /tmp/kavia
# Description:
#  Workspace directory for the agent. This is where the agent will store all the
#  files and data it needs to operate. The directory should be writable by the
# agent process. The agent may delete files in this directory.
#
# !!!WARNING!!!
# DO NOT SET THIS TO A DIRECTORY THAT CONTAINS IMPORTANT DATA
# CHANGE THIS VALUE ONLY IN config.ini FILE.

workspace_dir=/home/

# name: log_dir
# type: string
# default is: empty
# Description:
# Log directory for the agent. This is where the agent will be stored
# When empty, the logs will be stored in the workspace_dir
#  For deployment this should be non-empty and different from /tmp or any /tmp sub-directory
config_dir=

# name: default_docker_image
# type: string
# default: kavia_default_container_image
# Description:
#  Default docker image to use for running tools
default_docker_image=kavia_default_container_image

# name: debug_docker_executor
# type: bool
# default: False
# Description:
#  Enable debug mode for Docker executor operations
debug_docker_executor=False


# name: universal_init_wait_for_fs
# type: bool
# default: False
# Description:
#  Wait for the file system to be ready before starting the setup
universal_init_wait_for_fs=True

# LLM configuration
[LLM]
# name: model
# type: string
# default: gpt-4o-mini
# Description: 
#   Model name
# NOTE:
#  In deployment set this to: 
#     claude-3-5-sonnet-20241022
model=gpt-4.1

# name: enable_cache
# type: bool
# default: True
# Description:
#  Enable caching of System prompts and first user prompt
enable_cache=True

# List of model names for which caching is allowed
# This is a comma separated list of model names
# The model names should be the same as the ones defined in the [MODELS] section
cache_allowed_models=claude-3-5-sonnet-20241022,claude-sonnet-4-20250514

# name: backup_model
# type: string
# default: claude-3-5-sonnet-20241022
# Description:
#  Backup model name. This model is used when the main model is failing
# after multiple retries.

backup_model=claude-3-5-sonnet-20241022


# name: summarization_model
# type: string
# default: gpt-4o-mini
# Description:
#  Summarization model name
#  This model is used for summarizing the tool responses
#  The model is different from the main model to allow cost optimization
summarization_model=gpt-4.1-nano

# name: orchestrator_model
# type: string
# default: gpt-4o-mini
# Description:
#  Orchestrator model name
#  This model is used for by the orchestrator agent to manage the other agents
#  The model is different from the micro agent models to allow different latency

orchestrator_model=gpt-4.1

# name: initial_orchestrator_model
# type: string
# default: gpt-4o-mini
# Description:
#  Orchestrator model name
#  This model is used for by the orchestrator agent to display the welcome message
#  The model is different from the micro agent models to allow different latency

initial_orchestrator_model=gpt-4.1-nano

# name: max_tool_calls
# type: int
# default: 25
# Description:
#  Maximum number of tool calls by a single agent before giving up.
max_tool_calls=50

# name: max_preserved_tool_calls
# type: int
# default: 12
# Description:
#  Maximum number of tool calls to be preserved in the agent's memory.
#  This includes both calls and responses.
preserved_tool_calls=12

# name: max_tool_response_size
# type: int
# default: 10000
# Description:
#  Maximum number of bytes in the response from a tool.
# NOTE: placeholder, not in use yet
max_tool_response_size=10000

# name: strict_mode_enabled
# type: bool
# default: True
# Description:
#  Enable strict mode for llm function calls.
#  In this mode all tool call signatures are strongly enforced and validated.
#  If the function doesn't strongly enforces the strict mode requirements the
#  function call registration will fail with the LLM
strict_mode_enabled=False

# name debug
# type: bool
# default: False
# Description:
#  Enable debug mode
debug=False

# name preload_files
# type: bool
# default: True
# Description:
#  Preload relevant files in the prompt
preload_files=False

# name: llm_wrapper_summarization_token_threshold
# type: int
# default: 125000
# Description:
#  Token threshold for summarization of llm wrapper responses
# We assume 3 characters per token

llm_wrapper_summarization_token_threshold=150000


# name: auto_prompt_caching_chunk_size
# type: int
# default: 10000 (bytes)
# Description:
#  Chunk size for auto prompt caching. When we reach this size
# a new ephemeral cache message will be added to the prompt.
auto_prompt_caching_chunk_size=10000


# name: cost_warning_limit
# type: float
# default: 9 (float, USD)
# Description:
# Limit for the user receiving a warning
cost_warning_limit=9.0

# name: cost_error_limit
# type: float
# default: 10 (float, USD)
# Description:
# Limit for the user receiving a session too big error
cost_error_limit=10.0

[MODELS]
claude_anthropic_3_5 = claude-3-5-sonnet-20241022 | Claude 3.5
claude_sonnet_4_0 = claude-sonnet-4-20250514 | Claude 4.0
gpt4_1 = gpt-4.1 | GPT-4.1
Azure_4.1 = azure/gpt-4.1 | Azure GPT-4.1
o3 = o3 | O3
Gemini_2.5_pro = vertex_ai/gemini-2.5-pro-preview-05-06 | Gemini-2.5-pro
backup = claude-3-5-sonnet-20241022  | Claude 3.5

[LLM_MODEL_MAPPING]
# Mapping of agent names to LLM models. If defined, the agent will use the specified model
# If not defined, the agent will use the model defined in the [LLM] section
#
# Valid agent names:
#
# InitialSetupAgent
# CodeAnalysisAgent
# CodeWritingAgent
# TestCodeWritingAgent
# TestCaseCreationAgent
# TestDataGenerationAgent
# TestExecutionAgent
# VisualVerificationAgent
# SearchAgent
#
# Example:
#     SearchAgent=gpt-4o-mini

#InitialSetupAgent=gpt-4o-mini

# VisualVerificationAgent=gpt-4o-mini

[TOOLS]
# name: shell_command_timeout
# type: int
# default: 60
# Description:
#  Timeout for shell commands in seconds
shell_command_timeout=120

# name: return_size_limit
# type: int
# default: 2000
# Description:
#  Maximum number of bytes in the function call response
return_size_limit=10000

# name: browser_screenshot_width
# type: int
# default: 120
# Description:
#  Width of the browser screenshot
browser_screenshot_width=400

# name:browser_viewport
# type: string
# default: 1200x2400 (width x height)
# Description:
#   Browser viewport size
browser_viewport=1200x1600

# name: browser_reset_timeout
# type: int
# default: 30
# Description:
#   Timeout for web browser tool operations in seconds to avoid browser hang or stuck
browser_reset_timeout=30

# name: enable_git_tool
# type: bool
# default: false
# Description:
#  Enable Git tool functionality
enable_git_tool=false

# name: use_reasoning
# type: bool
# default: false
# Description:
#  Use a reasoning model to review plans
use_reasoning=false

# namee: auto_ci
# type: bool
# default: True
# Description:
#  Enable auto CI after writes and edits
auto_ci=False

# name: android_emulator_service
# type: string
# default: appetize
# Description:
#  Android emulator service to use
android_emulator_service=appetize
android_emulator_service_url=https://api.appetize.io/v1/apps

# name: enable_visual_edit
# type: bool
# default: true
# Description:
#  Enable visual edit in app preview
enable_visual_edit=true

[PROMPTS]

# name task_manager_prompt
# type: string
# Description: name of the j2 file to use for the task manager prompt
task_manager_prompt=orchestrator_taskmanager.j2

# name: emit_imporant_files
# type: bool
# default: True
# Description:
#  Include a list of important files in the base prompt
emit_important_files=true

# name: important_files_threshold
# type; int
# default: 20
# Description:
#  Max number of items to include in the important files list
important_files_threshold=200

# name: directory_size_threshold
# type: int
# default: 200*1024
# Description:
#  Repository size threshold for including files in the prompt
directory_size_threshold= 153600

# name: emit_important_commands
# type: bool
# default: True
# Description:
#  Include a list of important commands in the base prompt
emit_important_commands=true

# name: streaming_support
# type: bool
# default: False
# Description:
#  Enable streaming support for the prompt
streaming_support=True


[LOGGING]

# name: enable_telemetry
# type: bool
# default: True
# Description:
#  Enable telemetry

enable_telemetry=true

# name telemetry_implementation
# type: string
# default: datadog
# Description:
#  Telemetry implementation

telemetry_implementation=datadog


# name: datadog_api_key
# type: string
# default:
# Description:
#  Datadog API key
datadog_api_key=2fc68e65c44b27ae24b2d33444ec3f96


# name: datadog_app_key
# type: string
# default:
# Description:
#  Datadog APP key
datadog_app_key=****************************************


# name: datadog_server
# type: string
# default: us5.datadoghq.com
# Description:
#  Datadog server
datadog_server=us5.datadoghq.com

[KNOWLEDGE]
# name: knowledge_ingest_threads
# type: int
# default: 7
# Description:
#  Number of knowledge ingest threads
knowledge_ingest_threads=7

# name: semantic_search_enabled
# type: bool
# default: False
# Description:
#  Enable or disable knowledge embeddings
semantic_search_enabled=true

# name: semantic_search_top_k
# type: int
# default: 10
# Description:
#  Number of results to return when performing semantic search
#  on code knowledge base. Higher values return more results
#  but may include less relevant matches.
semantic_search_top_k=10

# name: semantic_search_radius
# type: float
# default: 0.8
# Description:
#  The radius parameter for vector similarity search in Milvus.
#  Controls the search scope - larger values are more strict,
#  smaller values allow more distant matches.
semantic_search_radius=0.8

# name: semantic_search_range_filter
# type: float
# default: 0.95
# Description:
#  The range filter is used in conjunction with the radius 
#  parameter to define a specific similarity range for the 
#  search results. Results with similarity scores lower than 
#  radius or greater than range_filter are filtered out.
#  Range filter parameter must be larger than radius.
semantic_search_range_filter=0.95

# name: semantic_search_conn_timeout
# type: int
# default: 60
# Description:
#  Connection timeout in seconds for Milvus operations
semantic_search_conn_timeout=60

# name: semantic_search_embedding_dim
# type: int
# default: 1536
# Description:
#  The dimension of the embedding vectors. This value depends on the model used.
#  For OpenAI's text-embedding-ada-002, this is 1536.
semantic_search_embedding_dim=1536

# name: semantic_search_model
# type: string
# default: text-embedding-ada-002
# Description:
#  The model to use for generating embeddings in semantic search.
#  Currently supports OpenAI's text-embedding-ada-002
semantic_search_model=text-embedding-ada-002

# name: ingestible_filetypes
# type: list of strings
# Description:
#  List of filetypes that are considered ingestible by the knowledge system
ingestible_filetypes = 
    # Web Development and Frontend
    .js, .jsx, .ts, .tsx, .html, .htm, .xml, .xhtml,
    .css, .scss, .less,
    
    # Data and Configuration
    .json, .yaml, .yml,

[CONTAINER]
# name: ports
# type: string
# Format: '{"port/tcp": port, "port/tcp": port, ...}'
# default: '{"3000/tcp": 3000, "8088/tcp": 8088, "8089/tcp": 8089, "5900/tcp": 5900, "5002/tcp": 5001}'
# Description:
#  Ports to expose in the container

ports = {"3000/tcp": 3000, "3001/tcp": 3001,
        "3002/tcp": 3002, "3003/tcp": 3003, "3004/tcp": 3004,
        "5432/tcp": 5432, "8088/tcp": 8088, "8089/tcp": 8089, "5900/tcp": 5900, "5002/tcp": 5001, "3005/tcp": 3005}

# name: default_backend_port
# type: int
# default: 3001
# Description:
#  Default backend port for the container
# Valid values are: 3001, 3002, 3003, 3004, 3005
default_backend_port = 3001

# name: default_frontend_port
# type: int
# default: 3000
# Description:
#  Default frontend port for the container
# Valid values are: 3000, 3005, 3006, 3007, 3008, 3009
default_frontend_port = 3000

# name: default_mobile_port
# type: int
# default: 8080
# Description:
# Default port exposing the mobile application
default_mobile_port = 8080

# name: default_database_port
# type: int
# default: 3002
# Description: Default database preview port
default_database_port = 3002

# name: container_name_arm64
# type: string
# default: kavia_default_container_image
# Description:
#  Container name for arm64 architecture

container_name_arm64=zoltankavia/kavia_base_dev_image

# name: container_name_x86_64
# type: string
# default: kavia_default_container_image
# Description:
#  Container name for x86_64 architecture
container_name_x86_64=zoltankavia/flutter_dev_image
